"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { TypographyH2, TypographyP } from "@/components/ui/typography";
import { 
  Building, 
  Calendar, 
  Users, 
  Download, 
  Shuffle, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  Filter,
  Search,
  Settings,
  FileSpreadsheet,
  Eye,
  RotateCcw
} from "lucide-react";
import { 
  Exam, 
  AcademicSession, 
  Semester, 
  HallAllocation,
  AllocationSettings
} from "@/types/exam";
import { 
  getAllAcademicSessions,
  getSemestersBySession,
  getExamsBySession,
  ensureSemestersExist
} from "@/lib/firebase/exam-service";
import {
  getHallAllocationsByExam,
  generateHallAllocation,
  saveHallAllocations,
  updateExamAllocationStatus,
  getStudentsForExam,
  validateAllocationCapacity
} from "@/lib/firebase/hall-allocation-service";
import { HallAllocationResults } from "@/components/admin/hall-allocation-results";

type ViewMode = 'selection' | 'allocation' | 'results';

interface AllocationResult {
  examId: string;
  examCode: string;
  examTitle: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  allocations?: HallAllocation[];
  error?: string;
  studentCount?: number;
  allocatedCount?: number;
}

export default function HallAllocationPage() {
  const [sessions, setSessions] = useState<AcademicSession[]>([]);
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [exams, setExams] = useState<Exam[]>([]);
  const [selectedSessionId, setSelectedSessionId] = useState("");
  const [selectedSemesterId, setSelectedSemesterId] = useState("all");
  const [selectedExamIds, setSelectedExamIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  
  const [viewMode, setViewMode] = useState<ViewMode>('selection');
  const [allocationResults, setAllocationResults] = useState<AllocationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState("");

  // Cancellation and timeout handling
  const [isCancelling, setIsCancelling] = useState(false);
  const [currentProcessingExam, setCurrentProcessingExam] = useState<string>("");
  const [allocationTimeouts, setAllocationTimeouts] = useState<Map<string, NodeJS.Timeout>>(new Map());

  const [allocationSettings, setAllocationSettings] = useState<AllocationSettings>({
    prioritizeMixedDepartments: true,
    groupByStudyMode: true,
    randomHallSelection: true,
    allowPartialAllocation: false
  });

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load semesters when session changes
  useEffect(() => {
    if (selectedSessionId) {
      loadSemesters(selectedSessionId);
    } else {
      setSemesters([]);
      setSelectedSemesterId("all");
    }
  }, [selectedSessionId]); // loadSemesters is stable, no need to include

  // Load exams when session or semester changes
  useEffect(() => {
    if (selectedSessionId) {
      loadExams(selectedSessionId, selectedSemesterId);
    } else {
      setExams([]);
    }
  }, [selectedSessionId, selectedSemesterId]);

  const loadInitialData = async () => {
    try {
      setIsLoadingData(true);
      setError("");

      const sessionsData = await getAllAcademicSessions();
      setSessions(sessionsData);

      console.log(`✅ Loaded ${sessionsData.length} academic sessions`);
    } catch (error) {
      console.error("Error loading initial data:", error);
      setError("Failed to load academic sessions");
    } finally {
      setIsLoadingData(false);
    }
  };

  const loadSemesters = async (sessionId: string) => {
    try {
      await ensureSemestersExist(sessionId, "admin-user-id");
      const semestersData = await getSemestersBySession(sessionId);
      setSemesters(semestersData);
      
      // Auto-select first semester if no specific selection
      if (semestersData.length > 0 && selectedSemesterId === "all") {
        // Keep "all" as default to show all semesters
        // setSelectedSemesterId(semestersData[0].id);
      }

      console.log(`✅ Loaded ${semestersData.length} semesters`);
    } catch (error) {
      console.error("Error loading semesters:", error);
      setError("Failed to load semesters");
    }
  };

  const loadExams = async (sessionId: string, semesterId?: string) => {
    try {
      // Convert "all" to undefined to load all semesters
      const actualSemesterId = semesterId === "all" ? undefined : semesterId;
      const examsData = await getExamsBySession(sessionId, actualSemesterId);
      setExams(examsData);
      setSelectedExamIds([]);

      console.log(`✅ Loaded ${examsData.length} exams`);
    } catch (error) {
      console.error("Error loading exams:", error);
      setError("Failed to load exams");
    }
  };

  // Filter exams based on search and filters
  const filteredExams = exams.filter(exam => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      if (!exam.courseCode.toLowerCase().includes(searchLower) &&
          !exam.courseTitle.toLowerCase().includes(searchLower) &&
          !exam.departmentName.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Department filter
    if (departmentFilter !== "all" && exam.departmentId !== departmentFilter) {
      return false;
    }

    // Status filter
    if (statusFilter === "allocated" && !exam.isAllocated) return false;
    if (statusFilter === "unallocated" && exam.isAllocated) return false;
    if (statusFilter === "scheduled" && exam.status !== "scheduled") return false;

    return true;
  });

  // Get unique departments for filter
  const departments = Array.from(new Set(exams.map(exam => ({
    id: exam.departmentId,
    name: exam.departmentName
  }))));

  const toggleExamSelection = (examId: string) => {
    setSelectedExamIds(prev => 
      prev.includes(examId) 
        ? prev.filter(id => id !== examId)
        : [...prev, examId]
    );
  };

  const selectAllExams = () => {
    console.log('🔄 Select All button clicked');
    console.log('📊 Filtered exams count:', filteredExams.length);

    // Use the same logic as the individual exam selection
    const selectableExams = filteredExams.filter(exam => {
      // Must not be already allocated
      if (exam.isAllocated) return false;

      // Must have valid time slots
      if (!exam.startTime || !exam.endTime) return false;

      // Must not be cancelled or completed
      if (exam.status === 'cancelled' || exam.status === 'completed') return false;

      // Must have a valid exam date (not in the past)
      const examDate = new Date(exam.examDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to compare dates only
      if (examDate < today) return false;

      return true;
    });

    console.log('✅ Selectable exams count:', selectableExams.length);
    console.log('📋 Selectable exam IDs:', selectableExams.map(e => e.courseCode));

    const examIds = selectableExams.map(exam => exam.id);
    setSelectedExamIds(examIds);

    console.log('🎯 Setting selected exam IDs:', examIds);
  };

  const deselectAllExams = () => {
    console.log('🔄 Deselect All button clicked');
    console.log('📊 Current selected count:', selectedExamIds.length);

    setSelectedExamIds([]);

    console.log('✅ Cleared all selections');
  };

  // Cancellation function
  const cancelAllocation = () => {
    console.log('🛑 User requested cancellation');
    setIsCancelling(true);

    // Clear any pending timeouts
    allocationTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    setAllocationTimeouts(new Map());

    // Update remaining pending/processing exams to cancelled
    setAllocationResults(prev => prev.map(result =>
      result.status === 'pending' || result.status === 'processing'
        ? { ...result, status: 'error', error: 'Cancelled by user' }
        : result
    ));

    setIsLoading(false);
    setIsCancelling(false);
    setCurrentProcessingExam("");
    setViewMode('results');
  };

  // Allocation with timeout and cancellation support
  const allocateExamWithTimeout = async (exam: Exam, timeoutMs: number = 30000): Promise<void> => {
    return new Promise((resolve, reject) => {
      let isResolved = false;

      // Set up timeout
      const timeoutId = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          reject(new Error(`Allocation timeout after ${timeoutMs/1000} seconds. This exam may have complex requirements or insufficient available halls.`));
        }
      }, timeoutMs);

      // Store timeout for potential cancellation
      setAllocationTimeouts(prev => new Map(prev.set(exam.id, timeoutId)));

      // Perform the actual allocation
      const performAllocation = async () => {
        try {
          // Get students first to know how many we need to accommodate
          const students = await getStudentsForExam(exam.levelId, exam.departmentId, exam.courseId);

          if (students.length === 0) {
            throw new Error(`No students found for this exam. Please check the course registration.`);
          }

          console.log(`👥 Found ${students.length} students for ${exam.courseCode}`);

          // Determine which halls to check for availability
          const hallsToCheck = exam.selectedHalls || [];

          // If no halls are pre-selected, check all active halls
          if (hallsToCheck.length === 0) {
            console.log(`📋 No halls pre-selected for ${exam.courseCode}, checking all active halls`);
          }

          // CRITICAL: Validate capacity BEFORE attempting allocation
          const capacityValidation = await validateAllocationCapacity(
            exam.examDate,
            exam.startTime,
            exam.endTime,
            students.length,
            hallsToCheck.length > 0 ? hallsToCheck : undefined
          );

          if (!capacityValidation.isValid) {
            // Provide detailed error with suggestions
            const availableHallNames = capacityValidation.availableHalls.map(h => h.name).join(', ');
            const shortage = capacityValidation.requiredCapacity - capacityValidation.availableCapacity;

            throw new Error(
              `Insufficient hall capacity for ${exam.courseCode}:\n` +
              `• Students: ${capacityValidation.requiredCapacity}\n` +
              `• Available capacity: ${capacityValidation.availableCapacity}\n` +
              `• Shortage: ${shortage} seats\n` +
              `• Available halls: ${availableHallNames || 'None'}\n\n` +
              `Suggestions:\n` +
              `• Choose a different time slot\n` +
              `• Select additional halls\n` +
              `• Split the exam into multiple sessions`
            );
          }

          console.log(`✅ Capacity validation passed: ${capacityValidation.availableCapacity} seats available for ${students.length} students`);

          // Use the validated available halls
          const halls = capacityValidation.availableHalls;

          // Generate allocation
          const allocations = await generateHallAllocation(
            exam.id,
            students,
            halls,
            exam.hallCapacityOverride,
            allocationSettings
          );

          // Save allocations
          const savedAllocations = await saveHallAllocations(allocations);

          // Update exam status
          await updateExamAllocationStatus(exam.id, true);

          const allocatedCount = savedAllocations.reduce((sum, alloc) => sum + alloc.allocatedCapacity, 0);

          if (!isResolved) {
            isResolved = true;
            clearTimeout(timeoutId);
            setAllocationTimeouts(prev => {
              const newMap = new Map(prev);
              newMap.delete(exam.id);
              return newMap;
            });

            // Update result to completed
            setAllocationResults(prev => prev.map(result =>
              result.examId === exam.id
                ? {
                    ...result,
                    status: 'completed',
                    allocations: savedAllocations,
                    studentCount: students.length,
                    allocatedCount
                  }
                : result
            ));

            resolve();
          }
        } catch (error) {
          if (!isResolved) {
            isResolved = true;
            clearTimeout(timeoutId);
            setAllocationTimeouts(prev => {
              const newMap = new Map(prev);
              newMap.delete(exam.id);
              return newMap;
            });
            reject(error);
          }
        }
      };

      performAllocation();
    });
  };

  const startAllocation = async () => {
    if (selectedExamIds.length === 0) {
      setError("Please select at least one exam to allocate");
      return;
    }

    setIsLoading(true);
    setIsCancelling(false);
    setError("");
    setViewMode('allocation');

    const selectedExams = exams.filter(exam => selectedExamIds.includes(exam.id));
    const results: AllocationResult[] = selectedExams.map(exam => ({
      examId: exam.id,
      examCode: exam.courseCode,
      examTitle: exam.courseTitle,
      status: 'pending'
    }));

    setAllocationResults(results);

    // Process each exam with cancellation support
    for (let i = 0; i < selectedExams.length; i++) {
      // Check for cancellation
      if (isCancelling) {
        console.log('🛑 Allocation cancelled by user');
        break;
      }

      const exam = selectedExams[i];
      setCurrentProcessingExam(`${exam.courseCode} - ${exam.courseTitle}`);
      
      try {
        // Update status to processing
        setAllocationResults(prev => prev.map(result => 
          result.examId === exam.id 
            ? { ...result, status: 'processing' }
            : result
        ));

        // Get students first to know how many we need to accommodate
        const students = await getStudentsForExam(exam.levelId, exam.departmentId, exam.courseId);

        if (students.length === 0) {
          throw new Error(`No students found for this exam. Please check the course registration.`);
        }

        console.log(`👥 Found ${students.length} students for ${exam.courseCode}`);

        // Determine which halls to check for availability
        const hallsToCheck = exam.selectedHalls || [];

        // If no halls are pre-selected, check all active halls
        if (hallsToCheck.length === 0) {
          console.log(`📋 No halls pre-selected for ${exam.courseCode}, checking all active halls`);
        }

        // CRITICAL: Validate capacity BEFORE attempting allocation
        const capacityValidation = await validateAllocationCapacity(
          exam.examDate,
          exam.startTime,
          exam.endTime,
          students.length,
          hallsToCheck.length > 0 ? hallsToCheck : undefined
        );

        if (!capacityValidation.isValid) {
          // Provide detailed error with suggestions
          const availableHallNames = capacityValidation.availableHalls.map(h => h.name).join(', ');
          const shortage = capacityValidation.requiredCapacity - capacityValidation.availableCapacity;

          throw new Error(
            `Insufficient hall capacity for ${exam.courseCode}:\n` +
            `• Students: ${capacityValidation.requiredCapacity}\n` +
            `• Available capacity: ${capacityValidation.availableCapacity}\n` +
            `• Shortage: ${shortage} seats\n` +
            `• Available halls: ${availableHallNames || 'None'}\n\n` +
            `Suggestions:\n` +
            `• Choose a different time slot\n` +
            `• Select additional halls\n` +
            `• Split the exam into multiple sessions`
          );
        }

        console.log(`✅ Capacity validation passed: ${capacityValidation.availableCapacity} seats available for ${students.length} students`);

        // Use the validated available halls
        const halls = capacityValidation.availableHalls;

        if (halls.length === 0) {
          throw new Error("No halls available for allocation");
        }

        // Use the timeout-enabled allocation function
        await allocateExamWithTimeout(exam, 45000); // 45 second timeout

      } catch (error) {
        console.error(`Error allocating halls for ${exam.courseCode}:`, error);

        // Update result to error
        setAllocationResults(prev => prev.map(result =>
          result.examId === exam.id
            ? {
                ...result,
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : result
        ));
      }

      // Small delay between allocations (only if not cancelled)
      if (!isCancelling) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setIsLoading(false);
    setViewMode('results');

    // Reload exams to update allocation status
    if (selectedSessionId) {
      await loadExams(selectedSessionId, selectedSemesterId);
    }
  };

  const resetAllocation = () => {
    setViewMode('selection');
    setAllocationResults([]);
    setSelectedExamIds([]);
    setError("");
  };

  const selectedSession = sessions.find(s => s.id === selectedSessionId);
  const selectedSemester = semesters.find(s => s.id === selectedSemesterId);

  // More flexible exam selection logic
  const selectableExams = filteredExams.filter(exam => {
    // Must not be already allocated
    if (exam.isAllocated) return false;

    // Must have valid time slots
    if (!exam.startTime || !exam.endTime) return false;

    // Must not be cancelled or completed
    if (exam.status === 'cancelled' || exam.status === 'completed') return false;

    // Must have a valid exam date (not in the past)
    const examDate = new Date(exam.examDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to compare dates only
    if (examDate < today) return false;

    return true;
  });

  // Debug logging for selectable exams
  console.log('📊 Component render - Filtered exams:', filteredExams.length);
  console.log('✅ Component render - Selectable exams:', selectableExams.length);
  console.log('🎯 Component render - Selected exam IDs:', selectedExamIds.length);

  if (isLoadingData) {
    return (
      <AdminLayout
        title="Hall Allocation Management"
        description="Manage hall allocations for examination sessions"
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading hall allocation data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Hall Allocation Management"
      description="Manage hall allocations for examination sessions"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <TypographyH2 className="flex items-center gap-2">
            <Building className="h-6 w-6" />
            Hall Allocation Management
          </TypographyH2>
          <TypographyP className="text-muted-foreground">
            Allocate examination halls by session and manage allocations
          </TypographyP>
        </div>
        {viewMode === 'results' && (
          <Button onClick={resetAllocation} variant="outline">
            <RotateCcw className="mr-2 h-4 w-4" />
            New Allocation
          </Button>
        )}
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium">Error</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
        </div>
      )}

      {viewMode === 'selection' && (
        <>
          {/* Session and Semester Selection */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Select Academic Session
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Academic Session</Label>
                  <Select value={selectedSessionId} onValueChange={setSelectedSessionId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select academic session" />
                    </SelectTrigger>
                    <SelectContent>
                      {sessions.map((session) => (
                        <SelectItem key={session.id} value={session.id}>
                          {session.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Semester</Label>
                  <Select
                    value={selectedSemesterId}
                    onValueChange={setSelectedSemesterId}
                    disabled={!selectedSessionId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !selectedSessionId ? "Select session first" : "Select semester"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Semesters</SelectItem>
                      {semesters.map((semester) => (
                        <SelectItem key={semester.id} value={semester.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {semester.code}
                            </Badge>
                            <span>{semester.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {selectedSession && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="text-sm">
                    <strong>Selected:</strong> {selectedSession.name}
                    {selectedSemester && ` - ${selectedSemester.name}`}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {selectedSession.startDate.toLocaleDateString()} - {selectedSession.endDate.toLocaleDateString()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Exam Selection */}
          {selectedSessionId && (
            <Card className="mb-6">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Select Exams for Allocation ({exams.length} total)
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('🔘 Select All button clicked!');
                        selectAllExams();
                      }}
                      disabled={selectableExams.length === 0}
                    >
                      Select All ({selectableExams.length})
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('🔘 Deselect All button clicked!');
                        deselectAllExams();
                      }}
                      disabled={selectedExamIds.length === 0}
                    >
                      Deselect All
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="search">Search Exams</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="Search by course code, title, or department"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Department</Label>
                    <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Departments</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                        <SelectItem value="allocated">Allocated</SelectItem>
                        <SelectItem value="unallocated">Unallocated</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Exam List */}
                {filteredExams.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No exams found for the selected criteria</p>
                    {exams.length === 0 && (
                      <p className="text-sm mt-2">
                        Make sure exams are scheduled for this session
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredExams.map((exam) => {
                      const isSelected = selectedExamIds.includes(exam.id);

                      // Use the same logic as selectableExams
                      const canSelect = (() => {
                        // Must not be already allocated
                        if (exam.isAllocated) return false;

                        // Must have valid time slots
                        if (!exam.startTime || !exam.endTime) return false;

                        // Must not be cancelled or completed
                        if (exam.status === 'cancelled' || exam.status === 'completed') return false;

                        // Must have a valid exam date (not in the past)
                        const examDate = new Date(exam.examDate);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0); // Reset time to compare dates only
                        if (examDate < today) return false;

                        return true;
                      })();

                      return (
                        <div
                          key={exam.id}
                          className={`flex items-center justify-between p-4 border rounded-lg ${
                            canSelect ? 'hover:bg-muted/50' : 'opacity-60'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={() => toggleExamSelection(exam.id)}
                              disabled={!canSelect}
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="outline" className="font-mono text-xs">
                                  {exam.courseCode}
                                </Badge>
                                <span className="font-medium">{exam.courseTitle}</span>
                                <Badge className={
                                  exam.isAllocated ? "bg-green-100 text-green-800" :
                                  exam.status === 'scheduled' ? "bg-blue-100 text-blue-800" :
                                  "bg-gray-100 text-gray-800"
                                }>
                                  {exam.isAllocated ? 'Allocated' : exam.status}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <span>🏛️ {exam.departmentName}</span>
                                <span>📚 {exam.levelName}</span>
                                <span>📅 {exam.examDate.toLocaleDateString()}</span>
                                <span>🕐 {exam.startTime} - {exam.endTime}</span>
                                <span>🏢 {exam.selectedHalls?.length || 0} halls {exam.selectedHalls?.length === 0 ? '(will use all available)' : ''}</span>
                                {exam.studentCount && (
                                  <span>👥 {exam.studentCount} students</span>
                                )}
                              </div>
                              {!canSelect && (
                                <div className="text-xs text-red-600 mt-1">
                                  {exam.isAllocated ? 'Already allocated' :
                                   !exam.startTime || !exam.endTime ? 'Missing time slots' :
                                   exam.status === 'cancelled' ? 'Exam cancelled' :
                                   exam.status === 'completed' ? 'Exam completed' :
                                   new Date(exam.examDate) < new Date() ? 'Exam date has passed' :
                                   'Cannot allocate this exam'}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}

                {/* Selection Summary */}
                {selectedExamIds.length > 0 && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2 text-green-800">
                      <CheckCircle className="h-4 w-4" />
                      <span className="font-medium">
                        {selectedExamIds.length} exam{selectedExamIds.length !== 1 ? 's' : ''} selected for allocation
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Allocation Settings */}
          {selectedExamIds.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Allocation Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Mixed Department Priority</label>
                      <p className="text-xs text-muted-foreground">
                        Prioritize mixing students from different departments in the same hall
                      </p>
                    </div>
                    <Checkbox
                      checked={allocationSettings.prioritizeMixedDepartments}
                      onCheckedChange={(checked) => setAllocationSettings(prev => ({
                        ...prev,
                        prioritizeMixedDepartments: checked as boolean
                      }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Group by Study Mode</label>
                      <p className="text-xs text-muted-foreground">
                        Group full-time and part-time students separately within halls
                      </p>
                    </div>
                    <Checkbox
                      checked={allocationSettings.groupByStudyMode}
                      onCheckedChange={(checked) => setAllocationSettings(prev => ({
                        ...prev,
                        groupByStudyMode: checked as boolean
                      }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Random Hall Selection</label>
                      <p className="text-xs text-muted-foreground">
                        Randomize hall selection order for fair distribution
                      </p>
                    </div>
                    <Checkbox
                      checked={allocationSettings.randomHallSelection}
                      onCheckedChange={(checked) => setAllocationSettings(prev => ({
                        ...prev,
                        randomHallSelection: checked as boolean
                      }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Allow Partial Allocation</label>
                      <p className="text-xs text-muted-foreground">
                        Allow allocation even if not all students can be accommodated
                      </p>
                    </div>
                    <Checkbox
                      checked={allocationSettings.allowPartialAllocation}
                      onCheckedChange={(checked) => setAllocationSettings(prev => ({
                        ...prev,
                        allowPartialAllocation: checked as boolean
                      }))}
                    />
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <Button
                    onClick={startAllocation}
                    disabled={isLoading || selectedExamIds.length === 0}
                    className="w-full"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Allocating Halls...
                      </>
                    ) : (
                      <>
                        <Shuffle className="mr-2 h-4 w-4" />
                        Start Hall Allocation ({selectedExamIds.length} exams)
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Allocation Progress View */}
      {viewMode === 'allocation' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                Allocating Halls...
              </CardTitle>
              <Button
                variant="destructive"
                size="sm"
                onClick={cancelAllocation}
                disabled={isCancelling}
                className="flex items-center gap-2"
              >
                {isCancelling ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Cancelling...
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4" />
                    Cancel Allocation
                  </>
                )}
              </Button>
            </div>
            {currentProcessingExam && (
              <div className="mt-2 text-sm text-muted-foreground">
                Currently processing: <span className="font-medium">{currentProcessingExam}</span>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Progress Summary */}
              <div className="grid grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {allocationResults.filter(r => r.status === 'completed').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {allocationResults.filter(r => r.status === 'processing').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Processing</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-500">
                    {allocationResults.filter(r => r.status === 'pending').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {allocationResults.filter(r => r.status === 'error').length}
                  </div>
                  <div className="text-xs text-muted-foreground">Errors</div>
                </div>
              </div>

              {/* Individual Exam Progress */}
              {allocationResults.map((result) => (
                <div key={result.examId} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="font-mono text-xs">
                      {result.examCode}
                    </Badge>
                    <span className="font-medium">{result.examTitle}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.status === 'pending' && (
                      <Badge variant="outline">Pending</Badge>
                    )}
                    {result.status === 'processing' && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Processing
                      </Badge>
                    )}
                    {result.status === 'completed' && (
                      <Badge variant="default" className="bg-green-600 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Completed
                      </Badge>
                    )}
                    {result.status === 'error' && (
                      <Badge variant="destructive" className="flex items-center gap-1">
                        <AlertTriangle className="h-3 w-3" />
                        Error
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results View */}
      {viewMode === 'results' && (
        <>
          {/* Import the results component */}
          <HallAllocationResults
            results={allocationResults}
            sessionName={selectedSession?.name || ""}
            semesterName={selectedSemester?.name}
          />
        </>
      )}
    </AdminLayout>
  );
}
