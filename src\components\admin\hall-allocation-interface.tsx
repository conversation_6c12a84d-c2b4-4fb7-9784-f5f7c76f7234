"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { TypographyH3, TypographyP } from "@/components/ui/typography";
import { 
  Building, 
  Users, 
  Shuffle, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  Eye,
  Edit,
  RotateCcw,
  Save,
  Settings
} from "lucide-react";
import { Exam, HallAllocation, AllocationSettings } from "@/types/exam";
import { Student } from "@/types/department";
import { Hall } from "@/types/hall";
import { 
  getStudentsForExam,
  getHallsForAllocation,
  generateHallAllocation,
  saveHallAllocations,
  getHallAllocationsByExam,
  checkAllocationConflicts,
  updateExamAllocationStatus
} from "@/lib/firebase/hall-allocation-service";

interface HallAllocationInterfaceProps {
  exam: Exam;
  onBack: () => void;
  onAllocationComplete: () => void;
}

export function HallAllocationInterface({ exam, onBack, onAllocationComplete }: HallAllocationInterfaceProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [halls, setHalls] = useState<Hall[]>([]);
  const [allocations, setAllocations] = useState<HallAllocation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string>("");
  const [allocationStep, setAllocationStep] = useState<'loading' | 'preview' | 'generated' | 'saved'>('loading');

  const [allocationSettings, setAllocationSettings] = useState<AllocationSettings>({
    prioritizeMixedDepartments: true,
    groupByStudyMode: true,
    randomHallSelection: true,
    allowPartialAllocation: false
  });

  useEffect(() => {
    loadInitialData();
  }, [exam.id]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError("");

      // Check if allocations already exist
      const existingAllocations = await getHallAllocationsByExam(exam.id);
      
      if (existingAllocations.length > 0) {
        setAllocations(existingAllocations);
        setAllocationStep('saved');
      } else {
        setAllocationStep('preview');
      }

      // Load students and halls in parallel
      const [studentsData, hallsData] = await Promise.all([
        getStudentsForExam(exam.levelId, exam.courseId),
        getHallsForAllocation(exam.selectedHalls)
      ]);

      setStudents(studentsData);
      setHalls(hallsData);

      console.log(`📊 Loaded ${studentsData.length} students and ${hallsData.length} halls`);
    } catch (error) {
      console.error("Error loading allocation data:", error);
      setError(error instanceof Error ? error.message : "Failed to load allocation data");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateAllocation = async () => {
    try {
      setIsGenerating(true);
      setError("");

      // Check for conflicts first
      const conflicts = await checkAllocationConflicts(
        exam.id,
        exam.examDate,
        exam.startTime,
        exam.endTime,
        exam.selectedHalls
      );

      if (conflicts.length > 0) {
        const conflictMessages = conflicts.map(c => c.message).join(', ');
        throw new Error(`Conflicts detected: ${conflictMessages}`);
      }

      // Generate allocation
      const generatedAllocations = await generateHallAllocation(
        exam.id,
        students,
        halls,
        exam.hallCapacityOverride,
        allocationSettings
      );

      setAllocations(generatedAllocations);
      setAllocationStep('generated');

      console.log('✅ Hall allocation generated successfully');
    } catch (error) {
      console.error("Error generating allocation:", error);
      setError(error instanceof Error ? error.message : "Failed to generate allocation");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveAllocation = async () => {
    try {
      setIsSaving(true);
      setError("");

      // Save allocations to Firestore
      const savedAllocations = await saveHallAllocations(allocations);
      
      // Update exam status
      await updateExamAllocationStatus(exam.id, true);

      setAllocations(savedAllocations);
      setAllocationStep('saved');

      console.log('✅ Hall allocation saved successfully');
      onAllocationComplete();
    } catch (error) {
      console.error("Error saving allocation:", error);
      setError(error instanceof Error ? error.message : "Failed to save allocation");
    } finally {
      setIsSaving(false);
    }
  };

  const handleRegenerateAllocation = () => {
    setAllocations([]);
    setAllocationStep('preview');
  };

  // Calculate statistics
  const totalStudents = students.length;
  const totalCapacity = halls.reduce((sum, hall) => 
    sum + (exam.hallCapacityOverride?.[hall.id] || hall.capacity), 0
  );
  const allocatedStudents = allocations.reduce((sum, allocation) => 
    sum + allocation.allocatedCapacity, 0
  );
  const utilizationRate = totalCapacity > 0 ? (allocatedStudents / totalCapacity) * 100 : 0;

  // Group students by department for statistics
  const departmentStats = students.reduce((acc, student) => {
    if (!acc[student.departmentId]) {
      acc[student.departmentId] = {
        name: student.departmentName,
        count: 0,
        fullTime: 0,
        partTime: 0
      };
    }
    acc[student.departmentId].count++;
    if (student.studyMode === 'full_time') {
      acc[student.departmentId].fullTime++;
    } else {
      acc[student.departmentId].partTime++;
    }
    return acc;
  }, {} as Record<string, { name: string; count: number; fullTime: number; partTime: number }>);

  if (isLoading) {
    return (
      <Card className="w-full max-w-6xl mx-auto">
        <CardContent className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading allocation data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <TypographyH3 className="flex items-center gap-2">
            <Building className="h-6 w-6" />
            Hall Allocation - {exam.courseCode}
          </TypographyH3>
          <TypographyP className="text-muted-foreground">
            {exam.courseTitle} • {exam.departmentName} • {exam.levelName}
          </TypographyP>
          <TypographyP className="text-sm text-muted-foreground">
            📅 {exam.examDate.toLocaleDateString()} • 🕐 {exam.startTime} - {exam.endTime}
          </TypographyP>
        </div>
        <Button variant="outline" onClick={onBack}>
          Back to Exams
        </Button>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium">Error</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
        </div>
      )}

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStudents}</div>
            <p className="text-xs text-muted-foreground">Registered for exam</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Halls</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{halls.length}</div>
            <p className="text-xs text-muted-foreground">Selected for allocation</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Capacity</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCapacity}</div>
            <p className="text-xs text-muted-foreground">
              {totalCapacity >= totalStudents ? 'Sufficient' : 'Insufficient'} capacity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilization</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{utilizationRate.toFixed(1)}%</div>
            <Progress value={utilizationRate} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Department Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Student Distribution by Department</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(departmentStats).map(([deptId, stats]) => (
              <div key={deptId} className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">{stats.name}</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Total:</span>
                    <Badge variant="outline">{stats.count}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Full Time:</span>
                    <Badge variant="secondary">{stats.fullTime}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Part Time:</span>
                    <Badge variant="secondary">{stats.partTime}</Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Allocation Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Allocation Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label htmlFor="mixedDepartments" className="text-sm font-medium">Mixed Department Priority</label>
                <input
                  id="mixedDepartments"
                  type="checkbox"
                  checked={allocationSettings.prioritizeMixedDepartments}
                  onChange={(e) => setAllocationSettings(prev => ({
                    ...prev,
                    prioritizeMixedDepartments: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                  disabled={allocationStep === 'saved'}
                  aria-label="Prioritize mixing students from different departments in the same hall"
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Prioritize mixing students from different departments in the same hall
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label htmlFor="groupByMode" className="text-sm font-medium">Group by Study Mode</label>
                <input
                  id="groupByMode"
                  type="checkbox"
                  checked={allocationSettings.groupByStudyMode}
                  onChange={(e) => setAllocationSettings(prev => ({
                    ...prev,
                    groupByStudyMode: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                  disabled={allocationStep === 'saved'}
                  aria-label="Group full-time and part-time students separately within halls"
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Group full-time and part-time students separately within halls
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label htmlFor="randomHalls" className="text-sm font-medium">Random Hall Selection</label>
                <input
                  id="randomHalls"
                  type="checkbox"
                  checked={allocationSettings.randomHallSelection}
                  onChange={(e) => setAllocationSettings(prev => ({
                    ...prev,
                    randomHallSelection: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                  disabled={allocationStep === 'saved'}
                  aria-label="Randomize hall selection order for fair distribution"
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Randomize hall selection order for fair distribution
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label htmlFor="partialAllocation" className="text-sm font-medium">Allow Partial Allocation</label>
                <input
                  id="partialAllocation"
                  type="checkbox"
                  checked={allocationSettings.allowPartialAllocation}
                  onChange={(e) => setAllocationSettings(prev => ({
                    ...prev,
                    allowPartialAllocation: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                  disabled={allocationStep === 'saved'}
                  aria-label="Allow allocation even if not all students can be accommodated"
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Allow allocation even if not all students can be accommodated
              </p>
            </div>
          </div>

          <Separator />

          <div className="flex gap-4">
            {allocationStep === 'preview' && (
              <Button
                onClick={handleGenerateAllocation}
                disabled={isGenerating || totalCapacity < totalStudents && !allocationSettings.allowPartialAllocation}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Allocation...
                  </>
                ) : (
                  <>
                    <Shuffle className="mr-2 h-4 w-4" />
                    Generate Hall Allocation
                  </>
                )}
              </Button>
            )}

            {allocationStep === 'generated' && (
              <>
                <Button
                  onClick={handleSaveAllocation}
                  disabled={isSaving}
                  className="flex-1"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving Allocation...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save & Approve Allocation
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleRegenerateAllocation}
                  disabled={isSaving}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Regenerate
                </Button>
              </>
            )}

            {allocationStep === 'saved' && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="font-medium">Allocation Saved & Approved</span>
              </div>
            )}
          </div>

          {totalCapacity < totalStudents && !allocationSettings.allowPartialAllocation && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Insufficient Capacity</span>
              </div>
              <p className="text-yellow-700 text-sm mt-1">
                Total capacity ({totalCapacity}) is less than students ({totalStudents}).
                Enable "Allow Partial Allocation" or add more halls.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Hall Allocations Preview/Results */}
      {allocations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Hall Allocations ({allocations.length} halls)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {allocations.map((allocation, index) => (
                <div key={allocation.hallId} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium flex items-center gap-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          {allocation.hallCode}
                        </Badge>
                        {allocation.hallName}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {allocation.allocatedCapacity} students allocated
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {allocation.allocatedCapacity} / {halls.find(h => h.id === allocation.hallId)?.capacity || 0}
                      </div>
                      <Progress
                        value={(allocation.allocatedCapacity / (halls.find(h => h.id === allocation.hallId)?.capacity || 1)) * 100}
                        className="w-24 mt-1"
                      />
                    </div>
                  </div>

                  {/* Student List Preview */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Students:</span>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        View All ({allocation.studentsAssigned.length})
                      </Button>
                    </div>

                    {/* Department breakdown */}
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(
                        allocation.studentsAssigned.reduce((acc, student) => {
                          if (!acc[student.departmentId]) {
                            acc[student.departmentId] = {
                              name: student.departmentName,
                              count: 0,
                              fullTime: 0,
                              partTime: 0
                            };
                          }
                          acc[student.departmentId].count++;
                          if (student.studyMode === 'full_time') {
                            acc[student.departmentId].fullTime++;
                          } else {
                            acc[student.departmentId].partTime++;
                          }
                          return acc;
                        }, {} as Record<string, { name: string; count: number; fullTime: number; partTime: number }>)
                      ).map(([deptId, stats]) => (
                        <div key={deptId} className="text-xs">
                          <Badge variant="secondary">
                            {stats.name}: {stats.count} ({stats.fullTime}FT, {stats.partTime}PT)
                          </Badge>
                        </div>
                      ))}
                    </div>

                    {/* Sample students */}
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">Sample: </span>
                      {allocation.studentsAssigned.slice(0, 3).map(s => s.matricNumber).join(', ')}
                      {allocation.studentsAssigned.length > 3 && ` +${allocation.studentsAssigned.length - 3} more`}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Summary */}
            <Separator className="my-4" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-medium text-lg">{allocatedStudents}</div>
                <div className="text-muted-foreground">Students Allocated</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-lg">{allocations.length}</div>
                <div className="text-muted-foreground">Halls Used</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-lg">{utilizationRate.toFixed(1)}%</div>
                <div className="text-muted-foreground">Capacity Utilization</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
