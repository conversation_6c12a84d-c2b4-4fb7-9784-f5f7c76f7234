// Test Firebase Connection
import { auth, db } from './firebase';
import { connectAuthEmulator } from 'firebase/auth';

export const testFirebaseConnection = () => {
  console.log('🔥 Testing Firebase Connection...');
  
  // Test Firebase Auth
  console.log('🔐 Auth instance:', auth);
  console.log('🔐 Auth app:', auth.app);
  console.log('🔐 Auth config:', auth.config);
  
  // Test Firestore
  console.log('📊 Firestore instance:', db);
  console.log('📊 Firestore app:', db.app);
  
  // Test if we can access Firebase services
  try {
    console.log('✅ Firebase services are accessible');
    return true;
  } catch (error) {
    console.error('❌ Firebase connection error:', error);
    return false;
  }
};

// Call this function to test connection
if (typeof window !== 'undefined') {
  testFirebaseConnection();
}
