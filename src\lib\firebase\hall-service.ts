// Hall Management Service for Firestore
import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  getDoc,
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  where,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './firebase';
import { Hall, HallFormData, HallStatus, HallType } from '@/types/hall';

const HALLS_COLLECTION = 'halls';

// Convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
};

// Convert Hall data from Firestore
const convertHallData = (id: string, data: any): Hall => {
  return {
    id,
    name: data.name,
    code: data.code,
    capacity: data.capacity,
    location: data.location,
    type: data.type,
    status: data.status,
    facilities: data.facilities || [],
    description: data.description || '',
    createdAt: convertTimestamp(data.createdAt),
    updatedAt: convertTimestamp(data.updatedAt),
    createdBy: data.createdBy
  };
};

// Add new hall to Firestore
export const addHall = async (hallData: HallFormData, createdBy: string): Promise<Hall> => {
  try {
    console.log('🏛️ Adding hall to Firestore:', hallData);
    
    const hallDoc = {
      ...hallData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };
    
    const docRef = await addDoc(collection(db, HALLS_COLLECTION), hallDoc);
    console.log('✅ Hall added with ID:', docRef.id);
    
    // Get the created document to return with proper timestamps
    const createdDoc = await getDoc(docRef);
    if (createdDoc.exists()) {
      return convertHallData(docRef.id, createdDoc.data());
    }
    
    throw new Error('Failed to retrieve created hall');
  } catch (error) {
    console.error('❌ Error adding hall:', error);
    throw new Error(`Failed to add hall: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get all halls from Firestore
export const getAllHalls = async (): Promise<Hall[]> => {
  try {
    console.log('🏛️ Fetching all halls from Firestore...');
    
    const q = query(
      collection(db, HALLS_COLLECTION),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const halls: Hall[] = [];
    
    querySnapshot.forEach((doc) => {
      halls.push(convertHallData(doc.id, doc.data()));
    });
    
    console.log(`✅ Retrieved ${halls.length} halls from Firestore`);
    return halls;
  } catch (error) {
    console.error('❌ Error fetching halls:', error);
    throw new Error(`Failed to fetch halls: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get hall by ID
export const getHallById = async (hallId: string): Promise<Hall | null> => {
  try {
    console.log('🏛️ Fetching hall by ID:', hallId);
    
    const docRef = doc(db, HALLS_COLLECTION, hallId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const hall = convertHallData(docSnap.id, docSnap.data());
      console.log('✅ Hall retrieved:', hall.name);
      return hall;
    } else {
      console.log('❌ Hall not found:', hallId);
      return null;
    }
  } catch (error) {
    console.error('❌ Error fetching hall:', error);
    throw new Error(`Failed to fetch hall: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Update hall in Firestore
export const updateHall = async (hallId: string, updates: Partial<HallFormData>): Promise<Hall> => {
  try {
    console.log('🏛️ Updating hall:', hallId, updates);

    const docRef = doc(db, HALLS_COLLECTION, hallId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });

    // Get the updated document to return
    const updatedDoc = await getDoc(docRef);
    if (updatedDoc.exists()) {
      const updatedHall = convertHallData(updatedDoc.id, updatedDoc.data());
      console.log('✅ Hall updated successfully:', updatedHall.name);
      return updatedHall;
    }

    throw new Error('Failed to retrieve updated hall');
  } catch (error) {
    console.error('❌ Error updating hall:', error);
    throw new Error(`Failed to update hall: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Delete hall from Firestore
export const deleteHall = async (hallId: string): Promise<void> => {
  try {
    console.log('🏛️ Deleting hall:', hallId);
    
    const docRef = doc(db, HALLS_COLLECTION, hallId);
    await deleteDoc(docRef);
    
    console.log('✅ Hall deleted successfully');
  } catch (error) {
    console.error('❌ Error deleting hall:', error);
    throw new Error(`Failed to delete hall: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get halls by status
export const getHallsByStatus = async (status: HallStatus): Promise<Hall[]> => {
  try {
    console.log('🏛️ Fetching halls by status:', status);
    
    const q = query(
      collection(db, HALLS_COLLECTION),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const halls: Hall[] = [];
    
    querySnapshot.forEach((doc) => {
      halls.push(convertHallData(doc.id, doc.data()));
    });
    
    console.log(`✅ Retrieved ${halls.length} halls with status: ${status}`);
    return halls;
  } catch (error) {
    console.error('❌ Error fetching halls by status:', error);
    throw new Error(`Failed to fetch halls by status: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get halls by type
export const getHallsByType = async (type: HallType): Promise<Hall[]> => {
  try {
    console.log('🏛️ Fetching halls by type:', type);
    
    const q = query(
      collection(db, HALLS_COLLECTION),
      where('type', '==', type),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const halls: Hall[] = [];
    
    querySnapshot.forEach((doc) => {
      halls.push(convertHallData(doc.id, doc.data()));
    });
    
    console.log(`✅ Retrieved ${halls.length} halls of type: ${type}`);
    return halls;
  } catch (error) {
    console.error('❌ Error fetching halls by type:', error);
    throw new Error(`Failed to fetch halls by type: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get active halls (for exam scheduling)
export const getActiveHalls = async (): Promise<Hall[]> => {
  return getHallsByStatus('active');
};

// Check if hall code is unique
export const isHallCodeUnique = async (code: string, excludeId?: string): Promise<boolean> => {
  try {
    console.log('🏛️ Checking hall code uniqueness:', code);
    
    const q = query(
      collection(db, HALLS_COLLECTION),
      where('code', '==', code.toUpperCase())
    );
    
    const querySnapshot = await getDocs(q);
    
    // If excluding an ID (for updates), check if any other hall has this code
    if (excludeId) {
      const otherHalls = querySnapshot.docs.filter(doc => doc.id !== excludeId);
      return otherHalls.length === 0;
    }
    
    return querySnapshot.empty;
  } catch (error) {
    console.error('❌ Error checking hall code uniqueness:', error);
    return false;
  }
};
