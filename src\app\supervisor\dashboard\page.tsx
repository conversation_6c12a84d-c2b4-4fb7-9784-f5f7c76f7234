"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyH1, TypographyH2, TypographyP } from "@/components/ui/typography";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Calendar, 
  Building, 
  Clock, 
  Users, 
  Shield,
  LogOut,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  Info,
  Bell,
  UserCheck,
  ClipboardList,
  MapPin
} from "lucide-react";

interface SupervisorInfo {
  id: string;
  name: string;
  role: 'supervisor' | 'invigilator';
  department: string;
  email: string;
}

interface AssignedExam {
  id: string;
  courseCode: string;
  courseTitle: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  duration: number;
  hallName: string;
  hallLocation: string;
  studentsCount: number;
  status: 'upcoming' | 'ongoing' | 'completed';
  role: 'supervisor' | 'invigilator';
}

export default function SupervisorDashboard() {
  const [supervisorInfo, setSupervisorInfo] = useState<SupervisorInfo | null>(null);
  const [assignedExams, setAssignedExams] = useState<AssignedExam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const router = useRouter();

  useEffect(() => {
    loadSupervisorData();
  }, []);

  const loadSupervisorData = async () => {
    setIsLoading(true);
    try {
      // Check if supervisor is logged in
      const session = localStorage.getItem('supervisor_session');
      if (!session) {
        router.push('/');
        return;
      }

      // Simulate loading supervisor data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock supervisor data - replace with real Firebase queries
      setSupervisorInfo({
        id: "SUP-2024-001",
        name: "Dr. Jane Smith",
        role: "supervisor",
        department: "Computer Science",
        email: "<EMAIL>"
      });

      // Mock assigned exams - replace with real Firebase queries
      setAssignedExams([
        {
          id: '1',
          courseCode: 'CSC201',
          courseTitle: 'Data Structures & Algorithms',
          examDate: new Date('2024-12-15'),
          startTime: '09:00',
          endTime: '12:00',
          duration: 180,
          hallName: 'Main Hall A',
          hallLocation: 'Ground Floor, Main Building',
          studentsCount: 180,
          status: 'upcoming',
          role: 'supervisor'
        },
        {
          id: '2',
          courseCode: 'CSC202',
          courseTitle: 'Database Management Systems',
          examDate: new Date('2024-12-17'),
          startTime: '14:00',
          endTime: '17:00',
          duration: 180,
          hallName: 'ICT Hall B',
          hallLocation: 'First Floor, ICT Building',
          studentsCount: 120,
          status: 'upcoming',
          role: 'invigilator'
        },
        {
          id: '3',
          courseCode: 'CSC101',
          courseTitle: 'Introduction to Computer Science',
          examDate: new Date('2024-12-05'),
          startTime: '09:00',
          endTime: '12:00',
          duration: 180,
          hallName: 'Main Hall A',
          hallLocation: 'Ground Floor, Main Building',
          studentsCount: 195,
          status: 'completed',
          role: 'supervisor'
        }
      ]);

    } catch (error) {
      setError("Failed to load supervisor data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('supervisor_session');
    router.push('/');
  };

  const getStatusBadge = (status: AssignedExam['status']) => {
    switch (status) {
      case 'upcoming':
        return <Badge className="bg-blue-600">Upcoming</Badge>;
      case 'ongoing':
        return <Badge className="bg-green-600">Ongoing</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getRoleBadge = (role: AssignedExam['role']) => {
    return role === 'supervisor' 
      ? <Badge variant="outline" className="text-purple-600 border-purple-600">Supervisor</Badge>
      : <Badge variant="outline" className="text-orange-600 border-orange-600">Invigilator</Badge>;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    return new Date(`2024-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const upcomingExams = assignedExams.filter(exam => exam.status === 'upcoming');
  const completedExams = assignedExams.filter(exam => exam.status === 'completed');
  const supervisorRoles = assignedExams.filter(exam => exam.role === 'supervisor').length;
  const invigilatorRoles = assignedExams.filter(exam => exam.role === 'invigilator').length;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-purple-600 mx-auto mb-4" />
          <TypographyP className="text-gray-600">Loading your dashboard...</TypographyP>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-purple-600 p-2 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hall Automata</h1>
                <p className="text-sm text-gray-600">Supervisor Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="font-mono hidden sm:inline-flex">
                🎓 MAPOLY
              </Badge>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Welcome Section */}
        {supervisorInfo && (
          <div className="mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <TypographyH1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                    Welcome back, {supervisorInfo.name}!
                  </TypographyH1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Shield className="h-4 w-4" />
                      {supervisorInfo.id}
                    </div>
                    <div className="flex items-center gap-1">
                      <UserCheck className="h-4 w-4" />
                      {supervisorInfo.role.charAt(0).toUpperCase() + supervisorInfo.role.slice(1)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Building className="h-4 w-4" />
                      {supervisorInfo.department}
                    </div>
                  </div>
                </div>
                <Button variant="outline" onClick={loadSupervisorData} className="mt-4 sm:mt-0">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 lg:gap-6 mb-8">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{assignedExams.length}</div>
              <p className="text-xs text-muted-foreground">This semester</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{upcomingExams.length}</div>
              <p className="text-xs text-muted-foreground">Exams scheduled</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">As Supervisor</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{supervisorRoles}</div>
              <p className="text-xs text-muted-foreground">Primary role</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">As Invigilator</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{invigilatorRoles}</div>
              <p className="text-xs text-muted-foreground">Support role</p>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Assignments */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <TypographyH2 className="text-xl font-bold text-gray-900">
              Upcoming Assignments
            </TypographyH2>
            {upcomingExams.length > 0 && (
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                {upcomingExams.length} scheduled
              </Badge>
            )}
          </div>

          {upcomingExams.length === 0 ? (
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <TypographyP className="text-gray-600 mb-2">No upcoming assignments</TypographyP>
                <TypographyP className="text-sm text-gray-500">
                  Your exam supervision schedule will appear here when available
                </TypographyP>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {upcomingExams.map((exam) => (
                <Card key={exam.id} className="bg-white/80 backdrop-blur-sm hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg font-bold text-gray-900">
                          {exam.courseCode}
                        </CardTitle>
                        <TypographyP className="text-gray-600 mt-1">
                          {exam.courseTitle}
                        </TypographyP>
                      </div>
                      <div className="flex flex-col gap-2">
                        {getStatusBadge(exam.status)}
                        {getRoleBadge(exam.role)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{formatDate(exam.examDate)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-4 w-4 text-green-600" />
                        <span>{formatTime(exam.startTime)} - {formatTime(exam.endTime)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Building className="h-4 w-4 text-purple-600" />
                        <span className="font-medium">{exam.hallName}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-4 w-4 text-orange-600" />
                        <span>{exam.studentsCount} students</span>
                      </div>
                    </div>
                    
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        <strong>Location:</strong> {exam.hallLocation}
                        <br />
                        <strong>Duration:</strong> {exam.duration} minutes
                        <br />
                        <strong>Role:</strong> You are assigned as the {exam.role} for this exam.
                      </AlertDescription>
                    </Alert>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Recent Assignments */}
        {completedExams.length > 0 && (
          <div>
            <TypographyH2 className="text-xl font-bold text-gray-900 mb-6">
              Recent Assignments
            </TypographyH2>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-0">
                <div className="divide-y divide-gray-200">
                  {completedExams.slice(0, 3).map((exam) => (
                    <div key={exam.id} className="p-4 hover:bg-gray-50/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">
                            {exam.courseCode} - {exam.courseTitle}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {formatDate(exam.examDate)} • {exam.hallName} • {exam.studentsCount} students
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getRoleBadge(exam.role)}
                          {getStatusBadge(exam.status)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  );
}
