// <PERSON>ript to update all exam dates to future dates (October 2025)
// This fixes the issue where exam dates were set in the past

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, writeBatch, doc, query, where } from 'firebase/firestore';
import { firebaseConfig } from '../lib/firebase/config';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// New exam dates for October 2025 (5-day exam period)
const NEW_EXAM_DATES = [
  { day: 'Monday', date: new Date('2025-10-06'), oldDate: new Date('2024-12-02') },
  { day: 'Tuesday', date: new Date('2025-10-07'), oldDate: new Date('2024-12-03') },
  { day: 'Wednesday', date: new Date('2025-10-08'), oldDate: new Date('2024-12-04') },
  { day: 'Thursday', date: new Date('2025-10-09'), oldDate: new Date('2024-12-05') },
  { day: 'Friday', date: new Date('2025-10-10'), oldDate: new Date('2024-12-06') }
];

interface ExamData {
  id: string;
  examDate: Date;
  courseCode: string;
  courseTitle: string;
  departmentName: string;
  levelName: string;
  startTime: string;
  endTime: string;
}

// Function to get the new date based on old date
function getNewExamDate(oldDate: Date): Date {
  const oldDateString = oldDate.toDateString();
  
  for (const dateMapping of NEW_EXAM_DATES) {
    if (dateMapping.oldDate.toDateString() === oldDateString) {
      return dateMapping.date;
    }
  }
  
  // If no exact match found, try to match by day of week
  const oldDayOfWeek = oldDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const mondayIndex = 1; // Monday is day 1
  
  if (oldDayOfWeek >= mondayIndex && oldDayOfWeek <= 5) { // Monday to Friday
    const dayIndex = oldDayOfWeek - mondayIndex; // Convert to 0-4 index
    if (dayIndex < NEW_EXAM_DATES.length) {
      return NEW_EXAM_DATES[dayIndex].date;
    }
  }
  
  // Default to first exam date if no match
  return NEW_EXAM_DATES[0].date;
}

// Function to get all exams from database
async function getAllExams(): Promise<ExamData[]> {
  console.log('📚 Fetching all exams from database...');
  
  try {
    const examsSnapshot = await getDocs(collection(db, 'exams'));
    const exams: ExamData[] = [];
    
    if (examsSnapshot.empty) {
      console.log('⚠️ No exams found in database');
      return [];
    }
    
    examsSnapshot.forEach((docSnapshot) => {
      const data = docSnapshot.data();
      
      // Convert Firestore timestamp to Date
      let examDate: Date;
      if (data.examDate && typeof data.examDate.toDate === 'function') {
        examDate = data.examDate.toDate();
      } else if (data.examDate instanceof Date) {
        examDate = data.examDate;
      } else {
        examDate = new Date(data.examDate);
      }
      
      exams.push({
        id: docSnapshot.id,
        examDate,
        courseCode: data.courseCode || '',
        courseTitle: data.courseTitle || '',
        departmentName: data.departmentName || '',
        levelName: data.levelName || '',
        startTime: data.startTime || '',
        endTime: data.endTime || ''
      });
    });
    
    console.log(`✅ Found ${exams.length} exams to update`);
    return exams;
    
  } catch (error) {
    console.error('❌ Error fetching exams:', error);
    throw error;
  }
}

// Function to update exam dates in batches
async function updateExamDates(exams: ExamData[]): Promise<void> {
  console.log('📅 Updating exam dates to October 2025...');
  
  if (exams.length === 0) {
    console.log('⚠️ No exams to update');
    return;
  }
  
  const batchSize = 500; // Firestore batch limit
  let updatedCount = 0;
  let batchCount = 0;
  
  // Group exams by their current date for better logging
  const examsByDate: Record<string, ExamData[]> = {};
  exams.forEach(exam => {
    const dateKey = exam.examDate.toDateString();
    if (!examsByDate[dateKey]) {
      examsByDate[dateKey] = [];
    }
    examsByDate[dateKey].push(exam);
  });
  
  console.log('\n📊 Exams by current date:');
  Object.entries(examsByDate).forEach(([date, examsOnDate]) => {
    console.log(`  ${date}: ${examsOnDate.length} exams`);
  });
  console.log('');
  
  // Process exams in batches
  for (let i = 0; i < exams.length; i += batchSize) {
    const batch = writeBatch(db);
    const examBatch = exams.slice(i, i + batchSize);
    batchCount++;
    
    console.log(`📝 Processing batch ${batchCount} (${examBatch.length} exams)...`);
    
    for (const exam of examBatch) {
      const newDate = getNewExamDate(exam.examDate);
      const examRef = doc(db, 'exams', exam.id);
      
      batch.update(examRef, {
        examDate: newDate,
        updatedAt: new Date(),
        updatedBy: 'date-update-script'
      });
      
      updatedCount++;
      
      // Log first few updates for verification
      if (updatedCount <= 5) {
        console.log(`  📅 ${exam.courseCode}: ${exam.examDate.toDateString()} → ${newDate.toDateString()}`);
      }
    }
    
    try {
      await batch.commit();
      console.log(`✅ Batch ${batchCount} committed successfully (${examBatch.length} exams)`);
    } catch (error) {
      console.error(`❌ Error committing batch ${batchCount}:`, error);
      throw error;
    }
  }
  
  console.log(`\n🎉 Successfully updated ${updatedCount} exam dates!`);
}

// Function to verify the updates
async function verifyUpdates(): Promise<void> {
  console.log('\n🔍 Verifying exam date updates...');
  
  try {
    // Get a sample of updated exams
    const examsSnapshot = await getDocs(collection(db, 'exams'));
    const sampleExams: any[] = [];
    
    let count = 0;
    examsSnapshot.forEach((docSnapshot) => {
      if (count < 10) { // Get first 10 exams as sample
        const data = docSnapshot.data();
        let examDate: Date;
        
        if (data.examDate && typeof data.examDate.toDate === 'function') {
          examDate = data.examDate.toDate();
        } else {
          examDate = new Date(data.examDate);
        }
        
        sampleExams.push({
          courseCode: data.courseCode,
          examDate: examDate,
          startTime: data.startTime,
          endTime: data.endTime
        });
        count++;
      }
    });
    
    console.log('\n📋 Sample of updated exams:');
    sampleExams.forEach((exam, index) => {
      console.log(`  ${index + 1}. ${exam.courseCode}: ${exam.examDate.toDateString()} ${exam.startTime}-${exam.endTime}`);
    });
    
    // Check if all dates are in October 2025
    const october2025Exams = sampleExams.filter(exam => 
      exam.examDate.getFullYear() === 2025 && exam.examDate.getMonth() === 9 // October is month 9
    );
    
    console.log(`\n✅ Verification: ${october2025Exams.length}/${sampleExams.length} sample exams are in October 2025`);
    
    if (october2025Exams.length === sampleExams.length) {
      console.log('🎉 All sample exams have been updated to October 2025!');
    } else {
      console.log('⚠️ Some exams may not have been updated correctly');
    }
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  }
}

// Main function
export async function updateExamDatesScript(): Promise<void> {
  console.log('📅 Hall Automata Exam Date Update Script');
  console.log('=======================================');
  console.log('');
  console.log('This script will update all exam dates from December 2024 to October 2025:');
  console.log('');
  console.log('📅 New Exam Schedule:');
  NEW_EXAM_DATES.forEach(dateInfo => {
    console.log(`  ${dateInfo.day}: ${dateInfo.date.toDateString()}`);
  });
  console.log('');
  
  try {
    // Step 1: Get all exams
    const exams = await getAllExams();
    
    if (exams.length === 0) {
      console.log('❌ No exams found. Please generate exam timetable first.');
      console.log('Run: npm run generate-exams');
      return;
    }
    
    // Step 2: Update exam dates
    await updateExamDates(exams);
    
    // Step 3: Verify updates
    await verifyUpdates();
    
    console.log('\n🎉 Exam date update completed successfully!');
    console.log('\n🚀 Next Steps:');
    console.log('1. Go to /admin/hall-allocation');
    console.log('2. Select "2024/2025" session');
    console.log('3. Choose "First Semester"');
    console.log('4. Verify exams now show October 2025 dates');
    console.log('5. Test hall allocation with future dates');
    console.log('');
    
  } catch (error) {
    console.error('❌ Error updating exam dates:', error);
    throw error;
  }
}

// Run if executed directly
if (require.main === module) {
  updateExamDatesScript()
    .then(() => {
      console.log('✅ Exam date update completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Exam date update failed:', error);
      process.exit(1);
    });
}
