"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { TypographyH3, TypographyP } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import {
  Crown,
  LayoutDashboard,
  Users,
  Building,
  Calendar,
  Settings,
  FileText,
  BarChart3,
  Shield,
  LogOut,
  ChevronLeft,
  ChevronRight,
  GraduationCap,
  BookOpen,
  Menu,
  X
} from "lucide-react";

interface AdminSidebarProps {
  className?: string;
  isMobileOpen?: boolean;
  onMobileToggle?: () => void;
}

const navigationItems = [
  {
    title: "Overview",
    href: "/admin/dashboard",
    icon: LayoutDashboard,
    description: "Dashboard overview"
  },
  {
    title: "Students",
    href: "/admin/students",
    icon: Users,
    description: "Manage student accounts & information",
    badge: "Core"
  },
  {
    title: "Departments",
    href: "/admin/departments",
    icon: Building,
    description: "Manage faculties & departments"
  },
  {
    title: "Courses",
    href: "/admin/courses",
    icon: BookOpen,
    description: "Manage academic courses"
  },
  {
    title: "Sessions",
    href: "/admin/sessions",
    icon: Calendar,
    description: "Academic sessions & semesters"
  },
  {
    title: "Exam Scheduling",
    href: "/admin/exams",
    icon: Calendar,
    description: "Schedule exams and allocate halls",
    badge: "New"
  },
  {
    title: "Hall Allocation",
    href: "/admin/hall-allocation",
    icon: Building,
    description: "Manage hall allocations by session",
    badge: "Core"
  },
  {
    title: "Hall Management",
    href: "/admin/halls",
    icon: Shield,
    description: "Manage exam halls and capacity"
  },
  {
    title: "Reports & Analytics",
    href: "/admin/reports",
    icon: BarChart3,
    description: "View attendance and exam reports"
  },
  {
    title: "System Settings",
    href: "/admin/settings",
    icon: Settings,
    description: "Configure system preferences"
  },
  {
    title: "Audit Logs",
    href: "/admin/logs",
    icon: FileText,
    description: "View system activity logs"
  }
];

export function AdminSidebar({ className, isMobileOpen = false, onMobileToggle }: AdminSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  // Close mobile sidebar when route changes (with a small delay to prevent flickering)
  useEffect(() => {
    if (isMobileOpen && onMobileToggle) {
      const timer = setTimeout(() => {
        onMobileToggle();
      }, 150); // Small delay to allow navigation to complete
      return () => clearTimeout(timer);
    }
  }, [pathname]);

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onMobileToggle}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "flex flex-col h-screen bg-background border-r border-border transition-all duration-300",
        // Desktop behavior
        "lg:relative lg:translate-x-0",
        isCollapsed ? "lg:w-16" : "lg:w-64",
        // Mobile behavior
        "fixed inset-y-0 left-0 z-50 w-64",
        "lg:z-auto",
        isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
        // Always display the sidebar, just transform it off-screen when closed
        "flex",
        className
      )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!isCollapsed && (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Crown className="h-5 w-5 text-white" />
            </div>
            <div>
              <TypographyH3 className="text-lg font-bold">Admin Portal</TypographyH3>
              <TypographyP className="text-xs text-muted-foreground">Hall Automata</TypographyP>
            </div>
          </div>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link key={item.href} href={item.href}>
              <div className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors group",
                isActive 
                  ? "bg-primary text-primary-foreground" 
                  : "hover:bg-muted text-muted-foreground hover:text-foreground"
              )}>
                <Icon className={cn(
                  "h-5 w-5 flex-shrink-0",
                  isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                )} />
                
                {!isCollapsed && (
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm truncate">
                        {item.title}
                      </span>
                      {item.badge && (
                        <Badge 
                          variant={isActive ? "secondary" : "outline"} 
                          className="ml-2 text-xs"
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <p className={cn(
                      "text-xs truncate mt-0.5",
                      isActive ? "text-primary-foreground/80" : "text-muted-foreground"
                    )}>
                      {item.description}
                    </p>
                  </div>
                )}
              </div>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border space-y-2">
        {!isCollapsed && (
          <div className="flex items-center space-x-3 px-3 py-2">
            <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">System Admin</p>
              <p className="text-xs text-muted-foreground truncate">MAPOLY Innovation</p>
            </div>
          </div>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "w-full justify-start text-muted-foreground hover:text-foreground",
            isCollapsed && "justify-center"
          )}
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span className="ml-2">Sign Out</span>}
        </Button>
      </div>
    </div>
    </>
  );
}
