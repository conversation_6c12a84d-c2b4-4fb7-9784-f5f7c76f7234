import { 
  collection, 
  addDoc, 
  query, 
  orderBy, 
  limit, 
  getDocs,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';

export interface ActivityItem {
  id: string;
  type: 'exam_created' | 'hall_allocated' | 'student_added' | 'user_created' | 'course_added' | 'department_created';
  message: string;
  timestamp: Date;
  user: string;
  details?: any;
}

// Log activity to Firestore
export const logActivity = async (
  type: ActivityItem['type'], 
  message: string, 
  user: string = 'admin',
  details?: any
) => {
  try {
    const activitiesRef = collection(db, 'system_activities');
    await addDoc(activitiesRef, {
      type,
      message,
      user,
      details: details || {},
      timestamp: serverTimestamp(),
      createdAt: serverTimestamp()
    });
    console.log('✅ Activity logged:', { type, message, user });
  } catch (error) {
    console.error('❌ Error logging activity:', error);
  }
};

// Get recent activities from Firestore
export const getRecentActivities = async (limitCount: number = 10): Promise<ActivityItem[]> => {
  try {
    const activitiesRef = collection(db, 'system_activities');
    const q = query(
      activitiesRef, 
      orderBy('timestamp', 'desc'), 
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    const activities: ActivityItem[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      activities.push({
        id: doc.id,
        type: data.type,
        message: data.message,
        timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toDate() : new Date(),
        user: data.user,
        details: data.details
      });
    });
    
    console.log(`✅ Loaded ${activities.length} recent activities`);
    return activities;
  } catch (error) {
    console.error('❌ Error loading recent activities:', error);
    return [];
  }
};

// Helper functions to log specific activities
export const logStudentAdded = async (studentName: string, matricNumber: string) => {
  await logActivity(
    'student_added',
    `New student added: ${studentName} (${matricNumber})`,
    'admin',
    { studentName, matricNumber }
  );
};

export const logCourseAdded = async (courseCode: string, courseTitle: string) => {
  await logActivity(
    'course_added',
    `New course added: ${courseCode} - ${courseTitle}`,
    'admin',
    { courseCode, courseTitle }
  );
};

export const logExamCreated = async (courseCode: string, examDate: Date) => {
  await logActivity(
    'exam_created',
    `Exam scheduled: ${courseCode} on ${examDate.toLocaleDateString()}`,
    'admin',
    { courseCode, examDate: examDate.toISOString() }
  );
};

export const logHallAllocated = async (examId: string, hallName: string, studentsCount: number) => {
  await logActivity(
    'hall_allocated',
    `Hall allocated: ${hallName} for ${studentsCount} students`,
    'admin',
    { examId, hallName, studentsCount }
  );
};

export const logUserCreated = async (userName: string, userRole: string) => {
  await logActivity(
    'user_created',
    `New ${userRole} created: ${userName}`,
    'admin',
    { userName, userRole }
  );
};

export const logDepartmentCreated = async (departmentName: string, facultyName: string) => {
  await logActivity(
    'department_created',
    `New department created: ${departmentName} in ${facultyName}`,
    'admin',
    { departmentName, facultyName }
  );
};
