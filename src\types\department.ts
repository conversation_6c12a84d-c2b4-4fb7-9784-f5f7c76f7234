// Department Management Types

export interface Faculty {
  id: string;
  name: string;
  code: string; // e.g., "ENG", "SCI", "BUS"
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Department {
  id: string;
  name: string;
  code: string; // e.g., "CS", "ME", "EE"
  facultyId: string;
  facultyCode: string; // For easy reference
  facultyName: string; // For easy reference
  description?: string;
  hasPortalAccess: boolean;
  accessId?: string; // e.g., "DEPT-ENG-CS-2024"
  accessPassword?: string;
  portalPermissions: DepartmentPermissions;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface DepartmentPermissions {
  canAddStudents: boolean;
  canViewStudents: boolean;
  canGenerateReports: boolean;
  canManageExamRegistrations: boolean;
}

export interface Level {
  id: string;
  name: string; // e.g., "ND1", "100", "Year 1"
  code: string; // e.g., "ND1", "100"
  departmentId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Student {
  id: string;
  matricNumber: string; // Must be at least 10 chars and contain 2+ forward slashes
  name?: string;
  gender: Gender;
  phoneNumber?: string;
  email?: string;
  facultyId: string;
  facultyName: string;
  departmentId: string;
  departmentName: string;
  levelId: string;
  levelName: string;
  studyMode: StudyMode;
  status: StudentStatus;
  defaultPassword: string;
  hasChangedPassword: boolean;
  hasSetupAccount: boolean; // For first-time login setup
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // Admin ID or Department Access ID
}

export type StudyMode = "full_time" | "part_time";

export type StudentStatus = "active" | "inactive" | "graduated" | "suspended";

export type Gender = "male" | "female";

// User Management Types (Supervisors/Invigilators)
export interface SystemUser {
  id: string;
  userId: string; // Auto-generated ID (SUP-2024-001, INV-2024-001)
  name: string;
  role: UserRole;
  email?: string;
  phoneNumber?: string;
  status: UserStatus;
  defaultPassword: string;
  hasChangedPassword: boolean;
  departmentAffiliation?: string[]; // Department IDs they can work with
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export type UserRole = "supervisor" | "invigilator";

export type UserStatus = "active" | "inactive" | "suspended";

// Form Data Types
export interface FacultyFormData {
  name: string;
  code: string;
  description?: string;
}

export interface DepartmentFormData {
  name: string;
  code: string;
  facultyId: string;
  description?: string;
  hasPortalAccess: boolean;
  portalPermissions: DepartmentPermissions;
}

export interface StudentFormData {
  matricNumber: string;
  name?: string;
  gender: Gender;
  phoneNumber?: string;
  email?: string;
  facultyId: string;
  departmentId: string;
  levelId: string;
  studyMode: StudyMode;
  status: StudentStatus;
}

export interface SystemUserFormData {
  name: string;
  role: UserRole;
  email?: string;
  phoneNumber?: string;
  departmentAffiliation?: string[];
  status: UserStatus;
}

// Level Presets
export const LEVEL_PRESETS = {
  ND_HND: [
    { name: "National Diploma 1", code: "ND1" },
    { name: "National Diploma 2", code: "ND2" },
    { name: "Higher National Diploma 1", code: "HND1" },
    { name: "Higher National Diploma 2", code: "HND2" }
  ],
  NUMERIC: [
    { name: "Level 100", code: "100" },
    { name: "Level 200", code: "200" },
    { name: "Level 300", code: "300" },
    { name: "Level 400", code: "400" },
    { name: "Level 500", code: "500" }
  ]
} as const;

export type LevelPresetType = keyof typeof LEVEL_PRESETS;

// Student History for tracking changes
export interface StudentHistory {
  id: string;
  studentId: string;
  action: StudentHistoryAction;
  previousData?: Partial<Student>;
  newData?: Partial<Student>;
  reason?: string;
  performedBy: string;
  performedAt: Date;
}

export type StudentHistoryAction = 
  | "created" 
  | "updated" 
  | "transferred" 
  | "level_changed" 
  | "status_changed" 
  | "graduated" 
  | "suspended";

// Department Portal Access
export interface DepartmentAccess {
  id: string;
  departmentId: string;
  accessId: string;
  accessPassword: string;
  permissions: DepartmentPermissions;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

// Utility functions
export const generateDepartmentAccessId = (facultyCode: string, departmentCode: string): string => {
  const currentYear = new Date().getFullYear();
  return `DEPT-${facultyCode.toUpperCase()}-${departmentCode.toUpperCase()}-${currentYear}`;
};

export const generateDefaultPassword = (type: 'student' | 'department'): string => {
  const currentYear = new Date().getFullYear();
  return `${type}${currentYear}`;
};

export const validateMatricNumber = (matricNumber: string): boolean => {
  // Must be at least 10 characters and contain at least 2 forward slashes
  const slashCount = (matricNumber.match(/\//g) || []).length;
  return slashCount >= 2 && matricNumber.trim().length >= 10;
};

export const generateSystemUserId = (role: UserRole): string => {
  const currentYear = new Date().getFullYear();
  const prefix = role === "supervisor" ? "SUP" : "INV";
  const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${currentYear}-${randomNum}`;
};

export const generateSystemUserPassword = (role: UserRole): string => {
  const currentYear = new Date().getFullYear();
  return `${role}${currentYear}`;
};

export const getStudyModeLabel = (mode: StudyMode): string => {
  return mode === "full_time" ? "Full Time" : "Part Time";
};

export const getStudentStatusLabel = (status: StudentStatus): string => {
  switch (status) {
    case "active": return "Active";
    case "inactive": return "Inactive";
    case "graduated": return "Graduated";
    case "suspended": return "Suspended";
    default: return status;
  }
};

export const getGenderLabel = (gender: Gender): string => {
  return gender === "male" ? "Male" : "Female";
};

export const getUserRoleLabel = (role: UserRole): string => {
  return role === "supervisor" ? "Supervisor" : "Invigilator";
};

export const getUserStatusLabel = (status: UserStatus): string => {
  switch (status) {
    case "active": return "Active";
    case "inactive": return "Inactive";
    case "suspended": return "Suspended";
    default: return status;
  }
};
