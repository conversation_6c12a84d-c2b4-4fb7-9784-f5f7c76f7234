// Hall Allocation Engine - Smart Distribution Algorithm
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc,
  updateDoc,
  query, 
  where,
  orderBy,
  writeBatch,
  serverTimestamp,
  getDoc
} from 'firebase/firestore';
import { db } from './firebase';
import { 
  HallAllocation, 
  StudentHallAssignment, 
  ExamConflict,
  AllocationSettings
} from '@/types/exam';
import { Student } from '@/types/department';
import { Hall } from '@/types/hall';

const STUDENT_COURSES_COLLECTION = 'student_course_registrations';
const STUDENTS_COLLECTION = 'students';
const HALLS_COLLECTION = 'halls';
const EXAMS_COLLECTION = 'exams';
const HALL_ALLOCATIONS_COLLECTION = 'hall_allocations';

// Convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
};

// Default allocation settings based on your requirements
const DEFAULT_ALLOCATION_SETTINGS: AllocationSettings = {
  prioritizeMixedDepartments: true,
  groupByStudyMode: true,
  randomHallSelection: true,
  allowPartialAllocation: false
};

// Get students for a specific exam based on level and department
export const getStudentsForExam = async (levelId: string, departmentId: string, courseId?: string): Promise<Student[]> => {
  try {
    console.log('👥 Fetching students for exam - Level:', levelId, 'Department:', departmentId, 'Course:', courseId);

    // Query students directly by level and department
    // This is the correct approach since all students in a level should take the exam
    const studentsQuery = query(
      collection(db, STUDENTS_COLLECTION),
      where('levelId', '==', levelId),
      where('departmentId', '==', departmentId),
      where('status', '==', 'active') // Only active students
    );

    const studentsSnapshot = await getDocs(studentsQuery);
    const students: Student[] = [];

    if (studentsSnapshot.empty) {
      console.log('⚠️ No students found in this level and department');
      return [];
    }
    
    studentsSnapshot.forEach((doc) => {
      const data = doc.data();
      students.push({
        id: doc.id,
        matricNumber: data.matricNumber,
        name: data.name,
        email: data.email,
        phoneNumber: data.phoneNumber,
        gender: data.gender,
        studyMode: data.studyMode,
        status: data.status,
        facultyId: data.facultyId,
        facultyName: data.facultyName,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        levelId: data.levelId,
        levelName: data.levelName,
        // Add missing Student properties with defaults
        defaultPassword: data.defaultPassword || '',
        hasChangedPassword: data.hasChangedPassword || false,
        hasSetupAccount: data.hasSetupAccount || false,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });
    
    console.log(`✅ Retrieved ${students.length} students for exam`);
    return students;
  } catch (error) {
    console.error('❌ Error fetching students for exam:', error);
    throw new Error(`Failed to fetch students: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get hall details for selected halls
export const getHallsForAllocation = async (hallIds: string[]): Promise<Hall[]> => {
  try {
    console.log('🏢 Fetching halls for allocation:', hallIds);
    
    if (hallIds.length === 0) {
      return [];
    }
    
    // Handle Firestore 'in' query limit of 10
    const halls: Hall[] = [];
    const batches = [];
    
    for (let i = 0; i < hallIds.length; i += 10) {
      batches.push(hallIds.slice(i, i + 10));
    }
    
    for (const batch of batches) {
      const hallsQuery = query(
        collection(db, HALLS_COLLECTION),
        where('__name__', 'in', batch)
      );
      
      const hallsSnapshot = await getDocs(hallsQuery);
      hallsSnapshot.forEach((doc) => {
        const data = doc.data();
        halls.push({
          id: doc.id,
          name: data.name,
          code: data.code,
          location: data.location,
          capacity: data.capacity,
          type: data.type || 'examination_hall',
          status: data.status,
          facilities: data.facilities || [],
          description: data.description,
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt),
          createdBy: data.createdBy
        });
      });
    }
    
    console.log(`✅ Retrieved ${halls.length} halls for allocation`);
    return halls;
  } catch (error) {
    console.error('❌ Error fetching halls:', error);
    throw new Error(`Failed to fetch halls: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get available halls for a specific exam time slot
export const getAvailableHallsForExam = async (
  examDate: Date,
  startTime: string,
  endTime: string,
  hallIds?: string[]
): Promise<Hall[]> => {
  try {
    console.log('🏢 Fetching available halls for exam...');
    console.log(`📅 Date: ${examDate.toDateString()}, Time: ${startTime}-${endTime}`);

    // Step 1: Get all halls or specific halls
    let allHalls: Hall[] = [];

    if (hallIds && hallIds.length > 0) {
      // Get specific halls
      allHalls = await getHallsForAllocation(hallIds);
    } else {
      // Get all active halls
      const { getAllHalls } = await import('./hall-service');
      const halls = await getAllHalls();
      allHalls = halls.filter(hall => hall.status === 'active');
    }

    console.log(`📊 Total halls to check: ${allHalls.length}`);

    // Step 2: Check which halls are already occupied at this time
    const conflicts = await checkAllocationConflicts(
      'temp-exam-id', // Temporary ID for conflict checking
      examDate,
      startTime,
      endTime,
      allHalls.map(h => h.id)
    );

    // Step 3: Filter out occupied halls
    const occupiedHallIds = conflicts
      .filter(c => c.type === 'hall_time_conflict')
      .map(c => c.hallId);

    const availableHalls = allHalls.filter(hall => !occupiedHallIds.includes(hall.id));

    console.log(`🚫 Occupied halls: ${occupiedHallIds.length}`);
    console.log(`✅ Available halls: ${availableHalls.length}`);

    if (occupiedHallIds.length > 0) {
      const occupiedHallNames = allHalls
        .filter(h => occupiedHallIds.includes(h.id))
        .map(h => h.name);
      console.log(`🚫 Occupied halls: ${occupiedHallNames.join(', ')}`);
    }

    return availableHalls;
  } catch (error) {
    console.error('❌ Error fetching available halls:', error);
    throw new Error(`Failed to fetch available halls: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Pre-allocation validation to ensure sufficient capacity
export const validateAllocationCapacity = async (
  examDate: Date,
  startTime: string,
  endTime: string,
  studentCount: number,
  hallIds?: string[]
): Promise<{
  isValid: boolean;
  availableCapacity: number;
  requiredCapacity: number;
  availableHalls: Hall[];
  message: string;
}> => {
  try {
    console.log('🔍 Validating allocation capacity...');
    console.log(`👥 Students: ${studentCount}, Date: ${examDate.toDateString()}, Time: ${startTime}-${endTime}`);

    // Get available halls for this time slot
    const availableHalls = await getAvailableHallsForExam(examDate, startTime, endTime, hallIds);

    // Calculate total available capacity
    const availableCapacity = availableHalls.reduce((sum, hall) => sum + hall.capacity, 0);

    const isValid = availableCapacity >= studentCount;

    let message = '';
    if (isValid) {
      message = `✅ Sufficient capacity: ${availableCapacity} seats available for ${studentCount} students`;
    } else {
      const shortage = studentCount - availableCapacity;
      message = `❌ Insufficient capacity: Need ${studentCount} seats, only ${availableCapacity} available (shortage: ${shortage})`;
    }

    console.log(message);

    return {
      isValid,
      availableCapacity,
      requiredCapacity: studentCount,
      availableHalls,
      message
    };
  } catch (error) {
    console.error('❌ Error validating allocation capacity:', error);
    throw new Error(`Failed to validate capacity: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Smart Hall Allocation Algorithm with Pre-Validation
export const generateHallAllocation = async (
  examId: string,
  students: Student[],
  availableHalls: Hall[],
  hallCapacityOverride: Record<string, number> = {},
  settings: AllocationSettings = DEFAULT_ALLOCATION_SETTINGS
): Promise<HallAllocation[]> => {
  try {
    console.log('🧠 Starting smart hall allocation with pre-validation...');
    console.log(`📊 Students: ${students.length}, Halls: ${availableHalls.length}`);

    if (students.length === 0) {
      throw new Error('No students to allocate');
    }

    if (availableHalls.length === 0) {
      throw new Error('No halls available for allocation');
    }

    // Get exam details
    const examDoc = await getDoc(doc(db, EXAMS_COLLECTION, examId));
    if (!examDoc.exists()) {
      throw new Error('Exam not found');
    }

    const examData = examDoc.data();

    // CRITICAL: Validate that these halls are actually available for this exam time
    console.log('🔍 Validating hall availability for exam time...');
    const actuallyAvailableHalls = await getAvailableHallsForExam(
      convertTimestamp(examData.examDate),
      examData.startTime,
      examData.endTime,
      availableHalls.map(h => h.id)
    );

    if (actuallyAvailableHalls.length === 0) {
      throw new Error('All selected halls are occupied at this time. Please choose different halls or change the exam time.');
    }

    if (actuallyAvailableHalls.length < availableHalls.length) {
      const occupiedCount = availableHalls.length - actuallyAvailableHalls.length;
      console.log(`⚠️ ${occupiedCount} halls are occupied and will be excluded from allocation`);
    }

    // Use only actually available halls
    console.log(`✅ Using ${actuallyAvailableHalls.length} available halls for allocation`);

    // Step 1: Group students by study mode if enabled
    const studentGroups = settings.groupByStudyMode
      ? groupStudentsByStudyMode(students)
      : { all: students };

    // Step 2: Prepare halls with capacity overrides
    const hallsWithCapacity = actuallyAvailableHalls.map(hall => ({
      ...hall,
      effectiveCapacity: hallCapacityOverride[hall.id] || hall.capacity
    }));
    
    // Step 3: Randomize hall order if enabled
    if (settings.randomHallSelection) {
      shuffleArray(hallsWithCapacity);
    }
    
    // Step 4: Calculate total capacity
    const totalCapacity = hallsWithCapacity.reduce((sum, hall) => sum + hall.effectiveCapacity, 0);
    
    if (totalCapacity < students.length && !settings.allowPartialAllocation) {
      throw new Error(`Insufficient hall capacity. Need: ${students.length}, Available: ${totalCapacity}`);
    }
    
    // Step 5: Allocate students to halls
    const allocations: HallAllocation[] = [];
    let remainingStudents = [...students];
    
    for (const hall of hallsWithCapacity) {
      if (remainingStudents.length === 0) break;
      
      const hallCapacity = hall.effectiveCapacity;
      let hallStudents: StudentHallAssignment[] = [];
      
      // Step 6: Apply mixed department prioritization
      if (settings.prioritizeMixedDepartments && remainingStudents.length > hallCapacity) {
        hallStudents = allocateWithMixedDepartments(remainingStudents, hallCapacity, settings);
      } else {
        // Simple allocation - take first N students
        const studentsToAllocate = remainingStudents.slice(0, hallCapacity);
        hallStudents = studentsToAllocate.map(student => ({
          studentId: student.id,
          matricNumber: student.matricNumber,
          studentName: student.name,
          departmentId: student.departmentId,
          departmentName: student.departmentName,
          levelId: student.levelId,
          levelName: student.levelName,
          studyMode: student.studyMode
        }));
      }
      
      // Remove allocated students from remaining list
      const allocatedStudentIds = hallStudents.map(s => s.studentId);
      remainingStudents = remainingStudents.filter(s => !allocatedStudentIds.includes(s.id));
      
      // Create hall allocation
      const allocation: HallAllocation = {
        id: '', // Will be set when saved to Firestore
        examId: examId,
        examCode: examData.courseCode,
        examTitle: examData.courseTitle,
        hallId: hall.id,
        hallName: hall.name,
        hallCode: hall.code,
        sessionId: examData.sessionId,
        semesterId: examData.semesterId,
        examDate: convertTimestamp(examData.examDate),
        startTime: examData.startTime,
        endTime: examData.endTime,
        allocatedCapacity: hallStudents.length,
        studentsAssigned: hallStudents,
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system'
      };
      
      allocations.push(allocation);
      
      console.log(`✅ Allocated ${hallStudents.length} students to ${hall.name}`);
    }
    
    // Check if all students were allocated
    if (remainingStudents.length > 0 && !settings.allowPartialAllocation) {
      throw new Error(`Could not allocate ${remainingStudents.length} students. Consider adding more halls or increasing capacity.`);
    }
    
    console.log(`🎉 Hall allocation completed! Created ${allocations.length} allocations`);
    return allocations;
    
  } catch (error) {
    console.error('❌ Error generating hall allocation:', error);
    throw new Error(`Failed to generate allocation: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Group students by study mode
const groupStudentsByStudyMode = (students: Student[]): Record<string, Student[]> => {
  const groups: Record<string, Student[]> = {
    full_time: [],
    part_time: []
  };
  
  students.forEach(student => {
    if (groups[student.studyMode]) {
      groups[student.studyMode].push(student);
    }
  });
  
  return groups;
};

// Shuffle array in place (Fisher-Yates algorithm)
const shuffleArray = <T>(array: T[]): void => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
};

// Allocate students with mixed department prioritization
const allocateWithMixedDepartments = (
  students: Student[], 
  capacity: number, 
  settings: AllocationSettings
): StudentHallAssignment[] => {
  console.log('🔀 Applying mixed department prioritization...');
  
  // Group students by department
  const departmentGroups: Record<string, Student[]> = {};
  students.forEach(student => {
    if (!departmentGroups[student.departmentId]) {
      departmentGroups[student.departmentId] = [];
    }
    departmentGroups[student.departmentId].push(student);
  });
  
  const departments = Object.keys(departmentGroups);
  console.log(`📊 Found ${departments.length} departments:`, departments.map(d => departmentGroups[d][0]?.departmentName));
  
  // If only one department, no mixing possible
  if (departments.length === 1) {
    console.log('ℹ️ Only one department found, using simple allocation');
    return students.slice(0, capacity).map(student => ({
      studentId: student.id,
      matricNumber: student.matricNumber,
      studentName: student.name,
      departmentId: student.departmentId,
      departmentName: student.departmentName,
      levelId: student.levelId,
      levelName: student.levelName,
      studyMode: student.studyMode
    }));
  }
  
  // Mixed department allocation - round-robin style
  const allocated: Student[] = [];
  let departmentIndex = 0;
  
  while (allocated.length < capacity && allocated.length < students.length) {
    const currentDept = departments[departmentIndex];
    const deptStudents = departmentGroups[currentDept];
    
    // Find next unallocated student from this department
    const availableStudent = deptStudents.find(s => !allocated.includes(s));
    
    if (availableStudent) {
      allocated.push(availableStudent);
      console.log(`➕ Added student from ${availableStudent.departmentName}: ${availableStudent.matricNumber}`);
    }
    
    // Move to next department
    departmentIndex = (departmentIndex + 1) % departments.length;
    
    // Safety check to prevent infinite loop
    if (allocated.length === 0 && departmentIndex === 0) {
      break;
    }
  }
  
  console.log(`✅ Mixed department allocation: ${allocated.length} students from ${departments.length} departments`);
  
  return allocated.map(student => ({
    studentId: student.id,
    matricNumber: student.matricNumber,
    studentName: student.name,
    departmentId: student.departmentId,
    departmentName: student.departmentName,
    levelId: student.levelId,
    levelName: student.levelName,
    studyMode: student.studyMode
  }));
};

// Save hall allocations to Firestore
export const saveHallAllocations = async (allocations: HallAllocation[]): Promise<HallAllocation[]> => {
  try {
    console.log('💾 Saving hall allocations to Firestore...');

    const batch = writeBatch(db);
    const savedAllocations: HallAllocation[] = [];

    for (const allocation of allocations) {
      const docRef = doc(collection(db, HALL_ALLOCATIONS_COLLECTION));
      const allocationDoc = {
        examId: allocation.examId,
        examCode: allocation.examCode,
        examTitle: allocation.examTitle,
        hallId: allocation.hallId,
        hallName: allocation.hallName,
        hallCode: allocation.hallCode,
        sessionId: allocation.sessionId,
        semesterId: allocation.semesterId,
        examDate: allocation.examDate,
        startTime: allocation.startTime,
        endTime: allocation.endTime,
        allocatedCapacity: allocation.allocatedCapacity,
        studentsAssigned: allocation.studentsAssigned,
        status: allocation.status,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: allocation.createdBy
      };

      batch.set(docRef, allocationDoc);

      savedAllocations.push({
        ...allocation,
        id: docRef.id
      });
    }

    await batch.commit();

    console.log(`✅ Saved ${savedAllocations.length} hall allocations`);
    return savedAllocations;
  } catch (error) {
    console.error('❌ Error saving hall allocations:', error);
    throw new Error(`Failed to save allocations: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Get existing hall allocations for an exam
export const getHallAllocationsByExam = async (examId: string): Promise<HallAllocation[]> => {
  try {
    console.log('📋 Fetching hall allocations for exam:', examId);

    const q = query(
      collection(db, HALL_ALLOCATIONS_COLLECTION),
      where('examId', '==', examId),
      orderBy('hallName', 'asc')
    );

    const querySnapshot = await getDocs(q);
    const allocations: HallAllocation[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      allocations.push({
        id: doc.id,
        examId: data.examId,
        examCode: data.examCode,
        examTitle: data.examTitle,
        hallId: data.hallId,
        hallName: data.hallName,
        hallCode: data.hallCode,
        sessionId: data.sessionId,
        semesterId: data.semesterId,
        examDate: convertTimestamp(data.examDate),
        startTime: data.startTime,
        endTime: data.endTime,
        allocatedCapacity: data.allocatedCapacity,
        studentsAssigned: data.studentsAssigned || [],
        status: data.status,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    console.log(`✅ Retrieved ${allocations.length} hall allocations`);
    return allocations;
  } catch (error) {
    console.error('❌ Error fetching hall allocations:', error);
    throw new Error(`Failed to fetch allocations: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Check for allocation conflicts
export const checkAllocationConflicts = async (
  examId: string,
  examDate: Date,
  startTime: string,
  endTime: string,
  selectedHalls: string[]
): Promise<ExamConflict[]> => {
  try {
    console.log('🔍 Checking allocation conflicts...');

    const conflicts: ExamConflict[] = [];

    // Get all allocations for the same date
    const examDateStr = examDate.toISOString().split('T')[0];

    // Query all allocations for the date (we'll filter by time in memory)
    const q = query(
      collection(db, HALL_ALLOCATIONS_COLLECTION),
      where('examDate', '>=', new Date(examDateStr)),
      where('examDate', '<', new Date(new Date(examDateStr).getTime() + 24 * 60 * 60 * 1000))
    );

    const querySnapshot = await getDocs(q);
    const existingAllocations: HallAllocation[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data.examId !== examId) { // Don't check against self
        existingAllocations.push({
          id: doc.id,
          examId: data.examId,
          examCode: data.examCode,
          examTitle: data.examTitle,
          hallId: data.hallId,
          hallName: data.hallName,
          hallCode: data.hallCode,
          sessionId: data.sessionId,
          semesterId: data.semesterId,
          examDate: convertTimestamp(data.examDate),
          startTime: data.startTime,
          endTime: data.endTime,
          allocatedCapacity: data.allocatedCapacity,
          studentsAssigned: data.studentsAssigned || [],
          status: data.status,
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt),
          createdBy: data.createdBy
        });
      }
    });

    // Check for hall time conflicts
    for (const allocation of existingAllocations) {
      const timeOverlap = checkTimeOverlap(startTime, endTime, allocation.startTime, allocation.endTime);

      if (timeOverlap && selectedHalls.includes(allocation.hallId)) {
        conflicts.push({
          type: 'hall_time_conflict',
          message: `Hall ${allocation.hallName} is already allocated to ${allocation.examCode} at this time`,
          examId: examId,
          examTitle: 'Current exam',
          conflictingExamId: allocation.examId,
          conflictingExamTitle: `${allocation.examCode} - ${allocation.examTitle}`,
          hallId: allocation.hallId,
          hallName: allocation.hallName,
          suggestions: ['Choose a different hall', 'Change the exam time', 'Contact administrator']
        });
      }
    }

    console.log(`🔍 Found ${conflicts.length} allocation conflicts`);
    return conflicts;
  } catch (error) {
    console.error('❌ Error checking allocation conflicts:', error);
    throw new Error(`Failed to check conflicts: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Helper function to check time overlap
const checkTimeOverlap = (start1: string, end1: string, start2: string, end2: string): boolean => {
  const startTime1 = new Date(`2000-01-01 ${start1}`);
  const endTime1 = new Date(`2000-01-01 ${end1}`);
  const startTime2 = new Date(`2000-01-01 ${start2}`);
  const endTime2 = new Date(`2000-01-01 ${end2}`);

  return startTime1 < endTime2 && startTime2 < endTime1;
};

// Update exam status after allocation
export const updateExamAllocationStatus = async (examId: string, isAllocated: boolean): Promise<void> => {
  try {
    await updateDoc(doc(db, EXAMS_COLLECTION, examId), {
      isAllocated,
      status: isAllocated ? 'allocated' : 'scheduled',
      updatedAt: serverTimestamp()
    });

    console.log(`✅ Updated exam ${examId} allocation status: ${isAllocated}`);
  } catch (error) {
    console.error('❌ Error updating exam allocation status:', error);
    throw new Error(`Failed to update exam status: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
