import { cn } from "@/lib/utils";

interface TypographyProps {
  children: React.ReactNode;
  className?: string;
}

// Beautiful Headings with Geist
export function TypographyH1({ children, className }: TypographyProps) {
  return (
    <h1 className={cn(
      "font-sans scroll-m-20 text-4xl font-bold tracking-tight lg:text-5xl",
      "text-foreground",
      className
    )}>
      {children}
    </h1>
  );
}

export function TypographyH2({ children, className }: TypographyProps) {
  return (
    <h2 className={cn(
      "font-sans scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0",
      "text-foreground border-border",
      className
    )}>
      {children}
    </h2>
  );
}

export function TypographyH3({ children, className }: TypographyProps) {
  return (
    <h3 className={cn(
      "font-sans scroll-m-20 text-2xl font-semibold tracking-tight",
      "text-foreground",
      className
    )}>
      {children}
    </h3>
  );
}

export function TypographyH4({ children, className }: TypographyProps) {
  return (
    <h4 className={cn(
      "font-sans scroll-m-20 text-xl font-semibold tracking-tight",
      "text-foreground",
      className
    )}>
      {children}
    </h4>
  );
}

// Body Text with Geist
export function TypographyP({ children, className }: TypographyProps) {
  return (
    <p className={cn(
      "font-sans leading-7 [&:not(:first-child)]:mt-6",
      "text-foreground",
      className
    )}>
      {children}
    </p>
  );
}

export function TypographyLead({ children, className }: TypographyProps) {
  return (
    <p className={cn(
      "font-sans text-xl text-muted-foreground",
      className
    )}>
      {children}
    </p>
  );
}

export function TypographyLarge({ children, className }: TypographyProps) {
  return (
    <div className={cn(
      "font-sans text-lg font-semibold",
      "text-foreground",
      className
    )}>
      {children}
    </div>
  );
}

export function TypographySmall({ children, className }: TypographyProps) {
  return (
    <small className={cn(
      "font-sans text-sm font-medium leading-none",
      "text-muted-foreground",
      className
    )}>
      {children}
    </small>
  );
}

export function TypographyMuted({ children, className }: TypographyProps) {
  return (
    <p className={cn(
      "font-sans text-sm text-muted-foreground",
      className
    )}>
      {children}
    </p>
  );
}

// Code and Data with Geist Mono
export function TypographyCode({ children, className }: TypographyProps) {
  return (
    <code className={cn(
      "font-mono relative rounded bg-muted px-[0.3rem] py-[0.2rem] text-sm font-semibold",
      "text-foreground",
      className
    )}>
      {children}
    </code>
  );
}

export function TypographyMatricNumber({ children, className }: TypographyProps) {
  return (
    <span className={cn(
      "font-mono text-sm font-medium tracking-wider",
      "text-primary",
      className
    )}>
      {children}
    </span>
  );
}

// Creative Text with Geist (stylized)
export function TypographyCreative({ children, className }: TypographyProps) {
  return (
    <span className={cn(
      "font-sans text-lg font-light italic",
      "text-foreground",
      className
    )}>
      {children}
    </span>
  );
}

// Lists with Geist
export function TypographyList({ children, className }: TypographyProps) {
  return (
    <ul className={cn(
      "font-sans my-6 ml-6 list-disc [&>li]:mt-2",
      "text-foreground",
      className
    )}>
      {children}
    </ul>
  );
}

// Blockquote with Geist
export function TypographyBlockquote({ children, className }: TypographyProps) {
  return (
    <blockquote className={cn(
      "font-sans mt-6 border-l-2 pl-6 italic",
      "border-border text-muted-foreground",
      className
    )}>
      {children}
    </blockquote>
  );
}

// Professional Accent Text with Geist
export function TypographyAccent({ children, className }: TypographyProps) {
  return (
    <span className={cn(
      "font-sans font-semibold text-primary",
      className
    )}>
      {children}
    </span>
  );
}

// System UI Text with Geist
export function TypographySystem({ children, className }: TypographyProps) {
  return (
    <span className={cn(
      "font-sans text-sm font-medium",
      "text-muted-foreground",
      className
    )}>
      {children}
    </span>
  );
}

// Table Typography with Geist
export function TypographyTable({ children, className }: TypographyProps) {
  return (
    <div className={cn(
      "font-sans my-6 w-full overflow-y-auto",
      className
    )}>
      <table className="w-full">
        <thead>
          <tr className="m-0 border-t p-0 even:bg-muted">
            {children}
          </tr>
        </thead>
      </table>
    </div>
  );
}
