"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Building, 
  Users, 
  Shuffle, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  Settings,
  Play,
  Pause,
  RotateCcw
} from "lucide-react";
import { Exam, AllocationSettings } from "@/types/exam";
import { 
  getStudentsForExam,
  getHallsForAllocation,
  generateHallAllocation,
  saveHallAllocations,
  updateExamAllocationStatus
} from "@/lib/firebase/hall-allocation-service";

interface BulkAllocationProgress {
  examId: string;
  examCode: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  studentCount?: number;
  allocatedCount?: number;
  error?: string;
}

interface BulkHallAllocationProps {
  exams: Exam[];
  onComplete: () => void;
  onCancel: () => void;
}

export function BulkHallAllocation({ exams, onComplete, onCancel }: BulkHallAllocationProps) {
  const [selectedExams, setSelectedExams] = useState<string[]>(exams.map(e => e.id));
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState<BulkAllocationProgress[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [allocationSettings, setAllocationSettings] = useState<AllocationSettings>({
    prioritizeMixedDepartments: true,
    groupByStudyMode: true,
    randomHallSelection: true,
    allowPartialAllocation: false
  });

  useEffect(() => {
    // Initialize progress tracking
    const initialProgress = exams.map(exam => ({
      examId: exam.id,
      examCode: exam.courseCode,
      status: 'pending' as const
    }));
    setProgress(initialProgress);
  }, [exams]);

  const updateProgress = (examId: string, updates: Partial<BulkAllocationProgress>) => {
    setProgress(prev => prev.map(p => 
      p.examId === examId ? { ...p, ...updates } : p
    ));
  };

  const processExam = async (exam: Exam): Promise<boolean> => {
    try {
      updateProgress(exam.id, { status: 'processing' });

      // Get students for the exam
      const students = await getStudentsForExam(exam.levelId, exam.courseId);
      updateProgress(exam.id, { studentCount: students.length });

      if (students.length === 0) {
        updateProgress(exam.id, { 
          status: 'error', 
          error: 'No students registered for this course' 
        });
        return false;
      }

      // Get halls for allocation
      const halls = await getHallsForAllocation(exam.selectedHalls);
      
      if (halls.length === 0) {
        updateProgress(exam.id, { 
          status: 'error', 
          error: 'No halls available for allocation' 
        });
        return false;
      }

      // Generate allocation
      const allocations = await generateHallAllocation(
        exam.id,
        students,
        halls,
        exam.hallCapacityOverride,
        allocationSettings
      );

      // Save allocations
      await saveHallAllocations(allocations);
      
      // Update exam status
      await updateExamAllocationStatus(exam.id, true);

      const allocatedCount = allocations.reduce((sum, alloc) => sum + alloc.allocatedCapacity, 0);
      updateProgress(exam.id, { 
        status: 'completed',
        allocatedCount
      });

      return true;
    } catch (error) {
      console.error(`Error processing exam ${exam.courseCode}:`, error);
      updateProgress(exam.id, { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  };

  const startBulkAllocation = async () => {
    setIsProcessing(true);
    setIsPaused(false);
    setCurrentIndex(0);

    const selectedExamList = exams.filter(exam => selectedExams.includes(exam.id));
    
    for (let i = currentIndex; i < selectedExamList.length; i++) {
      if (isPaused) {
        setCurrentIndex(i);
        break;
      }

      setCurrentIndex(i);
      const exam = selectedExamList[i];
      
      // Skip if already completed or has error
      const currentProgress = progress.find(p => p.examId === exam.id);
      if (currentProgress?.status === 'completed') {
        continue;
      }

      await processExam(exam);
      
      // Small delay between allocations
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    if (!isPaused) {
      setIsProcessing(false);
      setCurrentIndex(0);
    }
  };

  const pauseAllocation = () => {
    setIsPaused(true);
  };

  const resumeAllocation = () => {
    setIsPaused(false);
    startBulkAllocation();
  };

  const resetAllocation = () => {
    setIsProcessing(false);
    setIsPaused(false);
    setCurrentIndex(0);
    const resetProgress = exams.map(exam => ({
      examId: exam.id,
      examCode: exam.courseCode,
      status: 'pending' as const
    }));
    setProgress(resetProgress);
  };

  const toggleExamSelection = (examId: string) => {
    setSelectedExams(prev => 
      prev.includes(examId) 
        ? prev.filter(id => id !== examId)
        : [...prev, examId]
    );
  };

  const selectAllExams = () => {
    setSelectedExams(exams.map(e => e.id));
  };

  const deselectAllExams = () => {
    setSelectedExams([]);
  };

  const completedCount = progress.filter(p => p.status === 'completed').length;
  const errorCount = progress.filter(p => p.status === 'error').length;
  const totalSelected = selectedExams.length;
  const overallProgress = totalSelected > 0 ? (completedCount / totalSelected) * 100 : 0;

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Bulk Hall Allocation ({exams.length} exams)
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Allocate halls for multiple exams automatically
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-muted-foreground">
              {completedCount} of {totalSelected} completed
            </span>
          </div>
          <Progress value={overallProgress} className="w-full" />
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              {completedCount} completed
            </span>
            <span className="flex items-center gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              {errorCount} errors
            </span>
            <span className="flex items-center gap-1">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              {totalSelected - completedCount - errorCount} pending
            </span>
          </div>
        </div>

        {/* Allocation Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Allocation Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Mixed Department Priority</label>
                <Checkbox
                  checked={allocationSettings.prioritizeMixedDepartments}
                  onCheckedChange={(checked) => setAllocationSettings(prev => ({
                    ...prev,
                    prioritizeMixedDepartments: checked as boolean
                  }))}
                  disabled={isProcessing}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Group by Study Mode</label>
                <Checkbox
                  checked={allocationSettings.groupByStudyMode}
                  onCheckedChange={(checked) => setAllocationSettings(prev => ({
                    ...prev,
                    groupByStudyMode: checked as boolean
                  }))}
                  disabled={isProcessing}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Random Hall Selection</label>
                <Checkbox
                  checked={allocationSettings.randomHallSelection}
                  onCheckedChange={(checked) => setAllocationSettings(prev => ({
                    ...prev,
                    randomHallSelection: checked as boolean
                  }))}
                  disabled={isProcessing}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Allow Partial Allocation</label>
                <Checkbox
                  checked={allocationSettings.allowPartialAllocation}
                  onCheckedChange={(checked) => setAllocationSettings(prev => ({
                    ...prev,
                    allowPartialAllocation: checked as boolean
                  }))}
                  disabled={isProcessing}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Exam Selection */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Select Exams to Allocate</CardTitle>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={selectAllExams}
                  disabled={isProcessing}
                >
                  Select All
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={deselectAllExams}
                  disabled={isProcessing}
                >
                  Deselect All
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {exams.map((exam) => {
                const examProgress = progress.find(p => p.examId === exam.id);
                const isSelected = selectedExams.includes(exam.id);
                
                return (
                  <div key={exam.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleExamSelection(exam.id)}
                        disabled={isProcessing}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="font-mono text-xs">
                            {exam.courseCode}
                          </Badge>
                          <span className="font-medium">{exam.courseTitle}</span>
                          {exam.isAllocated && (
                            <Badge variant="secondary" className="text-xs">
                              Already Allocated
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          📅 {exam.examDate.toLocaleDateString()} • 
                          🕐 {exam.startTime} - {exam.endTime} • 
                          🏢 {exam.selectedHalls.length} halls
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {examProgress?.status === 'pending' && (
                        <Badge variant="outline">Pending</Badge>
                      )}
                      {examProgress?.status === 'processing' && (
                        <Badge variant="secondary" className="flex items-center gap-1">
                          <Loader2 className="h-3 w-3 animate-spin" />
                          Processing
                        </Badge>
                      )}
                      {examProgress?.status === 'completed' && (
                        <Badge variant="default" className="bg-green-600 flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Completed
                        </Badge>
                      )}
                      {examProgress?.status === 'error' && (
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3" />
                          Error
                        </Badge>
                      )}
                      
                      {examProgress?.studentCount && (
                        <span className="text-xs text-muted-foreground">
                          👥 {examProgress.allocatedCount || 0}/{examProgress.studentCount}
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Error Details */}
        {errorCount > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-red-600 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Allocation Errors ({errorCount})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {progress
                  .filter(p => p.status === 'error')
                  .map((p) => (
                    <div key={p.examId} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="font-mono text-xs">
                          {p.examCode}
                        </Badge>
                        <span className="font-medium text-red-800">Error</span>
                      </div>
                      <p className="text-sm text-red-700">{p.error}</p>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <div className="flex gap-4 pt-4">
          {!isProcessing && !isPaused && (
            <Button 
              onClick={startBulkAllocation}
              disabled={selectedExams.length === 0}
              className="flex-1"
            >
              <Play className="mr-2 h-4 w-4" />
              Start Bulk Allocation ({selectedExams.length} exams)
            </Button>
          )}

          {isProcessing && !isPaused && (
            <Button 
              onClick={pauseAllocation}
              variant="outline"
              className="flex-1"
            >
              <Pause className="mr-2 h-4 w-4" />
              Pause Allocation
            </Button>
          )}

          {isPaused && (
            <Button 
              onClick={resumeAllocation}
              className="flex-1"
            >
              <Play className="mr-2 h-4 w-4" />
              Resume Allocation
            </Button>
          )}

          <Button 
            onClick={resetAllocation}
            variant="outline"
            disabled={isProcessing && !isPaused}
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>

          <Button 
            onClick={completedCount > 0 ? onComplete : onCancel}
            variant={completedCount > 0 ? "default" : "outline"}
          >
            {completedCount > 0 ? `Complete (${completedCount} allocated)` : "Cancel"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
