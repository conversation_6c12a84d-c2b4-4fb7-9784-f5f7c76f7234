"use client";

import { useState, useEffect } from "react";
import { StudentLayout } from "@/components/student/student-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { TypographyH2, TypographyP } from "@/components/ui/typography";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Calendar, 
  Clock, 
  BookOpen,
  Search,
  Filter,
  RefreshCw,
  Loader2,
  Info,
  Building,
  MapPin,
  CheckCircle,
  AlertCircle
} from "lucide-react";

interface Exam {
  id: string;
  courseCode: string;
  courseTitle: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  duration: number;
  hallName?: string;
  seatNumber?: string;
  status: 'upcoming' | 'ongoing' | 'completed';
  isAllocated: boolean;
  instructions?: string;
}

export default function StudentExamsPage() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [filteredExams, setFilteredExams] = useState<Exam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  useEffect(() => {
    loadExams();
  }, []);

  useEffect(() => {
    filterExams();
  }, [exams, searchTerm, selectedStatus]);

  const loadExams = async () => {
    setIsLoading(true);
    try {
      // Simulate loading data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with real Firebase queries
      const mockExams: Exam[] = [
        {
          id: '1',
          courseCode: 'CSC201',
          courseTitle: 'Data Structures & Algorithms',
          examDate: new Date('2024-12-15'),
          startTime: '09:00',
          endTime: '12:00',
          duration: 180,
          hallName: 'Main Hall A',
          seatNumber: 'A-045',
          status: 'upcoming',
          isAllocated: true,
          instructions: 'Bring your student ID and writing materials. No electronic devices allowed.'
        },
        {
          id: '2',
          courseCode: 'CSC202',
          courseTitle: 'Database Management Systems',
          examDate: new Date('2024-12-17'),
          startTime: '14:00',
          endTime: '17:00',
          duration: 180,
          hallName: 'ICT Hall B',
          seatNumber: 'B-023',
          status: 'upcoming',
          isAllocated: true,
          instructions: 'This is a practical exam. Laptops will be provided.'
        },
        {
          id: '3',
          courseCode: 'MTH201',
          courseTitle: 'Advanced Mathematics',
          examDate: new Date('2024-12-20'),
          startTime: '09:00',
          endTime: '12:00',
          duration: 180,
          status: 'upcoming',
          isAllocated: false,
          instructions: 'Scientific calculators are allowed. Show all working clearly.'
        },
        {
          id: '4',
          courseCode: 'CSC101',
          courseTitle: 'Introduction to Computer Science',
          examDate: new Date('2024-12-05'),
          startTime: '09:00',
          endTime: '12:00',
          duration: 180,
          hallName: 'Main Hall A',
          seatNumber: 'A-089',
          status: 'completed',
          isAllocated: true,
          instructions: 'Multiple choice and essay questions. Use blue or black ink only.'
        }
      ];
      
      setExams(mockExams);
    } catch (error) {
      console.error('Error loading exams:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterExams = () => {
    let filtered = exams;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.courseCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.courseTitle.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (selectedStatus !== "all") {
      filtered = filtered.filter(exam => exam.status === selectedStatus);
    }

    setFilteredExams(filtered);
  };

  const getStatusBadge = (status: Exam['status']) => {
    switch (status) {
      case 'upcoming':
        return <Badge className="bg-blue-600">Upcoming</Badge>;
      case 'ongoing':
        return <Badge className="bg-green-600">Ongoing</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getAllocationBadge = (isAllocated: boolean) => {
    return isAllocated 
      ? <Badge variant="outline" className="text-green-600 border-green-600">Hall Allocated</Badge>
      : <Badge variant="outline" className="text-orange-600 border-orange-600">Pending Allocation</Badge>;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    return new Date(`2024-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const upcomingCount = exams.filter(e => e.status === 'upcoming').length;
  const allocatedCount = exams.filter(e => e.isAllocated).length;
  const completedCount = exams.filter(e => e.status === 'completed').length;

  return (
    <StudentLayout
      title="My Exams"
      description="View your exam schedule and details"
    >
      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 lg:gap-6 mb-8">
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{exams.length}</div>
            <p className="text-xs text-muted-foreground">This semester</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{upcomingCount}</div>
            <p className="text-xs text-muted-foreground">Scheduled</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Allocated</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{allocatedCount}</div>
            <p className="text-xs text-muted-foreground">Halls assigned</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{completedCount}</div>
            <p className="text-xs text-muted-foreground">Exams taken</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/80 backdrop-blur-sm mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <Button variant="outline" onClick={loadExams} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by course code or title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="upcoming">Upcoming</option>
              <option value="ongoing">Ongoing</option>
              <option value="completed">Completed</option>
            </select>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredExams.length} of {exams.length} exams
          </div>
        </CardContent>
      </Card>

      {/* Exams List */}
      {isLoading ? (
        <div className="text-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <TypographyP className="text-gray-600">Loading your exams...</TypographyP>
        </div>
      ) : filteredExams.length === 0 ? (
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <TypographyP className="text-gray-600 mb-2">
              {searchTerm || selectedStatus !== "all" 
                ? "No exams match your search criteria"
                : "No exams found"
              }
            </TypographyP>
            <TypographyP className="text-sm text-gray-500">
              Your exam schedule will appear here when available
            </TypographyP>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4 lg:space-y-6">
          {filteredExams.map((exam) => (
            <Card key={exam.id} className="bg-white/80 backdrop-blur-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-900">
                      {exam.courseCode} - {exam.courseTitle}
                    </CardTitle>
                    <TypographyP className="text-gray-600 mt-1">
                      {formatDate(exam.examDate)} • {formatTime(exam.startTime)} - {formatTime(exam.endTime)}
                    </TypographyP>
                  </div>
                  <div className="flex flex-wrap items-center gap-2">
                    {getStatusBadge(exam.status)}
                    {getAllocationBadge(exam.isAllocated)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Exam Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-gray-900">Duration: {exam.duration} minutes</p>
                      <p className="text-sm text-gray-600">3 hours examination</p>
                    </div>
                  </div>
                  
                  {exam.isAllocated && exam.hallName && (
                    <div className="flex items-center gap-3">
                      <Building className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium text-gray-900">{exam.hallName}</p>
                        {exam.seatNumber && (
                          <p className="text-sm text-gray-600">Seat: {exam.seatNumber}</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Instructions */}
                {exam.instructions && (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Instructions:</strong> {exam.instructions}
                    </AlertDescription>
                  </Alert>
                )}
                
                {/* Allocation Status */}
                {!exam.isAllocated && exam.status === 'upcoming' && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Hall allocation is pending. You will be notified once your exam venue is assigned.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </StudentLayout>
  );
}
