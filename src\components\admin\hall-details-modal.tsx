"use client";

import { <PERSON> } from "@/types/hall";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TypographyH3, TypographyP } from "@/components/ui/typography";
import { 
  X, 
  MapPin, 
  Users, 
  Calendar, 
  Settings, 
  Edit,
  Trash2,
  Building
} from "lucide-react";

interface HallDetailsModalProps {
  hall: Hall;
  onClose: () => void;
  onEdit: (hall: Hall) => void;
  onDelete: (hallId: string) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "active": return "bg-green-100 text-green-800 border-green-200";
    case "maintenance": return "bg-orange-100 text-orange-800 border-orange-200";
    case "unavailable": return "bg-red-100 text-red-800 border-red-200";
    case "reserved": return "bg-blue-100 text-blue-800 border-blue-200";
    default: return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case "examination_hall": return "🏛️";
    case "lecture_hall": return "🎓";
    case "auditorium": return "🎭";
    case "classroom": return "📚";
    case "laboratory": return "🔬";
    case "computer_lab": return "💻";
    default: return "🏢";
  }
};

export function HallDetailsModal({ hall, onClose, onEdit, onDelete }: HallDetailsModalProps) {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div className="bg-background rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <span className="text-3xl">{getTypeIcon(hall.type)}</span>
            <div>
              <TypographyH3 className="text-xl font-bold">{hall.name}</TypographyH3>
              <Badge variant="outline" className="font-mono text-sm mt-1">
                {hall.code}
              </Badge>
            </div>
          </div>
          
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Hall Name</label>
                  <p className="font-medium">{hall.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Hall Code</label>
                  <p className="font-mono font-medium">{hall.code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Capacity</label>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{hall.capacity} students</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location</label>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{hall.location}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Type</label>
                  <p className="font-medium capitalize">
                    {hall.type.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <Badge className={getStatusColor(hall.status)}>
                    {hall.status.charAt(0).toUpperCase() + hall.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Facilities */}
          {hall.facilities.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Facilities & Equipment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {hall.facilities.map((facility) => (
                    <Badge key={facility} variant="secondary">
                      {facility}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Description */}
          {hall.description && (
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <TypographyP className="text-muted-foreground">
                  {hall.description}
                </TypographyP>
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Record Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created</label>
                  <p className="text-sm">{hall.createdAt.toLocaleDateString()} at {hall.createdAt.toLocaleTimeString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                  <p className="text-sm">{hall.updatedAt.toLocaleDateString()} at {hall.updatedAt.toLocaleTimeString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created By</label>
                  <p className="text-sm font-mono">{hall.createdBy}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t border-border bg-muted/30">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => onEdit(hall)}
              className="text-blue-600 hover:text-blue-700"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit Hall
            </Button>
            <Button 
              variant="outline" 
              onClick={() => onDelete(hall.id)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Hall
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
