// Examination Management Service for Firestore
import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  getDoc,
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  where,
  Timestamp,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import { 
  AcademicSession,
  Semester,
  Course,
  Exam,
  StudentCourseRegistration,
  HallAllocation,
  ExamConflict,
  SessionFormData,
  SemesterFormData,
  CourseFormData,
  ExamFormData,
  calculateExamDuration
} from '@/types/exam';

const SESSIONS_COLLECTION = 'academic_sessions';
const SEMESTERS_COLLECTION = 'semesters';
const COURSES_COLLECTION = 'courses';
const EXAMS_COLLECTION = 'exams';
const STUDENT_COURSES_COLLECTION = 'student_course_registrations';
const HALL_ALLOCATIONS_COLLECTION = 'hall_allocations';

// Test Firebase connection
export const testFirebaseConnection = async (): Promise<boolean> => {
  try {
    console.log('🔥 Testing Firebase connection...');

    // Try to read from a collection (this will work even if empty)
    const testQuery = query(collection(db, SESSIONS_COLLECTION));
    await getDocs(testQuery);

    console.log('✅ Firebase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    return false;
  }
};

// Convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
};

// ACADEMIC SESSION OPERATIONS
export const addAcademicSession = async (sessionData: SessionFormData, createdBy: string): Promise<AcademicSession> => {
  try {
    console.log('📅 Adding academic session to Firestore:', sessionData);

    const batch = writeBatch(db);

    // Create the session
    const sessionDocRef = doc(collection(db, SESSIONS_COLLECTION));
    const sessionDoc = {
      ...sessionData,
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };
    batch.set(sessionDocRef, sessionDoc);

    // Automatically create First and Second semesters
    const firstSemesterRef = doc(collection(db, SEMESTERS_COLLECTION));
    const secondSemesterRef = doc(collection(db, SEMESTERS_COLLECTION));

    // Calculate semester dates (split the session into two halves)
    const sessionStart = new Date(sessionData.startDate);
    const sessionEnd = new Date(sessionData.endDate);
    const sessionMidpoint = new Date((sessionStart.getTime() + sessionEnd.getTime()) / 2);

    const firstSemesterDoc = {
      sessionId: sessionDocRef.id,
      sessionName: sessionData.name,
      name: "First Semester",
      code: "1ST",
      startDate: Timestamp.fromDate(sessionStart),
      endDate: Timestamp.fromDate(sessionMidpoint),
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };

    const secondSemesterDoc = {
      sessionId: sessionDocRef.id,
      sessionName: sessionData.name,
      name: "Second Semester",
      code: "2ND",
      startDate: Timestamp.fromDate(sessionMidpoint),
      endDate: Timestamp.fromDate(sessionEnd),
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };

    batch.set(firstSemesterRef, firstSemesterDoc);
    batch.set(secondSemesterRef, secondSemesterDoc);

    // Commit the batch
    await batch.commit();

    // Return the created session
    const createdDoc = await getDoc(sessionDocRef);
    if (createdDoc.exists()) {
      const session: AcademicSession = {
        id: sessionDocRef.id,
        ...sessionData,
        isActive: true,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };
      console.log('✅ Academic session added successfully with semesters:', session.name);
      return session;
    }

    throw new Error('Failed to retrieve created session');
  } catch (error) {
    console.error('❌ Error adding academic session:', error);
    throw new Error(`Failed to add session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getAllAcademicSessions = async (): Promise<AcademicSession[]> => {
  try {
    console.log('📅 Fetching all academic sessions from Firestore...');

    // Simple query without orderBy to avoid index issues initially
    const q = query(collection(db, SESSIONS_COLLECTION));

    const querySnapshot = await getDocs(q);
    const sessions: AcademicSession[] = [];

    if (querySnapshot.empty) {
      console.log('ℹ️ No academic sessions found in database (collection may be empty)');
      return [];
    }

    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        sessions.push({
          id: doc.id,
          name: data.name || '',
          startDate: convertTimestamp(data.startDate),
          endDate: convertTimestamp(data.endDate),
          isActive: data.isActive !== false, // Default to true if not specified
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt),
          createdBy: data.createdBy || ''
        });
      } catch (docError) {
        console.warn(`⚠️ Error processing session document ${doc.id}:`, docError);
        // Skip this document and continue
      }
    });

    // Sort by name (client-side)
    sessions.sort((a, b) => b.name.localeCompare(a.name));

    console.log(`✅ Retrieved ${sessions.length} academic sessions from Firestore`);
    return sessions;
  } catch (error) {
    console.error('❌ Error fetching academic sessions:', error);
    // Return empty array instead of throwing to prevent app crash
    console.warn('⚠️ Returning empty array due to error');
    return [];
  }
};

// SEMESTER OPERATIONS
export const addSemester = async (semesterData: SemesterFormData, createdBy: string): Promise<Semester> => {
  try {
    console.log('📚 Adding semester to Firestore:', semesterData);
    
    // Get session information
    const sessionDoc = await getDoc(doc(db, SESSIONS_COLLECTION, semesterData.sessionId));
    if (!sessionDoc.exists()) {
      throw new Error('Academic session not found');
    }
    
    const sessionInfo = sessionDoc.data();
    
    const semesterDoc = {
      ...semesterData,
      sessionName: sessionInfo.name,
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };
    
    const docRef = await addDoc(collection(db, SEMESTERS_COLLECTION), semesterDoc);
    const createdDoc = await getDoc(docRef);
    
    if (createdDoc.exists()) {
      const semester: Semester = {
        id: docRef.id,
        ...semesterData,
        sessionName: sessionInfo.name,
        isActive: true,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };
      console.log('✅ Semester added successfully:', semester.name);
      return semester;
    }
    
    throw new Error('Failed to retrieve created semester');
  } catch (error) {
    console.error('❌ Error adding semester:', error);
    throw new Error(`Failed to add semester: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getSemestersBySession = async (sessionId: string): Promise<Semester[]> => {
  try {
    console.log('📚 Fetching semesters for session:', sessionId);
    
    const q = query(
      collection(db, SEMESTERS_COLLECTION),
      where('sessionId', '==', sessionId)
    );
    
    const querySnapshot = await getDocs(q);
    const semesters: Semester[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      semesters.push({
        id: doc.id,
        sessionId: data.sessionId,
        sessionName: data.sessionName,
        name: data.name,
        code: data.code,
        startDate: convertTimestamp(data.startDate),
        endDate: convertTimestamp(data.endDate),
        isActive: data.isActive,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });
    
    // Sort by code to ensure First Semester comes before Second Semester
    semesters.sort((a, b) => a.code.localeCompare(b.code));
    
    console.log(`✅ Retrieved ${semesters.length} semesters for session`);
    return semesters;
  } catch (error) {
    console.error('❌ Error fetching semesters:', error);
    throw new Error(`Failed to fetch semesters: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Helper function to ensure semesters exist for a session (for backward compatibility)
export const ensureSemestersExist = async (sessionId: string, createdBy: string): Promise<void> => {
  try {
    console.log('🔍 Checking if semesters exist for session:', sessionId);

    const existingSemesters = await getSemestersBySession(sessionId);
    if (existingSemesters.length >= 2) {
      console.log('✅ Semesters already exist for session');
      return;
    }

    // Get session info
    const sessionDoc = await getDoc(doc(db, SESSIONS_COLLECTION, sessionId));
    if (!sessionDoc.exists()) {
      throw new Error('Session not found');
    }

    const sessionData = sessionDoc.data();
    const batch = writeBatch(db);

    // Calculate semester dates
    const sessionStart = convertTimestamp(sessionData.startDate);
    const sessionEnd = convertTimestamp(sessionData.endDate);
    const sessionMidpoint = new Date((sessionStart.getTime() + sessionEnd.getTime()) / 2);

    // Create missing semesters
    const hasFirstSemester = existingSemesters.some(s => s.code === '1ST');
    const hasSecondSemester = existingSemesters.some(s => s.code === '2ND');

    if (!hasFirstSemester) {
      const firstSemesterRef = doc(collection(db, SEMESTERS_COLLECTION));
      const firstSemesterDoc = {
        sessionId: sessionId,
        sessionName: sessionData.name,
        name: "First Semester",
        code: "1ST",
        startDate: Timestamp.fromDate(sessionStart),
        endDate: Timestamp.fromDate(sessionMidpoint),
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy
      };
      batch.set(firstSemesterRef, firstSemesterDoc);
    }

    if (!hasSecondSemester) {
      const secondSemesterRef = doc(collection(db, SEMESTERS_COLLECTION));
      const secondSemesterDoc = {
        sessionId: sessionId,
        sessionName: sessionData.name,
        name: "Second Semester",
        code: "2ND",
        startDate: Timestamp.fromDate(sessionMidpoint),
        endDate: Timestamp.fromDate(sessionEnd),
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy
      };
      batch.set(secondSemesterRef, secondSemesterDoc);
    }

    await batch.commit();
    console.log('✅ Missing semesters created for session');
  } catch (error) {
    console.error('❌ Error ensuring semesters exist:', error);
    throw new Error(`Failed to ensure semesters exist: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// COURSE OPERATIONS
export const addCourse = async (courseData: CourseFormData, createdBy: string): Promise<Course> => {
  try {
    console.log('📖 Adding course to Firestore:', courseData);
    
    // Get department information
    const departmentDoc = await getDoc(doc(db, 'departments', courseData.departmentId));
    if (!departmentDoc.exists()) {
      throw new Error('Department not found');
    }
    
    const departmentInfo = departmentDoc.data();
    
    const courseDoc = {
      ...courseData,
      code: courseData.code.toUpperCase(),
      departmentName: departmentInfo.name,
      facultyId: departmentInfo.facultyId,
      facultyName: departmentInfo.facultyName,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };
    
    const docRef = await addDoc(collection(db, COURSES_COLLECTION), courseDoc);
    const createdDoc = await getDoc(docRef);
    
    if (createdDoc.exists()) {
      const course: Course = {
        id: docRef.id,
        ...courseData,
        code: courseData.code.toUpperCase(),
        departmentName: departmentInfo.name,
        facultyId: departmentInfo.facultyId,
        facultyName: departmentInfo.facultyName,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };
      console.log('✅ Course added successfully:', course.code);
      return course;
    }
    
    throw new Error('Failed to retrieve created course');
  } catch (error) {
    console.error('❌ Error adding course:', error);
    throw new Error(`Failed to add course: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getCoursesByDepartment = async (departmentId: string): Promise<Course[]> => {
  try {
    console.log('📖 Fetching courses for department:', departmentId);
    
    const q = query(
      collection(db, COURSES_COLLECTION),
      where('departmentId', '==', departmentId)
    );
    
    const querySnapshot = await getDocs(q);
    const courses: Course[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      courses.push({
        id: doc.id,
        code: data.code,
        title: data.title,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        facultyId: data.facultyId,
        facultyName: data.facultyName,
        isElective: data.isElective,
        creditUnits: data.creditUnits,
        description: data.description,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });
    
    // Sort by course code
    courses.sort((a, b) => a.code.localeCompare(b.code));
    
    console.log(`✅ Retrieved ${courses.length} courses for department`);
    return courses;
  } catch (error) {
    console.error('❌ Error fetching courses:', error);
    throw new Error(`Failed to fetch courses: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Check if course code is unique within department
export const isCourseCodeUnique = async (code: string, departmentId: string, excludeId?: string): Promise<boolean> => {
  try {
    const q = query(
      collection(db, COURSES_COLLECTION),
      where('code', '==', code.toUpperCase()),
      where('departmentId', '==', departmentId)
    );

    const querySnapshot = await getDocs(q);

    if (excludeId) {
      const otherCourses = querySnapshot.docs.filter(doc => doc.id !== excludeId);
      return otherCourses.length === 0;
    }

    return querySnapshot.empty;
  } catch (error) {
    console.error('❌ Error checking course code uniqueness:', error);
    return false;
  }
};

// EXAM OPERATIONS
export const addExam = async (examData: ExamFormData, createdBy: string): Promise<Exam> => {
  try {
    console.log('📝 Adding exam to Firestore:', examData);

    // Get related data for the exam
    const [sessionDoc, semesterDoc, facultyDoc, departmentDoc, levelDoc, courseDoc] = await Promise.all([
      getDoc(doc(db, SESSIONS_COLLECTION, examData.sessionId)),
      getDoc(doc(db, SEMESTERS_COLLECTION, examData.semesterId)),
      getDoc(doc(db, 'faculties', examData.facultyId)),
      getDoc(doc(db, 'departments', examData.departmentId)),
      getDoc(doc(db, 'levels', examData.levelId)),
      getDoc(doc(db, COURSES_COLLECTION, examData.courseId))
    ]);

    if (!sessionDoc.exists()) throw new Error('Academic session not found');
    if (!semesterDoc.exists()) throw new Error('Semester not found');
    if (!facultyDoc.exists()) throw new Error('Faculty not found');
    if (!departmentDoc.exists()) throw new Error('Department not found');
    if (!levelDoc.exists()) throw new Error('Level not found');
    if (!courseDoc.exists()) throw new Error('Course not found');

    const sessionInfo = sessionDoc.data();
    const semesterInfo = semesterDoc.data();
    const facultyInfo = facultyDoc.data();
    const departmentInfo = departmentDoc.data();
    const levelInfo = levelDoc.data();
    const courseInfo = courseDoc.data();

    // Calculate duration
    const duration = calculateExamDuration(examData.startTime, examData.endTime);

    const examDoc = {
      courseId: examData.courseId,
      courseCode: courseInfo.code,
      courseTitle: courseInfo.title,
      sessionId: examData.sessionId,
      sessionName: sessionInfo.name,
      semesterId: examData.semesterId,
      semesterName: semesterInfo.name,
      facultyId: examData.facultyId,
      facultyName: facultyInfo.name,
      departmentId: examData.departmentId,
      departmentName: departmentInfo.name,
      levelId: examData.levelId,
      levelName: levelInfo.name,
      examDate: Timestamp.fromDate(examData.examDate),
      startTime: examData.startTime,
      endTime: examData.endTime,
      duration,
      selectedHalls: examData.selectedHalls,
      hallCapacityOverride: examData.hallCapacityOverride || {},
      status: 'draft' as const,
      isAllocated: false,
      isApproved: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };

    const docRef = await addDoc(collection(db, EXAMS_COLLECTION), examDoc);
    const createdDoc = await getDoc(docRef);

    if (createdDoc.exists()) {
      const exam: Exam = {
        id: docRef.id,
        courseId: examData.courseId,
        courseCode: courseInfo.code,
        courseTitle: courseInfo.title,
        sessionId: examData.sessionId,
        sessionName: sessionInfo.name,
        semesterId: examData.semesterId,
        semesterName: semesterInfo.name,
        facultyId: examData.facultyId,
        facultyName: facultyInfo.name,
        departmentId: examData.departmentId,
        departmentName: departmentInfo.name,
        levelId: examData.levelId,
        levelName: levelInfo.name,
        examDate: examData.examDate,
        startTime: examData.startTime,
        endTime: examData.endTime,
        duration,
        selectedHalls: examData.selectedHalls,
        hallCapacityOverride: examData.hallCapacityOverride || {},
        status: 'draft',
        isAllocated: false,
        isApproved: false,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };
      console.log('✅ Exam added successfully:', exam.courseCode);
      return exam;
    }

    throw new Error('Failed to retrieve created exam');
  } catch (error) {
    console.error('❌ Error adding exam:', error);
    throw new Error(`Failed to add exam: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getAllExams = async (): Promise<Exam[]> => {
  try {
    console.log('📝 Fetching all exams from Firestore...');

    // Simple query without orderBy to avoid index issues initially
    const q = query(collection(db, EXAMS_COLLECTION));

    const querySnapshot = await getDocs(q);
    const exams: Exam[] = [];

    if (querySnapshot.empty) {
      console.log('ℹ️ No exams found in database (collection may be empty)');
      return [];
    }

    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        exams.push({
          id: doc.id,
          courseId: data.courseId || '',
          courseCode: data.courseCode || '',
          courseTitle: data.courseTitle || '',
          sessionId: data.sessionId || '',
          sessionName: data.sessionName || '',
          semesterId: data.semesterId || '',
          semesterName: data.semesterName || '',
          facultyId: data.facultyId || '',
          facultyName: data.facultyName || '',
          departmentId: data.departmentId || '',
          departmentName: data.departmentName || '',
          levelId: data.levelId || '',
          levelName: data.levelName || '',
          examDate: convertTimestamp(data.examDate),
          startTime: data.startTime || '',
          endTime: data.endTime || '',
          duration: data.duration || 0,
          selectedHalls: data.selectedHalls || [],
          hallCapacityOverride: data.hallCapacityOverride || {},
          status: data.status || 'draft',
          studentCount: data.studentCount,
          isAllocated: data.isAllocated || false,
          isApproved: data.isApproved || false,
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt),
          createdBy: data.createdBy || ''
        });
      } catch (docError) {
        console.warn(`⚠️ Error processing exam document ${doc.id}:`, docError);
        // Skip this document and continue
      }
    });

    // Sort exams by date and time (client-side to avoid index issues)
    exams.sort((a, b) => {
      const dateCompare = b.examDate.getTime() - a.examDate.getTime();
      if (dateCompare !== 0) return dateCompare;
      return a.startTime.localeCompare(b.startTime);
    });

    console.log(`✅ Retrieved ${exams.length} exams from Firestore`);
    return exams;
  } catch (error) {
    console.error('❌ Error fetching exams:', error);
    // Return empty array instead of throwing to prevent app crash
    console.warn('⚠️ Returning empty array due to error');
    return [];
  }
};

export const getExamsBySession = async (sessionId: string, semesterId?: string): Promise<Exam[]> => {
  try {
    console.log('📝 Fetching exams for session:', sessionId, semesterId ? `semester: ${semesterId}` : '');

    let q = query(
      collection(db, EXAMS_COLLECTION),
      where('sessionId', '==', sessionId)
    );

    if (semesterId) {
      q = query(q, where('semesterId', '==', semesterId));
    }

    // Remove multiple orderBy to avoid index issues - sort in memory instead
    q = query(q, orderBy('createdAt', 'desc'));

    const querySnapshot = await getDocs(q);
    const exams: Exam[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      exams.push({
        id: doc.id,
        courseId: data.courseId,
        courseCode: data.courseCode,
        courseTitle: data.courseTitle,
        sessionId: data.sessionId,
        sessionName: data.sessionName,
        semesterId: data.semesterId,
        semesterName: data.semesterName,
        facultyId: data.facultyId,
        facultyName: data.facultyName,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        levelId: data.levelId,
        levelName: data.levelName,
        examDate: convertTimestamp(data.examDate),
        startTime: data.startTime,
        endTime: data.endTime,
        duration: data.duration,
        selectedHalls: data.selectedHalls || [],
        hallCapacityOverride: data.hallCapacityOverride || {},
        status: data.status,
        studentCount: data.studentCount,
        isAllocated: data.isAllocated || false,
        isApproved: data.isApproved || false,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    // Sort exams by date and time (client-side to avoid index issues)
    exams.sort((a, b) => {
      const dateCompare = a.examDate.getTime() - b.examDate.getTime();
      if (dateCompare !== 0) return dateCompare;
      return a.startTime.localeCompare(b.startTime);
    });

    console.log(`✅ Retrieved ${exams.length} exams for session`);
    return exams;
  } catch (error) {
    console.error('❌ Error fetching exams by session:', error);
    throw new Error(`Failed to fetch exams: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// CONFLICT DETECTION
export const checkExamConflicts = async (examData: ExamFormData, excludeExamId?: string): Promise<ExamConflict[]> => {
  try {
    console.log('🔍 Checking exam conflicts...');

    const conflicts: ExamConflict[] = [];

    // Get exams for the same date (direct query to avoid recursion)
    const examDateStr = examData.examDate.toISOString().split('T')[0];
    const startOfDay = new Date(examDateStr);
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    const q = query(
      collection(db, EXAMS_COLLECTION),
      where('examDate', '>=', startOfDay),
      where('examDate', '<', endOfDay)
    );

    const querySnapshot = await getDocs(q);
    const sameDate: any[] = [];

    querySnapshot.forEach((doc) => {
      if (excludeExamId && doc.id === excludeExamId) return;

      const data = doc.data();
      sameDate.push({
        id: doc.id,
        startTime: data.startTime,
        endTime: data.endTime,
        selectedHalls: data.selectedHalls || [],
        courseCode: data.courseCode,
        courseTitle: data.courseTitle,
        levelId: data.levelId,
        departmentId: data.departmentId
      });
    });

    // Check for hall conflicts
    for (const existingExam of sameDate) {
      const timeOverlap = checkTimeOverlap(
        examData.startTime, examData.endTime,
        existingExam.startTime, existingExam.endTime
      );

      if (timeOverlap) {
        // Check if any halls are shared
        const sharedHalls = examData.selectedHalls.filter(hallId =>
          existingExam.selectedHalls.includes(hallId)
        );

        if (sharedHalls.length > 0) {
          conflicts.push({
            type: 'hall_time_conflict',
            message: `Hall conflict detected with ${existingExam.courseCode} exam`,
            examId: examData.courseId,
            examTitle: `${examData.courseId} exam`,
            conflictingExamId: existingExam.id,
            conflictingExamTitle: `${existingExam.courseCode} - ${existingExam.courseTitle}`,
            hallId: sharedHalls[0],
            suggestions: [`Consider using different halls or changing the time`]
          });
        }

        // Check for student conflicts (same level taking different courses at same time)
        if (examData.levelId === existingExam.levelId && examData.departmentId === existingExam.departmentId) {
          conflicts.push({
            type: 'student_time_conflict',
            message: `Students from ${existingExam.levelName} may have conflicting exams`,
            examId: examData.courseId,
            examTitle: `${examData.courseId} exam`,
            conflictingExamId: existingExam.id,
            conflictingExamTitle: `${existingExam.courseCode} - ${existingExam.courseTitle}`,
            suggestions: [`Change exam time or verify student enrollment`]
          });
        }
      }
    }

    // Validate time range
    if (examData.startTime && examData.endTime) {
      const start = new Date(`2000-01-01 ${examData.startTime}`);
      const end = new Date(`2000-01-01 ${examData.endTime}`);
      if (end <= start) {
        conflicts.push({
          type: 'invalid_time_range',
          message: 'End time must be after start time',
          examId: examData.courseId,
          examTitle: `${examData.courseId} exam`,
          suggestions: ['Adjust the exam time range']
        });
      }
    }

    // Check if halls are selected
    if (examData.selectedHalls.length === 0) {
      conflicts.push({
        type: 'no_halls_selected',
        message: 'No halls selected for the exam',
        examId: examData.courseId,
        examTitle: `${examData.courseId} exam`,
        suggestions: ['Select at least one hall for the exam']
      });
    }

    console.log(`🔍 Found ${conflicts.length} conflicts`);
    return conflicts;
  } catch (error) {
    console.error('❌ Error checking conflicts:', error);
    throw new Error(`Failed to check conflicts: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Helper function to check time overlap
const checkTimeOverlap = (start1: string, end1: string, start2: string, end2: string): boolean => {
  const startTime1 = new Date(`2000-01-01 ${start1}`);
  const endTime1 = new Date(`2000-01-01 ${end1}`);
  const startTime2 = new Date(`2000-01-01 ${start2}`);
  const endTime2 = new Date(`2000-01-01 ${end2}`);

  return startTime1 < endTime2 && startTime2 < endTime1;
};

// Get student count for exam
export const getStudentCountForExam = async (levelId: string, courseId: string): Promise<number> => {
  try {
    console.log('👥 Counting students for exam...');

    // Check if the collection exists and has documents
    const q = query(
      collection(db, STUDENT_COURSES_COLLECTION),
      where('levelId', '==', levelId),
      where('courseId', '==', courseId),
      where('status', '==', 'registered')
    );

    const querySnapshot = await getDocs(q);
    const count = querySnapshot.size;

    console.log(`👥 Found ${count} students registered for the course`);
    return count;
  } catch (error) {
    console.warn('⚠️ Could not count students (collection may not exist yet):', error);
    // Return 0 if the collection doesn't exist or there's an error
    // This is expected when the student course registration system isn't implemented yet
    return 0;
  }
};

// Update exam with student count
export const updateExamStudentCount = async (examId: string): Promise<void> => {
  try {
    const examDoc = await getDoc(doc(db, EXAMS_COLLECTION, examId));
    if (!examDoc.exists()) {
      throw new Error('Exam not found');
    }

    const examData = examDoc.data();
    const studentCount = await getStudentCountForExam(examData.levelId, examData.courseId);

    await updateDoc(doc(db, EXAMS_COLLECTION, examId), {
      studentCount,
      updatedAt: serverTimestamp()
    });

    console.log(`✅ Updated exam ${examId} with student count: ${studentCount}`);
  } catch (error) {
    console.error('❌ Error updating exam student count:', error);
    throw new Error(`Failed to update student count: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
