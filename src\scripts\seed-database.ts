// Database Seeding Script for Hall Automata
// Run this script to populate Firestore with comprehensive test data

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, writeBatch, doc } from 'firebase/firestore';
import { firebaseConfig } from '../lib/firebase/config';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Data structures for seeding
interface SeedFaculty {
  name: string;
  code: string;
  departments: SeedDepartment[];
}

interface SeedDepartment {
  name: string;
  code: string;
  levels: SeedLevel[];
}

interface SeedLevel {
  name: string;
  code: string;
  courses: SeedCourse[];
}

interface SeedCourse {
  title: string;
  code: string;
  isElective: boolean;
}

// Comprehensive test data structure
const SEED_DATA: SeedFaculty[] = [
  {
    name: "Faculty of Engineering",
    code: "ENG",
    departments: [
      {
        name: "Computer Science",
        code: "CSC",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Introduction to Computer Science", code: "CSC101", isElective: false },
              { title: "Programming Fundamentals", code: "CSC102", isElective: false },
              { title: "Computer Mathematics", code: "CSC103", isElective: false },
              { title: "Digital Logic Design", code: "CSC104", isElective: false },
              { title: "Technical English", code: "ENG101", isElective: false },
              { title: "Web Development Basics", code: "CSC105", isElective: true },
              { title: "Database Fundamentals", code: "CSC106", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Data Structures & Algorithms", code: "CSC201", isElective: false },
              { title: "Object Oriented Programming", code: "CSC202", isElective: false },
              { title: "Computer Networks", code: "CSC203", isElective: false },
              { title: "Software Engineering", code: "CSC204", isElective: false },
              { title: "Operating Systems", code: "CSC205", isElective: false },
              { title: "Mobile App Development", code: "CSC206", isElective: true },
              { title: "Artificial Intelligence", code: "CSC207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Programming", code: "CSC301", isElective: false },
              { title: "System Analysis & Design", code: "CSC302", isElective: false },
              { title: "Computer Graphics", code: "CSC303", isElective: false },
              { title: "Cybersecurity Fundamentals", code: "CSC304", isElective: false },
              { title: "Project Management", code: "CSC305", isElective: false },
              { title: "Machine Learning", code: "CSC306", isElective: true },
              { title: "Cloud Computing", code: "CSC307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "CSC401", isElective: false },
              { title: "Advanced Database Systems", code: "CSC402", isElective: false },
              { title: "Software Testing", code: "CSC403", isElective: false },
              { title: "Enterprise Systems", code: "CSC404", isElective: false },
              { title: "Research Methodology", code: "CSC405", isElective: false },
              { title: "Blockchain Technology", code: "CSC406", isElective: true },
              { title: "IoT Systems", code: "CSC407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Electrical Engineering",
        code: "EEE",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Circuit Analysis I", code: "EEE101", isElective: false },
              { title: "Engineering Mathematics I", code: "EEE102", isElective: false },
              { title: "Basic Electronics", code: "EEE103", isElective: false },
              { title: "Engineering Drawing", code: "EEE104", isElective: false },
              { title: "Physics for Engineers", code: "EEE105", isElective: false },
              { title: "Digital Electronics", code: "EEE106", isElective: true },
              { title: "Workshop Practice", code: "EEE107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Circuit Analysis II", code: "EEE201", isElective: false },
              { title: "Electromagnetic Fields", code: "EEE202", isElective: false },
              { title: "Power Systems I", code: "EEE203", isElective: false },
              { title: "Control Systems", code: "EEE204", isElective: false },
              { title: "Electrical Machines I", code: "EEE205", isElective: false },
              { title: "Renewable Energy", code: "EEE206", isElective: true },
              { title: "Instrumentation", code: "EEE207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Power Systems", code: "EEE301", isElective: false },
              { title: "Electrical Machines II", code: "EEE302", isElective: false },
              { title: "Power Electronics", code: "EEE303", isElective: false },
              { title: "High Voltage Engineering", code: "EEE304", isElective: false },
              { title: "Project Management", code: "EEE305", isElective: false },
              { title: "Smart Grid Technology", code: "EEE306", isElective: true },
              { title: "Electric Vehicle Systems", code: "EEE307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "EEE401", isElective: false },
              { title: "Power System Protection", code: "EEE402", isElective: false },
              { title: "Industrial Automation", code: "EEE403", isElective: false },
              { title: "Energy Management", code: "EEE404", isElective: false },
              { title: "Professional Practice", code: "EEE405", isElective: false },
              { title: "Microgrids", code: "EEE406", isElective: true },
              { title: "Power Quality", code: "EEE407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Mechanical Engineering",
        code: "MEE",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Engineering Mechanics", code: "MEE101", isElective: false },
              { title: "Engineering Materials", code: "MEE102", isElective: false },
              { title: "Thermodynamics I", code: "MEE103", isElective: false },
              { title: "Engineering Drawing", code: "MEE104", isElective: false },
              { title: "Workshop Technology", code: "MEE105", isElective: false },
              { title: "CAD Fundamentals", code: "MEE106", isElective: true },
              { title: "Manufacturing Processes", code: "MEE107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Strength of Materials", code: "MEE201", isElective: false },
              { title: "Fluid Mechanics", code: "MEE202", isElective: false },
              { title: "Thermodynamics II", code: "MEE203", isElective: false },
              { title: "Machine Design I", code: "MEE204", isElective: false },
              { title: "Heat Transfer", code: "MEE205", isElective: false },
              { title: "Automotive Engineering", code: "MEE206", isElective: true },
              { title: "Robotics Fundamentals", code: "MEE207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Machine Design", code: "MEE301", isElective: false },
              { title: "Internal Combustion Engines", code: "MEE302", isElective: false },
              { title: "Refrigeration & AC", code: "MEE303", isElective: false },
              { title: "Industrial Engineering", code: "MEE304", isElective: false },
              { title: "Vibration Analysis", code: "MEE305", isElective: false },
              { title: "Renewable Energy Systems", code: "MEE306", isElective: true },
              { title: "Advanced Manufacturing", code: "MEE307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "MEE401", isElective: false },
              { title: "Power Plant Engineering", code: "MEE402", isElective: false },
              { title: "Quality Control", code: "MEE403", isElective: false },
              { title: "Maintenance Engineering", code: "MEE404", isElective: false },
              { title: "Engineering Management", code: "MEE405", isElective: false },
              { title: "Mechatronics", code: "MEE406", isElective: true },
              { title: "Additive Manufacturing", code: "MEE407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Civil Engineering",
        code: "CVE",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Engineering Surveying I", code: "CVE101", isElective: false },
              { title: "Building Construction I", code: "CVE102", isElective: false },
              { title: "Structural Analysis I", code: "CVE103", isElective: false },
              { title: "Engineering Materials", code: "CVE104", isElective: false },
              { title: "Engineering Drawing", code: "CVE105", isElective: false },
              { title: "Environmental Engineering", code: "CVE106", isElective: true },
              { title: "GIS Fundamentals", code: "CVE107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Structural Analysis II", code: "CVE201", isElective: false },
              { title: "Concrete Technology", code: "CVE202", isElective: false },
              { title: "Soil Mechanics", code: "CVE203", isElective: false },
              { title: "Highway Engineering", code: "CVE204", isElective: false },
              { title: "Hydraulics", code: "CVE205", isElective: false },
              { title: "Coastal Engineering", code: "CVE206", isElective: true },
              { title: "Urban Planning", code: "CVE207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Structural Design", code: "CVE301", isElective: false },
              { title: "Foundation Engineering", code: "CVE302", isElective: false },
              { title: "Water Resources Engineering", code: "CVE303", isElective: false },
              { title: "Construction Management", code: "CVE304", isElective: false },
              { title: "Steel Structures", code: "CVE305", isElective: false },
              { title: "Earthquake Engineering", code: "CVE306", isElective: true },
              { title: "Sustainable Construction", code: "CVE307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "CVE401", isElective: false },
              { title: "Advanced Concrete Design", code: "CVE402", isElective: false },
              { title: "Project Planning & Control", code: "CVE403", isElective: false },
              { title: "Infrastructure Management", code: "CVE404", isElective: false },
              { title: "Professional Practice", code: "CVE405", isElective: false },
              { title: "Smart Cities", code: "CVE406", isElective: true },
              { title: "Climate Change Adaptation", code: "CVE407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Architecture",
        code: "ARC",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Architectural Design I", code: "ARC101", isElective: false },
              { title: "Building Construction I", code: "ARC102", isElective: false },
              { title: "Architectural Drawing I", code: "ARC103", isElective: false },
              { title: "History of Architecture", code: "ARC104", isElective: false },
              { title: "Building Materials", code: "ARC105", isElective: false },
              { title: "Computer Aided Design", code: "ARC106", isElective: true },
              { title: "Photography for Architects", code: "ARC107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Architectural Design II", code: "ARC201", isElective: false },
              { title: "Building Construction II", code: "ARC202", isElective: false },
              { title: "Structural Systems", code: "ARC203", isElective: false },
              { title: "Environmental Design", code: "ARC204", isElective: false },
              { title: "Building Services", code: "ARC205", isElective: false },
              { title: "Landscape Architecture", code: "ARC206", isElective: true },
              { title: "Interior Design", code: "ARC207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Architectural Design", code: "ARC301", isElective: false },
              { title: "Urban Design", code: "ARC302", isElective: false },
              { title: "Construction Technology", code: "ARC303", isElective: false },
              { title: "Building Regulations", code: "ARC304", isElective: false },
              { title: "Quantity Surveying", code: "ARC305", isElective: false },
              { title: "Green Building Design", code: "ARC306", isElective: true },
              { title: "Heritage Conservation", code: "ARC307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "ARC401", isElective: false },
              { title: "Professional Practice", code: "ARC402", isElective: false },
              { title: "Project Management", code: "ARC403", isElective: false },
              { title: "Advanced Building Technology", code: "ARC404", isElective: false },
              { title: "Research Methods", code: "ARC405", isElective: false },
              { title: "Smart Buildings", code: "ARC406", isElective: true },
              { title: "Parametric Design", code: "ARC407", isElective: true }
            ]
          }
        ]
      }
    ]
  },
  {
    name: "Faculty of Science",
    code: "SCI",
    departments: [
      {
        name: "Mathematics",
        code: "MTH",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Calculus I", code: "MTH101", isElective: false },
              { title: "Linear Algebra", code: "MTH102", isElective: false },
              { title: "Statistics I", code: "MTH103", isElective: false },
              { title: "Discrete Mathematics", code: "MTH104", isElective: false },
              { title: "Mathematical Methods", code: "MTH105", isElective: false },
              { title: "Number Theory", code: "MTH106", isElective: true },
              { title: "Mathematical Modeling", code: "MTH107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Calculus II", code: "MTH201", isElective: false },
              { title: "Differential Equations", code: "MTH202", isElective: false },
              { title: "Statistics II", code: "MTH203", isElective: false },
              { title: "Abstract Algebra", code: "MTH204", isElective: false },
              { title: "Real Analysis", code: "MTH205", isElective: false },
              { title: "Numerical Analysis", code: "MTH206", isElective: true },
              { title: "Operations Research", code: "MTH207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Calculus", code: "MTH301", isElective: false },
              { title: "Complex Analysis", code: "MTH302", isElective: false },
              { title: "Topology", code: "MTH303", isElective: false },
              { title: "Mathematical Statistics", code: "MTH304", isElective: false },
              { title: "Functional Analysis", code: "MTH305", isElective: false },
              { title: "Cryptography", code: "MTH306", isElective: true },
              { title: "Financial Mathematics", code: "MTH307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "MTH401", isElective: false },
              { title: "Advanced Statistics", code: "MTH402", isElective: false },
              { title: "Research Methods", code: "MTH403", isElective: false },
              { title: "Applied Mathematics", code: "MTH404", isElective: false },
              { title: "Mathematical Computing", code: "MTH405", isElective: false },
              { title: "Data Science", code: "MTH406", isElective: true },
              { title: "Actuarial Science", code: "MTH407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Physics",
        code: "PHY",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Mechanics", code: "PHY101", isElective: false },
              { title: "Waves and Optics", code: "PHY102", isElective: false },
              { title: "Electricity and Magnetism", code: "PHY103", isElective: false },
              { title: "Thermodynamics", code: "PHY104", isElective: false },
              { title: "Laboratory Physics I", code: "PHY105", isElective: false },
              { title: "Astronomy", code: "PHY106", isElective: true },
              { title: "Biophysics", code: "PHY107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Modern Physics", code: "PHY201", isElective: false },
              { title: "Quantum Mechanics I", code: "PHY202", isElective: false },
              { title: "Statistical Mechanics", code: "PHY203", isElective: false },
              { title: "Solid State Physics", code: "PHY204", isElective: false },
              { title: "Laboratory Physics II", code: "PHY205", isElective: false },
              { title: "Nuclear Physics", code: "PHY206", isElective: true },
              { title: "Geophysics", code: "PHY207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Quantum Mechanics", code: "PHY301", isElective: false },
              { title: "Electromagnetic Theory", code: "PHY302", isElective: false },
              { title: "Condensed Matter Physics", code: "PHY303", isElective: false },
              { title: "Particle Physics", code: "PHY304", isElective: false },
              { title: "Computational Physics", code: "PHY305", isElective: false },
              { title: "Laser Physics", code: "PHY306", isElective: true },
              { title: "Medical Physics", code: "PHY307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "PHY401", isElective: false },
              { title: "Advanced Laboratory", code: "PHY402", isElective: false },
              { title: "Research Methods", code: "PHY403", isElective: false },
              { title: "Theoretical Physics", code: "PHY404", isElective: false },
              { title: "Physics Seminar", code: "PHY405", isElective: false },
              { title: "Nanotechnology", code: "PHY406", isElective: true },
              { title: "Renewable Energy Physics", code: "PHY407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Chemistry",
        code: "CHM",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "General Chemistry I", code: "CHM101", isElective: false },
              { title: "Organic Chemistry I", code: "CHM102", isElective: false },
              { title: "Inorganic Chemistry I", code: "CHM103", isElective: false },
              { title: "Physical Chemistry I", code: "CHM104", isElective: false },
              { title: "Analytical Chemistry I", code: "CHM105", isElective: false },
              { title: "Environmental Chemistry", code: "CHM106", isElective: true },
              { title: "Biochemistry Fundamentals", code: "CHM107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Organic Chemistry II", code: "CHM201", isElective: false },
              { title: "Inorganic Chemistry II", code: "CHM202", isElective: false },
              { title: "Physical Chemistry II", code: "CHM203", isElective: false },
              { title: "Analytical Chemistry II", code: "CHM204", isElective: false },
              { title: "Industrial Chemistry", code: "CHM205", isElective: false },
              { title: "Polymer Chemistry", code: "CHM206", isElective: true },
              { title: "Medicinal Chemistry", code: "CHM207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Organic Chemistry", code: "CHM301", isElective: false },
              { title: "Advanced Inorganic Chemistry", code: "CHM302", isElective: false },
              { title: "Quantum Chemistry", code: "CHM303", isElective: false },
              { title: "Spectroscopy", code: "CHM304", isElective: false },
              { title: "Chemical Kinetics", code: "CHM305", isElective: false },
              { title: "Catalysis", code: "CHM306", isElective: true },
              { title: "Materials Chemistry", code: "CHM307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "CHM401", isElective: false },
              { title: "Advanced Analytical Methods", code: "CHM402", isElective: false },
              { title: "Research Methods", code: "CHM403", isElective: false },
              { title: "Chemical Process Design", code: "CHM404", isElective: false },
              { title: "Chemistry Seminar", code: "CHM405", isElective: false },
              { title: "Green Chemistry", code: "CHM406", isElective: true },
              { title: "Computational Chemistry", code: "CHM407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Biology",
        code: "BIO",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "General Biology", code: "BIO101", isElective: false },
              { title: "Cell Biology", code: "BIO102", isElective: false },
              { title: "Genetics", code: "BIO103", isElective: false },
              { title: "Ecology", code: "BIO104", isElective: false },
              { title: "Botany", code: "BIO105", isElective: false },
              { title: "Marine Biology", code: "BIO106", isElective: true },
              { title: "Microbiology", code: "BIO107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Molecular Biology", code: "BIO201", isElective: false },
              { title: "Biochemistry", code: "BIO202", isElective: false },
              { title: "Physiology", code: "BIO203", isElective: false },
              { title: "Evolution", code: "BIO204", isElective: false },
              { title: "Zoology", code: "BIO205", isElective: false },
              { title: "Biotechnology", code: "BIO206", isElective: true },
              { title: "Conservation Biology", code: "BIO207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Genetics", code: "BIO301", isElective: false },
              { title: "Developmental Biology", code: "BIO302", isElective: false },
              { title: "Immunology", code: "BIO303", isElective: false },
              { title: "Bioinformatics", code: "BIO304", isElective: false },
              { title: "Research Methods", code: "BIO305", isElective: false },
              { title: "Synthetic Biology", code: "BIO306", isElective: true },
              { title: "Systems Biology", code: "BIO307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "BIO401", isElective: false },
              { title: "Advanced Laboratory Techniques", code: "BIO402", isElective: false },
              { title: "Biostatistics", code: "BIO403", isElective: false },
              { title: "Scientific Writing", code: "BIO404", isElective: false },
              { title: "Biology Seminar", code: "BIO405", isElective: false },
              { title: "Genomics", code: "BIO406", isElective: true },
              { title: "Proteomics", code: "BIO407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Computer Science",
        code: "SCS",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Programming Fundamentals", code: "SCS101", isElective: false },
              { title: "Discrete Mathematics", code: "SCS102", isElective: false },
              { title: "Computer Systems", code: "SCS103", isElective: false },
              { title: "Data Structures", code: "SCS104", isElective: false },
              { title: "Logic Design", code: "SCS105", isElective: false },
              { title: "Web Programming", code: "SCS106", isElective: true },
              { title: "Game Development", code: "SCS107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Algorithms", code: "SCS201", isElective: false },
              { title: "Database Systems", code: "SCS202", isElective: false },
              { title: "Software Engineering", code: "SCS203", isElective: false },
              { title: "Computer Networks", code: "SCS204", isElective: false },
              { title: "Operating Systems", code: "SCS205", isElective: false },
              { title: "Artificial Intelligence", code: "SCS206", isElective: true },
              { title: "Cybersecurity", code: "SCS207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Algorithms", code: "SCS301", isElective: false },
              { title: "Machine Learning", code: "SCS302", isElective: false },
              { title: "Distributed Systems", code: "SCS303", isElective: false },
              { title: "Computer Graphics", code: "SCS304", isElective: false },
              { title: "Human-Computer Interaction", code: "SCS305", isElective: false },
              { title: "Quantum Computing", code: "SCS306", isElective: true },
              { title: "Blockchain", code: "SCS307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "SCS401", isElective: false },
              { title: "Advanced Machine Learning", code: "SCS402", isElective: false },
              { title: "Research Methods", code: "SCS403", isElective: false },
              { title: "Software Architecture", code: "SCS404", isElective: false },
              { title: "Computer Science Seminar", code: "SCS405", isElective: false },
              { title: "Deep Learning", code: "SCS406", isElective: true },
              { title: "Cloud Computing", code: "SCS407", isElective: true }
            ]
          }
        ]
      }
    ]
  },
  {
    name: "Faculty of Business Administration",
    code: "BUS",
    departments: [
      {
        name: "Accounting",
        code: "ACC",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Principles of Accounting", code: "ACC101", isElective: false },
              { title: "Business Mathematics", code: "ACC102", isElective: false },
              { title: "Business Communication", code: "ACC103", isElective: false },
              { title: "Economics I", code: "ACC104", isElective: false },
              { title: "Business Law", code: "ACC105", isElective: false },
              { title: "Computer Applications", code: "ACC106", isElective: true },
              { title: "Entrepreneurship", code: "ACC107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Cost Accounting", code: "ACC201", isElective: false },
              { title: "Management Accounting", code: "ACC202", isElective: false },
              { title: "Financial Accounting", code: "ACC203", isElective: false },
              { title: "Taxation", code: "ACC204", isElective: false },
              { title: "Auditing I", code: "ACC205", isElective: false },
              { title: "International Accounting", code: "ACC206", isElective: true },
              { title: "Forensic Accounting", code: "ACC207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Financial Accounting", code: "ACC301", isElective: false },
              { title: "Advanced Management Accounting", code: "ACC302", isElective: false },
              { title: "Advanced Auditing", code: "ACC303", isElective: false },
              { title: "Financial Management", code: "ACC304", isElective: false },
              { title: "Corporate Reporting", code: "ACC305", isElective: false },
              { title: "Public Sector Accounting", code: "ACC306", isElective: true },
              { title: "Environmental Accounting", code: "ACC307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "ACC401", isElective: false },
              { title: "Strategic Management Accounting", code: "ACC402", isElective: false },
              { title: "Professional Ethics", code: "ACC403", isElective: false },
              { title: "Research Methods", code: "ACC404", isElective: false },
              { title: "Accounting Seminar", code: "ACC405", isElective: false },
              { title: "Digital Accounting", code: "ACC406", isElective: true },
              { title: "Investment Analysis", code: "ACC407", isElective: true }
            ]
          }
        ]
      },
      {
        name: "Business Administration",
        code: "BAD",
        levels: [
          {
            name: "National Diploma 1",
            code: "ND1",
            courses: [
              { title: "Principles of Management", code: "BAD101", isElective: false },
              { title: "Business Environment", code: "BAD102", isElective: false },
              { title: "Marketing Principles", code: "BAD103", isElective: false },
              { title: "Human Resource Management", code: "BAD104", isElective: false },
              { title: "Business Statistics", code: "BAD105", isElective: false },
              { title: "E-Commerce", code: "BAD106", isElective: true },
              { title: "Small Business Management", code: "BAD107", isElective: true }
            ]
          },
          {
            name: "National Diploma 2",
            code: "ND2",
            courses: [
              { title: "Operations Management", code: "BAD201", isElective: false },
              { title: "Strategic Management", code: "BAD202", isElective: false },
              { title: "Financial Management", code: "BAD203", isElective: false },
              { title: "Organizational Behavior", code: "BAD204", isElective: false },
              { title: "Project Management", code: "BAD205", isElective: false },
              { title: "International Business", code: "BAD206", isElective: true },
              { title: "Supply Chain Management", code: "BAD207", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 1",
            code: "HND1",
            courses: [
              { title: "Advanced Strategic Management", code: "BAD301", isElective: false },
              { title: "Leadership and Change Management", code: "BAD302", isElective: false },
              { title: "Business Research Methods", code: "BAD303", isElective: false },
              { title: "Corporate Governance", code: "BAD304", isElective: false },
              { title: "Innovation Management", code: "BAD305", isElective: false },
              { title: "Digital Transformation", code: "BAD306", isElective: true },
              { title: "Crisis Management", code: "BAD307", isElective: true }
            ]
          },
          {
            name: "Higher National Diploma 2",
            code: "HND2",
            courses: [
              { title: "Final Year Project I", code: "BAD401", isElective: false },
              { title: "Business Consulting", code: "BAD402", isElective: false },
              { title: "Advanced Research Methods", code: "BAD403", isElective: false },
              { title: "Business Ethics", code: "BAD404", isElective: false },
              { title: "Business Seminar", code: "BAD405", isElective: false },
              { title: "Sustainable Business", code: "BAD406", isElective: true },
              { title: "Business Analytics", code: "BAD407", isElective: true }
            ]
          }
        ]
      }
    ]
  }
];

// Hall data for seeding
const HALL_DATA = [
  // Main Examination Halls
  { name: "Main Examination Hall A", code: "MEHA", location: "Ground Floor, Main Building", capacity: 200, type: "examination_hall" },
  { name: "Main Examination Hall B", code: "MEHB", location: "Ground Floor, Main Building", capacity: 180, type: "examination_hall" },
  { name: "Main Examination Hall C", code: "MEHC", location: "First Floor, Main Building", capacity: 150, type: "examination_hall" },
  { name: "Main Examination Hall D", code: "MEHD", location: "First Floor, Main Building", capacity: 120, type: "examination_hall" },

  // Faculty-specific Halls
  { name: "Engineering Hall 1", code: "ENG1", location: "Engineering Complex", capacity: 100, type: "examination_hall" },
  { name: "Engineering Hall 2", code: "ENG2", location: "Engineering Complex", capacity: 80, type: "examination_hall" },
  { name: "Science Laboratory Hall", code: "SCIH", location: "Science Building", capacity: 60, type: "laboratory_hall" },
  { name: "Computer Laboratory Hall", code: "COMP", location: "ICT Center", capacity: 50, type: "computer_lab" },

  // Lecture Halls (can be used for exams)
  { name: "Grand Lecture Theatre", code: "GLT", location: "Academic Block", capacity: 300, type: "lecture_hall" },
  { name: "Lecture Hall 1", code: "LH1", location: "Academic Block", capacity: 150, type: "lecture_hall" },
  { name: "Lecture Hall 2", code: "LH2", location: "Academic Block", capacity: 120, type: "lecture_hall" },
  { name: "Lecture Hall 3", code: "LH3", location: "Academic Block", capacity: 100, type: "lecture_hall" },
  { name: "Lecture Hall 4", code: "LH4", location: "Academic Block", capacity: 80, type: "lecture_hall" },
  { name: "Lecture Hall 5", code: "LH5", location: "Academic Block", capacity: 60, type: "lecture_hall" },

  // Specialized Halls
  { name: "Architecture Studio Hall", code: "ARCH", location: "Architecture Building", capacity: 40, type: "studio_hall" },
  { name: "Business Seminar Hall", code: "BSH", location: "Business School", capacity: 70, type: "seminar_hall" },
  { name: "Multi-Purpose Hall", code: "MPH", location: "Student Center", capacity: 250, type: "multi_purpose" },

  // Additional Examination Halls
  { name: "Examination Hall E", code: "EXE", location: "Second Floor, Main Building", capacity: 90, type: "examination_hall" },
  { name: "Examination Hall F", code: "EXF", location: "Second Floor, Main Building", capacity: 75, type: "examination_hall" },
  { name: "Examination Hall G", code: "EXG", location: "Third Floor, Main Building", capacity: 65, type: "examination_hall" },
  { name: "Examination Hall H", code: "EXH", location: "Third Floor, Main Building", capacity: 55, type: "examination_hall" }
];

// Nigerian names for realistic student data
const NIGERIAN_FIRST_NAMES = [
  "Adebayo", "Chioma", "Emeka", "Fatima", "Ibrahim", "Kemi", "Olumide", "Ngozi", "Tunde", "Aisha",
  "Chinedu", "Folake", "Hassan", "Ifeoma", "Jide", "Khadija", "Lanre", "Maryam", "Nkem", "Ola",
  "Precious", "Rasheed", "Sola", "Taiwo", "Uche", "Victoria", "Wale", "Yemi", "Zainab", "Abdulazeez",
  "Blessing", "Chukwuma", "Damilola", "Esther", "Felix", "Grace", "Habib", "Isioma", "Joseph", "Kehinde",
  "Lateef", "Mercy", "Nnamdi", "Obinna", "Peace", "Quadri", "Ruth", "Samuel", "Temitope", "Uzoma"
];

const NIGERIAN_LAST_NAMES = [
  "Adebayo", "Okafor", "Ibrahim", "Yusuf", "Okonkwo", "Bello", "Adamu", "Eze", "Mohammed", "Okoro",
  "Aliyu", "Nwankwo", "Hassan", "Ogundimu", "Musa", "Chukwu", "Abdullahi", "Ojo", "Usman", "Nwosu",
  "Garba", "Okeke", "Suleiman", "Adeyemi", "Danjuma", "Onwuka", "Lawal", "Okafor", "Shehu", "Emeka",
  "Yakubu", "Nnamdi", "Ismail", "Adebisi", "Mamman", "Chinedu", "Abubakar", "Olumide", "Tijani", "Ikechukwu",
  "Haruna", "Obinna", "Salisu", "Adunni", "Kabir", "Chioma", "Nasir", "Folake", "Aminu", "Ngozi"
];

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function generateMatricNumber(deptCode: string, year: number, sequence: number): string {
  return `${deptCode}/${year}/${sequence.toString().padStart(3, '0')}`;
}

function generateEmail(firstName: string, lastName: string, matricNumber: string): string {
  // Use matric number in email for uniqueness
  const cleanMatric = matricNumber.replace(/\//g, '');
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}.${cleanMatric}@student.mapoly.edu.ng`;
}

// Academic session data
const CURRENT_SESSION = {
  name: "2024/2025",
  startDate: new Date("2024-09-01"),
  endDate: new Date("2025-08-31"),
  isActive: true
};

// Seeding functions
async function seedAcademicSession() {
  console.log("🎓 Seeding academic session...");

  try {
    const sessionRef = await addDoc(collection(db, 'academic_sessions'), {
      ...CURRENT_SESSION,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'system'
    });

    console.log(`✅ Created academic session: ${CURRENT_SESSION.name}`);
    return sessionRef.id;
  } catch (error) {
    console.error("❌ Error creating academic session:", error);
    throw error;
  }
}

async function seedSemesters(sessionId: string) {
  console.log("📚 Seeding semesters...");

  const semesters = [
    { name: "First Semester", code: "1ST", startDate: new Date("2024-09-01"), endDate: new Date("2025-01-31") },
    { name: "Second Semester", code: "2ND", startDate: new Date("2025-02-01"), endDate: new Date("2025-06-30") }
  ];

  const semesterIds: string[] = [];

  for (const semester of semesters) {
    try {
      const semesterRef = await addDoc(collection(db, 'semesters'), {
        ...semester,
        sessionId,
        sessionName: CURRENT_SESSION.name,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system'
      });

      semesterIds.push(semesterRef.id);
      console.log(`✅ Created semester: ${semester.name}`);
    } catch (error) {
      console.error(`❌ Error creating semester ${semester.name}:`, error);
    }
  }

  return semesterIds;
}

async function seedHalls() {
  console.log("🏢 Seeding halls...");

  const hallIds: string[] = [];

  for (const hall of HALL_DATA) {
    try {
      const hallRef = await addDoc(collection(db, 'halls'), {
        ...hall,
        status: 'active',
        facilities: ['projector', 'air_conditioning', 'sound_system'],
        description: `${hall.name} - ${hall.capacity} seating capacity`,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system'
      });

      hallIds.push(hallRef.id);
      console.log(`✅ Created hall: ${hall.name} (${hall.capacity} capacity)`);
    } catch (error) {
      console.error(`❌ Error creating hall ${hall.name}:`, error);
    }
  }

  return hallIds;
}

async function seedFacultiesAndDepartments() {
  console.log("🏛️ Seeding faculties, departments, levels, and courses...");

  const facultyIds: Record<string, string> = {};
  const departmentIds: Record<string, string> = {};
  const levelIds: Record<string, string> = {};
  const courseIds: Record<string, string> = {};

  for (const faculty of SEED_DATA) {
    try {
      // Create Faculty
      const facultyRef = await addDoc(collection(db, 'faculties'), {
        name: faculty.name,
        code: faculty.code,
        description: `${faculty.name} - Academic Faculty`,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system'
      });

      facultyIds[faculty.code] = facultyRef.id;
      console.log(`✅ Created faculty: ${faculty.name}`);

      // Create Departments
      for (const department of faculty.departments) {
        const deptRef = await addDoc(collection(db, 'departments'), {
          name: department.name,
          code: department.code,
          facultyId: facultyRef.id,
          facultyName: faculty.name,
          description: `${department.name} Department`,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'system'
        });

        departmentIds[`${faculty.code}-${department.code}`] = deptRef.id;
        console.log(`  ✅ Created department: ${department.name}`);

        // Create Levels
        for (const level of department.levels) {
          const levelRef = await addDoc(collection(db, 'levels'), {
            name: level.name,
            code: level.code,
            departmentId: deptRef.id,
            departmentName: department.name,
            facultyId: facultyRef.id,
            facultyName: faculty.name,
            studyMode: level.code.includes('ND') ? 'full_time' : 'full_time', // Can be customized
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'system'
          });

          levelIds[`${faculty.code}-${department.code}-${level.code}`] = levelRef.id;
          console.log(`    ✅ Created level: ${level.name}`);

          // Create Courses
          for (const course of level.courses) {
            const courseRef = await addDoc(collection(db, 'courses'), {
              title: course.title,
              code: course.code,
              isElective: course.isElective,
              departmentId: deptRef.id,
              departmentName: department.name,
              facultyId: facultyRef.id,
              facultyName: faculty.name,
              levelId: levelRef.id,
              levelName: level.name,
              creditUnits: course.isElective ? 2 : 3,
              status: 'active',
              createdAt: new Date(),
              updatedAt: new Date(),
              createdBy: 'system'
            });

            courseIds[`${faculty.code}-${department.code}-${level.code}-${course.code}`] = courseRef.id;
            console.log(`      ✅ Created course: ${course.code} - ${course.title}`);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error creating faculty ${faculty.name}:`, error);
    }
  }

  return { facultyIds, departmentIds, levelIds, courseIds };
}

async function seedStudents(
  departmentIds: Record<string, string>,
  levelIds: Record<string, string>,
  facultyIds: Record<string, string>
) {
  console.log("👥 Seeding students...");

  const studentsPerLevel = 50; // 25 full-time + 25 part-time
  const currentYear = new Date().getFullYear();

  for (const faculty of SEED_DATA) {
    for (const department of faculty.departments) {
      for (const level of department.levels) {
        const deptKey = `${faculty.code}-${department.code}`;
        const levelKey = `${faculty.code}-${department.code}-${level.code}`;

        const departmentId = departmentIds[deptKey];
        const levelId = levelIds[levelKey];

        if (!departmentId || !levelId) {
          console.warn(`⚠️ Missing IDs for ${levelKey}`);
          continue;
        }

        console.log(`  👥 Creating ${studentsPerLevel} students for ${department.name} ${level.name}...`);

        // Create batch for better performance
        const batch = writeBatch(db);
        let batchCount = 0;

        for (let i = 1; i <= studentsPerLevel; i++) {
          const firstName = getRandomElement(NIGERIAN_FIRST_NAMES);
          const lastName = getRandomElement(NIGERIAN_LAST_NAMES);
          const studyMode = i <= 25 ? 'full_time' : 'part_time';
          const matricNumber = generateMatricNumber(department.code, currentYear, i);
          const email = generateEmail(firstName, lastName, matricNumber);

          const studentRef = doc(collection(db, 'students'));
          batch.set(studentRef, {
            matricNumber,
            name: `${firstName} ${lastName}`,
            email,
            phoneNumber: `+234${Math.floor(Math.random() * **********) + **********}`,
            gender: Math.random() > 0.5 ? 'male' : 'female',
            studyMode,
            status: 'active',
            facultyId: facultyIds[faculty.code],
            facultyName: faculty.name,
            departmentId,
            departmentName: department.name,
            levelId,
            levelName: level.name,
            defaultPassword: 'student123',
            hasChangedPassword: false,
            hasSetupAccount: Math.random() > 0.3, // 70% have setup accounts
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'system'
          });

          batchCount++;

          // Commit batch every 500 documents (Firestore limit)
          if (batchCount >= 500) {
            await batch.commit();
            console.log(`    ✅ Committed batch of ${batchCount} students`);
            batchCount = 0;
          }
        }

        // Commit remaining documents
        if (batchCount > 0) {
          await batch.commit();
          console.log(`    ✅ Committed final batch of ${batchCount} students`);
        }

        console.log(`  ✅ Created ${studentsPerLevel} students for ${department.name} ${level.name}`);
      }
    }
  }
}

// Main seeding function
export async function seedDatabase() {
  console.log("🌱 Starting database seeding...");
  console.log("⚠️  This will create a large amount of test data!");

  try {
    // Step 1: Create academic session
    const sessionId = await seedAcademicSession();

    // Step 2: Create semesters
    await seedSemesters(sessionId);

    // Step 3: Create halls
    await seedHalls();

    // Step 4: Create academic structure
    const { facultyIds, departmentIds, levelIds } = await seedFacultiesAndDepartments();

    // Step 5: Create students
    await seedStudents(departmentIds, levelIds, facultyIds);

    console.log("🎉 Database seeding completed successfully!");
    console.log("📊 Summary:");
    console.log(`  - 1 Academic Session (${CURRENT_SESSION.name})`);
    console.log(`  - 2 Semesters`);
    console.log(`  - ${HALL_DATA.length} Halls`);
    console.log(`  - ${SEED_DATA.length} Faculties`);
    console.log(`  - ${SEED_DATA.reduce((sum, f) => sum + f.departments.length, 0)} Departments`);
    console.log(`  - ${SEED_DATA.reduce((sum, f) => sum + f.departments.reduce((dSum, d) => dSum + d.levels.length, 0), 0)} Levels`);
    console.log(`  - ${SEED_DATA.reduce((sum, f) => sum + f.departments.reduce((dSum, d) => dSum + d.levels.reduce((lSum, l) => lSum + l.courses.length, 0), 0), 0)} Courses`);
    console.log(`  - ${SEED_DATA.reduce((sum, f) => sum + f.departments.reduce((dSum, d) => dSum + d.levels.length * 50, 0), 0)} Students`);

  } catch (error) {
    console.error("❌ Error during database seeding:", error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log("✅ Seeding completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Seeding failed:", error);
      process.exit(1);
    });
}
