// Department Management Service for Firestore
import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  getDoc,
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  where,
  Timestamp,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import {
  Faculty,
  Department,
  Level,
  Student,
  StudentHistory,
  DepartmentAccess,
  SystemUser,
  FacultyFormData,
  DepartmentFormData,
  StudentFormData,
  SystemUserFormData,
  LEVEL_PRESETS,
  LevelPresetType,
  generateDepartmentAccessId,
  generateDefaultPassword,
  generateSystemUserId,
  generateSystemUserPassword
} from '@/types/department';
import { Course } from '@/types/exam';

const FACULTIES_COLLECTION = 'faculties';
const DEPARTMENTS_COLLECTION = 'departments';
const LEVELS_COLLECTION = 'levels';
const STUDENTS_COLLECTION = 'students';
const STUDENT_HISTORY_COLLECTION = 'student_history';
const DEPARTMENT_ACCESS_COLLECTION = 'department_access';
const SYSTEM_USERS_COLLECTION = 'system_users';
const COURSES_COLLECTION = 'courses';

// Convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
};

// FACULTY OPERATIONS
export const addFaculty = async (facultyData: FacultyFormData, createdBy: string): Promise<Faculty> => {
  try {
    console.log('🏫 Adding faculty to Firestore:', facultyData);
    
    const facultyDoc = {
      ...facultyData,
      code: facultyData.code.toUpperCase(),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };
    
    const docRef = await addDoc(collection(db, FACULTIES_COLLECTION), facultyDoc);
    const createdDoc = await getDoc(docRef);
    
    if (createdDoc.exists()) {
      const faculty: Faculty = {
        id: docRef.id,
        ...facultyData,
        code: facultyData.code.toUpperCase(),
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };
      console.log('✅ Faculty added successfully:', faculty.name);
      return faculty;
    }
    
    throw new Error('Failed to retrieve created faculty');
  } catch (error) {
    console.error('❌ Error adding faculty:', error);
    throw new Error(`Failed to add faculty: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getAllFaculties = async (): Promise<Faculty[]> => {
  try {
    console.log('🏫 Fetching all faculties from Firestore...');
    
    const q = query(
      collection(db, FACULTIES_COLLECTION),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    const faculties: Faculty[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      faculties.push({
        id: doc.id,
        name: data.name,
        code: data.code,
        description: data.description,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });
    
    console.log(`✅ Retrieved ${faculties.length} faculties from Firestore`);
    return faculties;
  } catch (error) {
    console.error('❌ Error fetching faculties:', error);
    throw new Error(`Failed to fetch faculties: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// DEPARTMENT OPERATIONS
export const addDepartment = async (departmentData: DepartmentFormData, createdBy: string): Promise<Department> => {
  try {
    console.log('🏢 Adding department to Firestore:', departmentData);
    
    // Get faculty information
    const facultyDoc = await getDoc(doc(db, FACULTIES_COLLECTION, departmentData.facultyId));
    if (!facultyDoc.exists()) {
      throw new Error('Faculty not found');
    }
    
    const facultyData = facultyDoc.data();
    
    const departmentDoc = {
      ...departmentData,
      code: departmentData.code.toUpperCase(),
      facultyCode: facultyData.code,
      facultyName: facultyData.name,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };
    
    const docRef = await addDoc(collection(db, DEPARTMENTS_COLLECTION), departmentDoc);
    const createdDoc = await getDoc(docRef);
    
    if (createdDoc.exists()) {
      const department: Department = {
        id: docRef.id,
        ...departmentData,
        code: departmentData.code.toUpperCase(),
        facultyCode: facultyData.code,
        facultyName: facultyData.name,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };
      
      console.log('✅ Department added successfully:', department.name);
      return department;
    }
    
    throw new Error('Failed to retrieve created department');
  } catch (error) {
    console.error('❌ Error adding department:', error);
    throw new Error(`Failed to add department: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getDepartmentsByFaculty = async (facultyId: string): Promise<Department[]> => {
  try {
    console.log('🏢 Fetching departments for faculty:', facultyId);

    // Try without orderBy first to avoid index issues
    const q = query(
      collection(db, DEPARTMENTS_COLLECTION),
      where('facultyId', '==', facultyId)
    );

    const querySnapshot = await getDocs(q);
    const departments: Department[] = [];

    console.log('🏢 Query snapshot size:', querySnapshot.size);
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      departments.push({
        id: doc.id,
        name: data.name,
        code: data.code,
        facultyId: data.facultyId,
        facultyCode: data.facultyCode,
        facultyName: data.facultyName,
        description: data.description,
        hasPortalAccess: data.hasPortalAccess || false,
        accessId: data.accessId,
        accessPassword: data.accessPassword,
        portalPermissions: data.portalPermissions || {
          canAddStudents: false,
          canViewStudents: false,
          canGenerateReports: false,
          canManageExamRegistrations: false
        },
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    // Sort by name after fetching to avoid Firestore index issues
    departments.sort((a, b) => a.name.localeCompare(b.name));

    console.log(`✅ Retrieved ${departments.length} departments for faculty`);
    return departments;
  } catch (error) {
    console.error('❌ Error fetching departments:', error);
    throw new Error(`Failed to fetch departments: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getAllDepartments = async (): Promise<Department[]> => {
  try {
    console.log('🏢 Fetching all departments from Firestore...');
    
    const q = query(
      collection(db, DEPARTMENTS_COLLECTION),
      orderBy('facultyName', 'asc'),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    const departments: Department[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      departments.push({
        id: doc.id,
        name: data.name,
        code: data.code,
        facultyId: data.facultyId,
        facultyCode: data.facultyCode,
        facultyName: data.facultyName,
        description: data.description,
        hasPortalAccess: data.hasPortalAccess || false,
        accessId: data.accessId,
        accessPassword: data.accessPassword,
        portalPermissions: data.portalPermissions || {
          canAddStudents: false,
          canViewStudents: false,
          canGenerateReports: false,
          canManageExamRegistrations: false
        },
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });
    
    console.log(`✅ Retrieved ${departments.length} departments from Firestore`);
    return departments;
  } catch (error) {
    console.error('❌ Error fetching departments:', error);
    throw new Error(`Failed to fetch departments: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// LEVEL OPERATIONS
export const createLevelsForDepartment = async (
  departmentId: string, 
  presetType: LevelPresetType, 
  customLevels?: Array<{name: string, code: string}>
): Promise<Level[]> => {
  try {
    console.log('📚 Creating levels for department:', departmentId);
    
    const batch = writeBatch(db);
    const levels: Level[] = [];
    const levelsToCreate = customLevels || LEVEL_PRESETS[presetType];
    
    for (const levelData of levelsToCreate) {
      const levelDoc = {
        name: levelData.name,
        code: levelData.code.toUpperCase(),
        departmentId,
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };
      
      const docRef = doc(collection(db, LEVELS_COLLECTION));
      batch.set(docRef, levelDoc);
      
      levels.push({
        id: docRef.id,
        name: levelData.name,
        code: levelData.code.toUpperCase(),
        departmentId,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    await batch.commit();
    console.log(`✅ Created ${levels.length} levels for department`);
    return levels;
  } catch (error) {
    console.error('❌ Error creating levels:', error);
    throw new Error(`Failed to create levels: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getLevelsByDepartment = async (departmentId: string): Promise<Level[]> => {
  try {
    console.log('📚 Fetching levels for department:', departmentId);

    // Remove orderBy to avoid index issues
    const q = query(
      collection(db, LEVELS_COLLECTION),
      where('departmentId', '==', departmentId),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(q);
    const levels: Level[] = [];

    console.log('📚 Query snapshot size:', querySnapshot.size);

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      levels.push({
        id: doc.id,
        name: data.name,
        code: data.code,
        departmentId: data.departmentId,
        isActive: data.isActive,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt)
      });
    });

    // Sort by code after fetching to avoid Firestore index issues
    levels.sort((a, b) => a.code.localeCompare(b.code));

    console.log(`✅ Retrieved ${levels.length} levels for department`);
    return levels;
  } catch (error) {
    console.error('❌ Error fetching levels:', error);
    throw new Error(`Failed to fetch levels: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Check if faculty code is unique
export const isFacultyCodeUnique = async (code: string, excludeId?: string): Promise<boolean> => {
  try {
    const q = query(
      collection(db, FACULTIES_COLLECTION),
      where('code', '==', code.toUpperCase())
    );
    
    const querySnapshot = await getDocs(q);
    
    if (excludeId) {
      const otherFaculties = querySnapshot.docs.filter(doc => doc.id !== excludeId);
      return otherFaculties.length === 0;
    }
    
    return querySnapshot.empty;
  } catch (error) {
    console.error('❌ Error checking faculty code uniqueness:', error);
    return false;
  }
};

// Check if department code is unique within faculty
export const isDepartmentCodeUnique = async (code: string, facultyId: string, excludeId?: string): Promise<boolean> => {
  try {
    const q = query(
      collection(db, DEPARTMENTS_COLLECTION),
      where('code', '==', code.toUpperCase()),
      where('facultyId', '==', facultyId)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (excludeId) {
      const otherDepartments = querySnapshot.docs.filter(doc => doc.id !== excludeId);
      return otherDepartments.length === 0;
    }
    
    return querySnapshot.empty;
  } catch (error) {
    console.error('❌ Error checking department code uniqueness:', error);
    return false;
  }
};

// STUDENT OPERATIONS
export const addStudent = async (studentData: StudentFormData, createdBy: string): Promise<Student> => {
  try {
    console.log('👨‍🎓 Adding student to Firestore:', studentData);

    // Get department and level information
    const [departmentDoc, levelDoc] = await Promise.all([
      getDoc(doc(db, DEPARTMENTS_COLLECTION, studentData.departmentId)),
      getDoc(doc(db, LEVELS_COLLECTION, studentData.levelId))
    ]);

    if (!departmentDoc.exists()) {
      throw new Error('Department not found');
    }

    if (!levelDoc.exists()) {
      throw new Error('Level not found');
    }

    const departmentInfo = departmentDoc.data();
    const levelInfo = levelDoc.data();

    const defaultPassword = generateDefaultPassword('student');

    const studentDoc = {
      ...studentData,
      matricNumber: studentData.matricNumber.toUpperCase(),
      facultyId: departmentInfo.facultyId,
      facultyName: departmentInfo.facultyName,
      departmentName: departmentInfo.name,
      levelName: levelInfo.name,
      defaultPassword,
      hasChangedPassword: false,
      hasSetupAccount: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };

    const docRef = await addDoc(collection(db, STUDENTS_COLLECTION), studentDoc);
    const createdDoc = await getDoc(docRef);

    if (createdDoc.exists()) {
      const student: Student = {
        id: docRef.id,
        ...studentData,
        matricNumber: studentData.matricNumber.toUpperCase(),
        facultyId: departmentInfo.facultyId,
        facultyName: departmentInfo.facultyName,
        departmentName: departmentInfo.name,
        levelName: levelInfo.name,
        defaultPassword,
        hasChangedPassword: false,
        hasSetupAccount: false,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };

      console.log('✅ Student added successfully:', student.matricNumber);
      return student;
    }

    throw new Error('Failed to retrieve created student');
  } catch (error) {
    console.error('❌ Error adding student:', error);
    throw new Error(`Failed to add student: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getStudentsByDepartment = async (departmentId: string): Promise<Student[]> => {
  try {
    console.log('👨‍🎓 Fetching students for department:', departmentId);

    const q = query(
      collection(db, STUDENTS_COLLECTION),
      where('departmentId', '==', departmentId),
      orderBy('matricNumber', 'asc')
    );

    const querySnapshot = await getDocs(q);
    const students: Student[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      students.push({
        id: doc.id,
        matricNumber: data.matricNumber,
        name: data.name,
        gender: data.gender,
        phoneNumber: data.phoneNumber,
        email: data.email,
        facultyId: data.facultyId,
        facultyName: data.facultyName,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        levelId: data.levelId,
        levelName: data.levelName,
        studyMode: data.studyMode,
        status: data.status,
        defaultPassword: data.defaultPassword,
        hasChangedPassword: data.hasChangedPassword || false,
        hasSetupAccount: data.hasSetupAccount || false,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    console.log(`✅ Retrieved ${students.length} students for department`);
    return students;
  } catch (error) {
    console.error('❌ Error fetching students:', error);
    throw new Error(`Failed to fetch students: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getAllStudents = async (): Promise<Student[]> => {
  try {
    console.log('👨‍🎓 Fetching all students from Firestore...');

    const q = query(
      collection(db, STUDENTS_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const students: Student[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      students.push({
        id: doc.id,
        matricNumber: data.matricNumber,
        name: data.name,
        gender: data.gender,
        phoneNumber: data.phoneNumber,
        email: data.email,
        facultyId: data.facultyId,
        facultyName: data.facultyName,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        levelId: data.levelId,
        levelName: data.levelName,
        studyMode: data.studyMode,
        status: data.status,
        defaultPassword: data.defaultPassword,
        hasChangedPassword: data.hasChangedPassword || false,
        hasSetupAccount: data.hasSetupAccount || false,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    console.log(`✅ Retrieved ${students.length} students from Firestore`);
    return students;
  } catch (error) {
    console.error('❌ Error fetching students:', error);
    throw new Error(`Failed to fetch students: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Check if matric number is unique
export const isMatricNumberUnique = async (matricNumber: string, excludeId?: string): Promise<boolean> => {
  try {
    const q = query(
      collection(db, STUDENTS_COLLECTION),
      where('matricNumber', '==', matricNumber.toUpperCase())
    );

    const querySnapshot = await getDocs(q);

    if (excludeId) {
      const otherStudents = querySnapshot.docs.filter(doc => doc.id !== excludeId);
      return otherStudents.length === 0;
    }

    return querySnapshot.empty;
  } catch (error) {
    console.error('❌ Error checking matric number uniqueness:', error);
    return false;
  }
};

// SYSTEM USER OPERATIONS (Supervisors/Invigilators)
export const addSystemUser = async (userData: SystemUserFormData, createdBy: string): Promise<SystemUser> => {
  try {
    console.log('👨‍💼 Adding system user to Firestore:', userData);

    const userId = generateSystemUserId(userData.role);
    const defaultPassword = generateSystemUserPassword(userData.role);

    const userDoc = {
      ...userData,
      userId,
      defaultPassword,
      hasChangedPassword: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy
    };

    const docRef = await addDoc(collection(db, SYSTEM_USERS_COLLECTION), userDoc);
    const createdDoc = await getDoc(docRef);

    if (createdDoc.exists()) {
      const systemUser: SystemUser = {
        id: docRef.id,
        ...userData,
        userId,
        defaultPassword,
        hasChangedPassword: false,
        createdAt: convertTimestamp(createdDoc.data().createdAt),
        updatedAt: convertTimestamp(createdDoc.data().updatedAt),
        createdBy
      };

      console.log('✅ System user added successfully:', systemUser.userId);
      return systemUser;
    }

    throw new Error('Failed to retrieve created system user');
  } catch (error) {
    console.error('❌ Error adding system user:', error);
    throw new Error(`Failed to add system user: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getAllSystemUsers = async (): Promise<SystemUser[]> => {
  try {
    console.log('👨‍💼 Fetching all system users from Firestore...');

    const q = query(
      collection(db, SYSTEM_USERS_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const users: SystemUser[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      users.push({
        id: doc.id,
        userId: data.userId,
        name: data.name,
        role: data.role,
        email: data.email,
        phoneNumber: data.phoneNumber,
        status: data.status,
        defaultPassword: data.defaultPassword,
        hasChangedPassword: data.hasChangedPassword || false,
        departmentAffiliation: data.departmentAffiliation || [],
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    console.log(`✅ Retrieved ${users.length} system users from Firestore`);
    return users;
  } catch (error) {
    console.error('❌ Error fetching system users:', error);
    throw new Error(`Failed to fetch system users: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const getSystemUsersByRole = async (role: 'supervisor' | 'invigilator'): Promise<SystemUser[]> => {
  try {
    console.log('👨‍💼 Fetching system users by role:', role);

    const q = query(
      collection(db, SYSTEM_USERS_COLLECTION),
      where('role', '==', role),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const users: SystemUser[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      users.push({
        id: doc.id,
        userId: data.userId,
        name: data.name,
        role: data.role,
        email: data.email,
        phoneNumber: data.phoneNumber,
        status: data.status,
        defaultPassword: data.defaultPassword,
        hasChangedPassword: data.hasChangedPassword || false,
        departmentAffiliation: data.departmentAffiliation || [],
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    console.log(`✅ Retrieved ${users.length} ${role}s from Firestore`);
    return users;
  } catch (error) {
    console.error(`❌ Error fetching ${role}s:`, error);
    throw new Error(`Failed to fetch ${role}s: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Update system user status (activate/deactivate)
export const updateSystemUserStatus = async (userId: string, status: 'active' | 'inactive' | 'suspended'): Promise<void> => {
  try {
    console.log('👨‍💼 Updating system user status:', userId, status);

    const docRef = doc(db, SYSTEM_USERS_COLLECTION, userId);
    await updateDoc(docRef, {
      status,
      updatedAt: serverTimestamp()
    });

    console.log('✅ System user status updated successfully');
  } catch (error) {
    console.error('❌ Error updating system user status:', error);
    throw new Error(`Failed to update user status: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// COURSE OPERATIONS
export const getAllCourses = async (): Promise<Course[]> => {
  try {
    console.log('📖 Fetching all courses from Firestore...');

    const q = query(
      collection(db, COURSES_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const courses: Course[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      courses.push({
        id: doc.id,
        code: data.code,
        title: data.title,
        creditUnits: data.creditUnits || 3,
        isElective: data.isElective || false,
        facultyId: data.facultyId,
        facultyName: data.facultyName,
        departmentId: data.departmentId,
        departmentName: data.departmentName,
        description: data.description,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
        createdBy: data.createdBy
      });
    });

    console.log(`✅ Retrieved ${courses.length} courses from Firestore`);
    return courses;
  } catch (error) {
    console.error('❌ Error fetching courses:', error);
    throw new Error(`Failed to fetch courses: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
