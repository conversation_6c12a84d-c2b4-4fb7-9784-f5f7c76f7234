"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Users, Save, X, Loader2, AlertCircle, Plus } from "lucide-react";
import { Hall, HallFormData, HallType, HallStatus } from "@/types/hall";
import { isHallCodeUnique } from "@/lib/firebase/hall-service";

interface EditHallFormProps {
  hall: Hall;
  onSubmit: (hallId: string, data: Partial<HallFormData>) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const hallTypes: { value: HallType; label: string }[] = [
  { value: "examination_hall", label: "Examination Hall" },
  { value: "lecture_hall", label: "Lecture Hall" },
  { value: "auditorium", label: "Auditorium" },
  { value: "classroom", label: "Classroom" },
  { value: "laboratory", label: "Laboratory" },
  { value: "computer_lab", label: "Computer Lab" },
];

const hallStatuses: { value: HallStatus; label: string; color: string }[] = [
  { value: "active", label: "Active", color: "text-green-600" },
  { value: "maintenance", label: "Under Maintenance", color: "text-orange-600" },
  { value: "unavailable", label: "Unavailable", color: "text-red-600" },
  { value: "reserved", label: "Reserved", color: "text-blue-600" },
];

const commonFacilities = [
  "Air Conditioning", "Projector", "Sound System", "Whiteboard", 
  "Blackboard", "WiFi", "Power Outlets", "CCTV", "Emergency Exit", 
  "Wheelchair Access", "Microphone", "Speakers"
];

export function EditHallForm({ hall, onSubmit, onCancel, isLoading = false }: EditHallFormProps) {
  const [formData, setFormData] = useState<HallFormData>({
    name: hall.name,
    code: hall.code,
    capacity: hall.capacity,
    location: hall.location,
    type: hall.type,
    status: hall.status,
    facilities: [...hall.facilities],
    description: hall.description || ""
  });

  const [newFacility, setNewFacility] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidatingCode, setIsValidatingCode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Track changes
  useEffect(() => {
    const changed = 
      formData.name !== hall.name ||
      formData.code !== hall.code ||
      formData.capacity !== hall.capacity ||
      formData.location !== hall.location ||
      formData.type !== hall.type ||
      formData.status !== hall.status ||
      JSON.stringify(formData.facilities.sort()) !== JSON.stringify(hall.facilities.sort()) ||
      formData.description !== (hall.description || "");
    
    setHasChanges(changed);
  }, [formData, hall]);

  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    // Validate required fields
    if (!formData.name.trim()) {
      newErrors.name = "Hall name is required";
    }

    if (!formData.code.trim()) {
      newErrors.code = "Hall code is required";
    } else if (formData.code !== hall.code) {
      // Check if hall code is unique (only if changed)
      setIsValidatingCode(true);
      const isUnique = await isHallCodeUnique(formData.code, hall.id);
      setIsValidatingCode(false);
      
      if (!isUnique) {
        newErrors.code = "Hall code already exists";
      }
    }

    if (formData.capacity <= 0) {
      newErrors.capacity = "Capacity must be greater than 0";
    }

    if (!formData.location.trim()) {
      newErrors.location = "Location is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!hasChanges) {
      onCancel();
      return;
    }

    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    // Only send changed fields
    const changes: Partial<HallFormData> = {};
    if (formData.name !== hall.name) changes.name = formData.name;
    if (formData.code !== hall.code) changes.code = formData.code;
    if (formData.capacity !== hall.capacity) changes.capacity = formData.capacity;
    if (formData.location !== hall.location) changes.location = formData.location;
    if (formData.type !== hall.type) changes.type = formData.type;
    if (formData.status !== hall.status) changes.status = formData.status;
    if (JSON.stringify(formData.facilities.sort()) !== JSON.stringify(hall.facilities.sort())) {
      changes.facilities = formData.facilities;
    }
    if (formData.description !== (hall.description || "")) changes.description = formData.description;

    await onSubmit(hall.id, changes);
  };

  const addFacility = (facility: string) => {
    if (facility && !formData.facilities.includes(facility)) {
      setFormData(prev => ({
        ...prev,
        facilities: [...prev.facilities, facility]
      }));
    }
    setNewFacility("");
  };

  const removeFacility = (facility: string) => {
    setFormData(prev => ({
      ...prev,
      facilities: prev.facilities.filter(f => f !== facility)
    }));
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Edit Hall: {hall.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Hall Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Main Lecture Theatre"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={errors.name ? "border-red-500" : ""}
                required
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="code">Hall Code *</Label>
              <Input
                id="code"
                placeholder="e.g., LT1, LAB-A, HALL-001"
                value={formData.code}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                className={`font-mono ${errors.code ? "border-red-500" : ""}`}
                required
              />
              {errors.code && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.code}
                </p>
              )}
              {isValidatingCode && (
                <p className="text-sm text-blue-600 flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Checking availability...
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity *</Label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="capacity"
                  type="number"
                  placeholder="e.g., 150"
                  value={formData.capacity || ""}
                  onChange={(e) => setFormData(prev => ({ ...prev, capacity: parseInt(e.target.value) || 0 }))}
                  className={`pl-10 ${errors.capacity ? "border-red-500" : ""}`}
                  min="1"
                  required
                />
              </div>
              {errors.capacity && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.capacity}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="location">Location/Building *</Label>
              <Input
                id="location"
                placeholder="e.g., Science Block, Main Building"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                className={errors.location ? "border-red-500" : ""}
                required
              />
              {errors.location && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.location}
                </p>
              )}
            </div>
          </div>

          {/* Hall Type and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Hall Type *</Label>
              <Select 
                value={formData.type} 
                onValueChange={(value: HallType) => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {hallTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Status *</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value: HallStatus) => setFormData(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {hallStatuses.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <span className={status.color}>{status.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Facilities */}
          <div className="space-y-4">
            <Label>Facilities & Equipment</Label>
            
            {/* Common Facilities */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {commonFacilities.map((facility) => (
                <Button
                  key={facility}
                  type="button"
                  variant={formData.facilities.includes(facility) ? "default" : "outline"}
                  size="sm"
                  onClick={() => 
                    formData.facilities.includes(facility) 
                      ? removeFacility(facility)
                      : addFacility(facility)
                  }
                  className="text-xs"
                >
                  {facility}
                </Button>
              ))}
            </div>

            {/* Custom Facility */}
            <div className="flex gap-2">
              <Input
                placeholder="Add custom facility..."
                value={newFacility}
                onChange={(e) => setNewFacility(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addFacility(newFacility))}
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => addFacility(newFacility)}
                disabled={!newFacility}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Selected Facilities */}
            {formData.facilities.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.facilities.map((facility) => (
                  <Badge key={facility} variant="secondary" className="flex items-center gap-1">
                    {facility}
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeFacility(facility)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Additional notes about the hall..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button 
              type="submit" 
              disabled={isLoading || !hasChanges} 
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Hall...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {hasChanges ? "Save Changes" : "No Changes"}
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>

          {/* Changes Indicator */}
          {hasChanges && (
            <div className="text-sm text-blue-600 text-center">
              You have unsaved changes
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
