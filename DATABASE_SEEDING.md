# 🌱 Hall Automata Database Seeding Guide

This guide will help you populate your Firestore database with comprehensive test data for the Hall Automata system.

## 📊 What Will Be Created

### **Academic Structure:**
- **5 Faculties:**
  - Faculty of Engineering (5 departments)
  - Faculty of Science (5 departments) 
  - Faculty of Business Administration (2 departments)
  - Faculty of Arts (coming soon)
  - Faculty of Environmental Sciences (coming soon)

### **Departments per Faculty:**
- **Engineering:** Computer Science, Electrical Engineering, Mechanical Engineering, Civil Engineering, Architecture
- **Science:** Mathematics, Physics, Chemistry, Biology, Computer Science
- **Business:** Accounting, Business Administration

### **Academic Levels (4 per department):**
- **ND1** - National Diploma 1
- **ND2** - National Diploma 2  
- **HND1** - Higher National Diploma 1
- **HND2** - Higher National Diploma 2

### **Courses (7+ per level):**
- **Core courses** (5 required courses)
- **Elective courses** (2+ optional courses)
- **Realistic course codes** (e.g., CSC101, EEE201, MTH301)

### **Students (50 per level):**
- **25 Full-time students** per level
- **25 Part-time students** per level
- **Realistic Nigerian names** and matric numbers
- **Generated email addresses** (@student.mapoly.edu.ng)
- **Random phone numbers** and demographics

### **Examination Halls (20+ halls):**
- **Main examination halls** (200, 180, 150, 120 capacity)
- **Faculty-specific halls** (Engineering, Science halls)
- **Lecture halls** (300, 150, 120, 100, 80, 60 capacity)
- **Specialized halls** (Computer lab, Architecture studio)
- **Various capacities** for testing different scenarios

### **Academic Sessions:**
- **Current session:** 2024/2025
- **Two semesters:** First Semester, Second Semester
- **Active status** for immediate use

## 🚀 How to Run the Seeding

### **Method 1: Using npm script (Recommended)**
```bash
npm run seed
```

### **Method 2: Direct execution**
```bash
npm run seed-direct
```

### **Method 3: Manual execution**
```bash
npx tsx src/scripts/seed-database.ts
```

## ⚠️ Important Notes

### **Before Running:**
1. **Backup your database** if you have existing data
2. **Verify Firebase configuration** in `src/lib/firebase/config.ts`
3. **Check Firestore permissions** - ensure write access
4. **Confirm you're using development database** (not production!)

### **Requirements:**
- Node.js installed
- Firebase project configured
- Firestore database enabled
- Internet connection for Firebase access

### **Time & Resources:**
- **Execution time:** 5-15 minutes (depending on internet speed)
- **Documents created:** ~3,000+ documents
- **Firestore operations:** Thousands of write operations
- **Cost:** May incur small Firebase usage charges

## 📈 Expected Results

### **Total Documents Created:**
```
📚 Academic Structure:
- 1 Academic Session (2024/2025)
- 2 Semesters (First, Second)
- 5 Faculties
- 12 Departments  
- 48 Levels (12 departments × 4 levels)
- 336+ Courses (48 levels × 7+ courses)

👥 Students:
- 2,400 Students (48 levels × 50 students)
- 1,200 Full-time students
- 1,200 Part-time students

🏢 Infrastructure:
- 20+ Examination halls
- Various capacities (40-300 seats)
- Different hall types
```

### **Sample Data Structure:**

#### **Student Example:**
```javascript
{
  matricNumber: "CSC/2024/001",
  name: "Adebayo Okafor",
  email: "<EMAIL>",
  phoneNumber: "+2348123456789",
  gender: "male",
  studyMode: "full_time",
  status: "active",
  facultyName: "Faculty of Engineering",
  departmentName: "Computer Science",
  levelName: "National Diploma 1"
}
```

#### **Course Example:**
```javascript
{
  code: "CSC101",
  title: "Introduction to Computer Science",
  isElective: false,
  creditUnits: 3,
  departmentName: "Computer Science",
  levelName: "National Diploma 1"
}
```

#### **Hall Example:**
```javascript
{
  name: "Main Examination Hall A",
  code: "MEHA",
  location: "Ground Floor, Main Building",
  capacity: 200,
  type: "examination_hall",
  status: "active"
}
```

## 🧪 Testing After Seeding

### **1. Hall Allocation Testing:**
```bash
# Navigate to hall allocation page
http://localhost:3000/admin/hall-allocation

# Test workflow:
1. Select "2024/2025" session
2. Choose "First Semester" 
3. Filter by department (e.g., Computer Science)
4. Select multiple exams
5. Configure allocation settings
6. Run hall allocation
7. Export results to Excel
```

### **2. Student Management:**
```bash
# Check student data
http://localhost:3000/admin/students

# Verify:
- Students appear in correct departments
- Matric numbers are properly formatted
- Study modes are correctly assigned
- Contact information is realistic
```

### **3. Exam Scheduling:**
```bash
# Create test exams
http://localhost:3000/admin/exams

# Test scenarios:
- Schedule exam for Computer Science ND1
- Select multiple halls
- Verify student count appears
- Test conflict detection
```

## 🔧 Troubleshooting

### **Common Issues:**

#### **"Firebase connection failed"**
```bash
# Check Firebase config
- Verify src/lib/firebase/config.ts
- Ensure Firestore is enabled
- Check internet connection
```

#### **"Permission denied"**
```bash
# Update Firestore rules (development only):
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

#### **"tsx command not found"**
```bash
# Install tsx globally
npm install -g tsx

# Or use npx
npx tsx src/scripts/seed-database.ts
```

#### **"Out of memory" errors**
```bash
# Increase Node.js memory limit
node --max-old-space-size=4096 src/scripts/run-seed.js
```

### **Partial Seeding:**
If seeding fails partway through, you can:
1. Check Firestore console for created documents
2. Delete partial data if needed
3. Re-run the seeding script
4. The script will create new documents (no duplicates)

## 📊 Verification Checklist

After seeding completes, verify:

- [ ] **Academic Sessions:** 1 session (2024/2025) exists
- [ ] **Semesters:** 2 semesters exist under the session
- [ ] **Faculties:** 5 faculties created
- [ ] **Departments:** 12+ departments across faculties
- [ ] **Levels:** 48 levels (4 per department)
- [ ] **Courses:** 300+ courses across all levels
- [ ] **Students:** 2,400 students with realistic data
- [ ] **Halls:** 20+ halls with various capacities
- [ ] **Hall Allocation:** Can select session and see exams
- [ ] **Student Queries:** Students appear when allocating halls

## 🎯 Next Steps

After successful seeding:

1. **Test Hall Allocation:**
   - Go to `/admin/hall-allocation`
   - Select 2024/2025 session
   - Choose multiple exams
   - Run allocation and verify results

2. **Create Sample Exams:**
   - Use bulk exam creation
   - Import from Excel
   - Test different scenarios

3. **Verify Data Integrity:**
   - Check student-department relationships
   - Verify course-level associations
   - Test hall capacity calculations

4. **Performance Testing:**
   - Test with large student groups
   - Verify allocation speed
   - Check Excel export functionality

## 🎉 Success!

If everything works correctly, you now have a fully populated Hall Automata database ready for comprehensive testing and demonstration!

Your system can now handle:
- ✅ **Real-world scale testing** (2,400+ students)
- ✅ **Multi-department scenarios** (12 departments)
- ✅ **Complex hall allocation** (20+ halls)
- ✅ **Professional demonstrations** (realistic data)
- ✅ **Final year project presentation** (enterprise-grade dataset)
