"use client";

import { useState, useEffect } from "react";
import { Department, Student, Level } from "@/types/department";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TypographyH3, TypographyP } from "@/components/ui/typography";
import { ArrowLeft, GraduationCap, Search, Filter, Users, Eye } from "lucide-react";
import { getLevelsByDepartment } from "@/lib/firebase/department-service";

interface DepartmentViewProps {
  department: Department;
  students: Student[];
  onBack: () => void;
}

export function DepartmentView({ department, students, onBack }: DepartmentViewProps) {
  const [levels, setLevels] = useState<Level[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>(students);
  const [searchTerm, setSearchTerm] = useState("");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [modeFilter, setModeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoadingLevels, setIsLoadingLevels] = useState(true);

  // Load levels for this department
  useEffect(() => {
    const loadLevels = async () => {
      try {
        const levelsData = await getLevelsByDepartment(department.id);
        setLevels(levelsData);
      } catch (error) {
        console.error("Error loading levels:", error);
      } finally {
        setIsLoadingLevels(false);
      }
    };

    loadLevels();
  }, [department.id]);

  // Apply filters whenever filters or students change
  useEffect(() => {
    let filtered = [...students];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(student => 
        student.matricNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (student.name && student.name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Level filter
    if (levelFilter !== "all") {
      filtered = filtered.filter(student => student.levelId === levelFilter);
    }

    // Study mode filter
    if (modeFilter !== "all") {
      filtered = filtered.filter(student => student.studyMode === modeFilter);
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(student => student.status === statusFilter);
    }

    setFilteredStudents(filtered);
  }, [students, searchTerm, levelFilter, modeFilter, statusFilter]);

  const clearFilters = () => {
    setSearchTerm("");
    setLevelFilter("all");
    setModeFilter("all");
    setStatusFilter("all");
  };

  // Calculate statistics
  const totalStudents = students.length;
  const activeStudents = students.filter(s => s.status === "active").length;
  const fullTimeStudents = students.filter(s => s.studyMode === "full_time").length;
  const partTimeStudents = students.filter(s => s.studyMode === "part_time").length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "inactive": return "bg-gray-100 text-gray-800";
      case "graduated": return "bg-blue-100 text-blue-800";
      case "suspended": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Faculty
        </Button>
        <div>
          <TypographyH3 className="flex items-center gap-2">
            <GraduationCap className="h-6 w-6" />
            {department.name}
          </TypographyH3>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="font-mono">
              {department.facultyCode}-{department.code}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {department.facultyName}
            </span>
          </div>
        </div>
      </div>

      {/* Department Description */}
      {department.description && (
        <Card>
          <CardContent className="pt-6">
            <TypographyP className="text-muted-foreground">
              {department.description}
            </TypographyP>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStudents}</div>
            <p className="text-xs text-muted-foreground">Registered students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeStudents}</div>
            <p className="text-xs text-muted-foreground">Active students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Full Time</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{fullTimeStudents}</div>
            <p className="text-xs text-muted-foreground">Full time students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Part Time</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{partTimeStudents}</div>
            <p className="text-xs text-muted-foreground">Part time students</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Students
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by matric number or name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Dropdowns */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Level</label>
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {levels.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.name} ({level.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Study Mode</label>
              <Select value={modeFilter} onValueChange={setModeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All modes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Modes</SelectItem>
                  <SelectItem value="full_time">Full Time</SelectItem>
                  <SelectItem value="part_time">Part Time</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="graduated">Graduated</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button variant="outline" onClick={clearFilters} className="w-full">
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Filter Summary */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Showing {filteredStudents.length} of {totalStudents} students</span>
            {(searchTerm || levelFilter !== "all" || modeFilter !== "all" || statusFilter !== "all") && (
              <Badge variant="secondary">Filtered</Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Students List */}
      <Card>
        <CardHeader>
          <CardTitle>Students ({filteredStudents.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredStudents.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <TypographyP className="text-muted-foreground">
                {totalStudents === 0 ? "No students found in this department." : "No students match your filters."}
              </TypographyP>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredStudents.map((student) => (
                <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Badge variant="outline" className="font-mono text-xs">
                        {student.matricNumber}
                      </Badge>
                      {student.name && (
                        <span className="font-medium">{student.name}</span>
                      )}
                      <Badge variant="secondary" className="text-xs">
                        {student.gender === "male" ? "M" : "F"}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>📚 {student.levelName}</span>
                      <span>🎓 {student.studyMode === "full_time" ? "Full Time" : "Part Time"}</span>
                      {student.email && <span>📧 {student.email}</span>}
                      {student.phoneNumber && <span>📱 {student.phoneNumber}</span>}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(student.status)}>
                      {student.status.charAt(0).toUpperCase() + student.status.slice(1)}
                    </Badge>
                    <div className="text-xs text-muted-foreground">
                      {student.createdAt.toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
