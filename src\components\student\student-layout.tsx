"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import { 
  GraduationCap, 
  LogOut, 
  User, 
  Calendar,
  Building,
  BookOpen,
  Bell,
  Menu,
  X
} from "lucide-react";

interface StudentLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

interface StudentInfo {
  matricNumber: string;
  name: string;
  department: string;
  level: string;
}

export function StudentLayout({ 
  children, 
  title = "Dashboard", 
  description = "Student Portal"
}: StudentLayoutProps) {
  const [studentInfo, setStudentInfo] = useState<StudentInfo | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if student is logged in and load basic info
    const session = localStorage.getItem('student_session');
    if (!session) {
      router.push('/sign-in');
      return;
    }

    // Mock student info - replace with real data loading
    setStudentInfo({
      matricNumber: "CSC/2024/001",
      name: "John Doe",
      department: "Computer Science",
      level: "ND2"
    });
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('student_session');
    router.push('/sign-in');
  };

  const navigationItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: GraduationCap,
      description: "Overview and quick access"
    },
    {
      title: "My Exams",
      href: "/exams",
      icon: Calendar,
      description: "Exam schedule and details"
    },
    {
      title: "Hall Allocations",
      href: "/allocations",
      icon: Building,
      description: "Exam venue assignments"
    },
    {
      title: "My Courses",
      href: "/courses",
      icon: BookOpen,
      description: "Registered courses"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
              
              <Link href="/dashboard" className="flex items-center space-x-3">
                <div className="bg-green-700 p-2 rounded-lg">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Hall Automata</h1>
                  <p className="text-sm text-gray-600 hidden sm:block">{description}</p>
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-6">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors"
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </Link>
                );
              })}
            </nav>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4">
              {studentInfo && (
                <div className="hidden sm:block text-right">
                  <p className="text-sm font-medium text-gray-900">{studentInfo.name}</p>
                  <p className="text-xs text-gray-600">{studentInfo.matricNumber}</p>
                </div>
              )}
              
              <Badge variant="outline" className="font-mono hidden sm:inline-flex">
                🎓 MAPOLY
              </Badge>
              
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Sign Out</span>
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="lg:hidden mt-4 pb-4 border-t border-gray-200 pt-4">
              <nav className="space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Icon className="h-4 w-4" />
                      <div>
                        <div>{item.title}</div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                    </Link>
                  );
                })}
              </nav>
              
              {studentInfo && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="px-3 py-2">
                    <p className="text-sm font-medium text-gray-900">{studentInfo.name}</p>
                    <p className="text-xs text-gray-600">{studentInfo.matricNumber}</p>
                    <p className="text-xs text-gray-600">{studentInfo.department} • {studentInfo.level}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </header>

      {/* Page Header */}
      <div className="bg-white/60 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">{title}</h1>
            {description && (
              <TypographyP className="text-gray-600 mt-1">{description}</TypographyP>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white/60 backdrop-blur-sm border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <TypographyP className="text-sm text-gray-500">
              © 2024 Hall Automata - Moshood Abiola Polytechnic
            </TypographyP>
            <TypographyP className="text-xs text-gray-400 mt-1">
              Examination Hall Allocation System
            </TypographyP>
          </div>
        </div>
      </footer>
    </div>
  );
}
