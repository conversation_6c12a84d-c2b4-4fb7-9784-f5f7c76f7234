// Hall Automata - Complete Test Data Population Script
// Run this script to populate Firestore with comprehensive test data

const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  addDoc, 
  serverTimestamp,
  writeBatch,
  doc
} = require('firebase/firestore');

// Firebase configuration (replace with your config)
const firebaseConfig = {
  // Add your Firebase config here
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Test data structures
const faculties = [
  { name: "Faculty of Engineering", code: "ENG" },
  { name: "Faculty of Science", code: "SCI" },
  { name: "Faculty of Business Administration", code: "BUS" },
  { name: "Faculty of Environmental Studies", code: "ENV" },
  { name: "Faculty of Applied Arts", code: "ART" }
];

const departments = [
  // Engineering Faculty
  { name: "Computer Science", code: "CSC", facultyName: "Faculty of Engineering" },
  { name: "Electrical Engineering", code: "EEE", facultyName: "Faculty of Engineering" },
  { name: "Mechanical Engineering", code: "MEE", facultyName: "Faculty of Engineering" },
  { name: "Civil Engineering", code: "CVE", facultyName: "Faculty of Engineering" },
  
  // Science Faculty
  { name: "Mathematics", code: "MTH", facultyName: "Faculty of Science" },
  { name: "Physics", code: "PHY", facultyName: "Faculty of Science" },
  { name: "Chemistry", code: "CHM", facultyName: "Faculty of Science" },
  { name: "Biology", code: "BIO", facultyName: "Faculty of Science" },
  
  // Business Faculty
  { name: "Accounting", code: "ACC", facultyName: "Faculty of Business Administration" },
  { name: "Business Administration", code: "BUA", facultyName: "Faculty of Business Administration" },
  { name: "Marketing", code: "MKT", facultyName: "Faculty of Business Administration" },
  
  // Environmental Faculty
  { name: "Estate Management", code: "ESM", facultyName: "Faculty of Environmental Studies" },
  { name: "Urban Planning", code: "URP", facultyName: "Faculty of Environmental Studies" },
  
  // Arts Faculty
  { name: "Mass Communication", code: "MCM", facultyName: "Faculty of Applied Arts" },
  { name: "Fine Arts", code: "FNA", facultyName: "Faculty of Applied Arts" }
];

const levels = ["ND1", "ND2", "HND1", "HND2"];
const studyModes = ["full_time", "part_time"];

const halls = [
  { name: "Main Hall A", location: "Ground Floor, Main Building", capacity: 200 },
  { name: "Main Hall B", location: "First Floor, Main Building", capacity: 180 },
  { name: "ICT Hall A", location: "Ground Floor, ICT Building", capacity: 150 },
  { name: "ICT Hall B", location: "First Floor, ICT Building", capacity: 120 },
  { name: "Science Hall", location: "Ground Floor, Science Building", capacity: 160 },
  { name: "Engineering Hall", location: "Ground Floor, Engineering Building", capacity: 140 },
  { name: "Business Hall", location: "Ground Floor, Business Building", capacity: 130 },
  { name: "Lecture Theatre 1", location: "Academic Block A", capacity: 100 },
  { name: "Lecture Theatre 2", location: "Academic Block B", capacity: 100 },
  { name: "Conference Hall", location: "Administrative Block", capacity: 80 }
];

// Generate courses for each department and level
const generateCourses = () => {
  const courses = [];
  const courseTemplates = {
    "Computer Science": [
      { code: "CSC101", title: "Introduction to Computer Science", creditUnits: 3 },
      { code: "CSC102", title: "Computer Programming I", creditUnits: 4 },
      { code: "CSC201", title: "Data Structures and Algorithms", creditUnits: 4 },
      { code: "CSC202", title: "Database Management Systems", creditUnits: 3 },
      { code: "CSC301", title: "Software Engineering", creditUnits: 3 },
      { code: "CSC302", title: "Computer Networks", creditUnits: 3 },
      { code: "CSC401", title: "Artificial Intelligence", creditUnits: 3 },
      { code: "CSC402", title: "Project Management", creditUnits: 4 }
    ],
    "Electrical Engineering": [
      { code: "EEE101", title: "Circuit Analysis I", creditUnits: 4 },
      { code: "EEE102", title: "Electrical Measurements", creditUnits: 3 },
      { code: "EEE201", title: "Electronics I", creditUnits: 4 },
      { code: "EEE202", title: "Power Systems", creditUnits: 3 },
      { code: "EEE301", title: "Control Systems", creditUnits: 3 },
      { code: "EEE302", title: "Digital Electronics", creditUnits: 3 },
      { code: "EEE401", title: "Power Electronics", creditUnits: 3 },
      { code: "EEE402", title: "Industrial Training", creditUnits: 6 }
    ],
    "Accounting": [
      { code: "ACC101", title: "Principles of Accounting", creditUnits: 3 },
      { code: "ACC102", title: "Business Mathematics", creditUnits: 3 },
      { code: "ACC201", title: "Cost Accounting", creditUnits: 3 },
      { code: "ACC202", title: "Financial Accounting", creditUnits: 4 },
      { code: "ACC301", title: "Auditing", creditUnits: 3 },
      { code: "ACC302", title: "Taxation", creditUnits: 3 },
      { code: "ACC401", title: "Advanced Accounting", creditUnits: 4 },
      { code: "ACC402", title: "Management Accounting", creditUnits: 3 }
    ]
  };

  departments.forEach(dept => {
    const templates = courseTemplates[dept.name] || [
      { code: `${dept.code}101`, title: `Introduction to ${dept.name}`, creditUnits: 3 },
      { code: `${dept.code}102`, title: `${dept.name} Fundamentals`, creditUnits: 3 },
      { code: `${dept.code}201`, title: `Advanced ${dept.name}`, creditUnits: 4 },
      { code: `${dept.code}202`, title: `${dept.name} Applications`, creditUnits: 3 },
      { code: `${dept.code}301`, title: `${dept.name} Project I`, creditUnits: 3 },
      { code: `${dept.code}302`, title: `${dept.name} Seminar`, creditUnits: 2 },
      { code: `${dept.code}401`, title: `${dept.name} Project II`, creditUnits: 4 },
      { code: `${dept.code}402`, title: `Industrial Training`, creditUnits: 6 }
    ];

    levels.forEach((level, levelIndex) => {
      const levelCourses = templates.slice(levelIndex * 2, (levelIndex + 1) * 2);
      levelCourses.forEach(course => {
        courses.push({
          courseCode: course.code,
          courseTitle: course.title,
          departmentName: dept.name,
          facultyName: dept.facultyName,
          levelName: level,
          creditUnits: course.creditUnits,
          semester: Math.random() > 0.5 ? "First" : "Second",
          isElective: Math.random() > 0.7,
          status: "active",
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          createdBy: "admin"
        });
      });
    });
  });

  return courses;
};

// Generate students for each department and level
const generateStudents = () => {
  const students = [];
  let studentCounter = 1;

  departments.forEach(dept => {
    levels.forEach(level => {
      studyModes.forEach(studyMode => {
        const studentsCount = studyMode === 'full_time' ? 
          Math.floor(Math.random() * 30) + 40 : // 40-70 full-time students
          Math.floor(Math.random() * 20) + 15;  // 15-35 part-time students

        for (let i = 0; i < studentsCount; i++) {
          const matricNumber = `${dept.code}/${new Date().getFullYear()}/${String(studentCounter).padStart(3, '0')}`;
          const firstName = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Mary', 'James', 'Lisa', 'Robert', 'Emily'][Math.floor(Math.random() * 10)];
          const lastName = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'][Math.floor(Math.random() * 10)];
          
          students.push({
            matricNumber,
            name: `${firstName} ${lastName}`,
            departmentName: dept.name,
            facultyName: dept.facultyName,
            levelName: level,
            email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${studentCounter}@student.mapoly.edu.ng`,
            phoneNumber: `+234${Math.floor(Math.random() * **********) + **********}`,
            gender: Math.random() > 0.5 ? 'male' : 'female',
            studyMode,
            status: 'active',
            hasSetupAccount: Math.random() > 0.3, // 70% have setup accounts
            hasChangedPassword: Math.random() > 0.4, // 60% have changed passwords
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            createdBy: 'admin'
          });
          
          studentCounter++;
        }
      });
    });
  });

  return students;
};

// Generate system users (staff)
const generateSystemUsers = () => {
  const users = [];
  const roles = ['supervisor', 'invigilator'];
  const names = [
    'Dr. John Smith', 'Prof. Mary Johnson', 'Mr. David Williams', 'Mrs. Sarah Brown',
    'Dr. Michael Jones', 'Ms. Emily Garcia', 'Mr. James Miller', 'Dr. Lisa Davis',
    'Prof. Robert Rodriguez', 'Mrs. Jennifer Martinez', 'Dr. William Anderson',
    'Ms. Elizabeth Taylor', 'Mr. Christopher Thomas', 'Dr. Jessica Hernandez'
  ];

  names.forEach((name, index) => {
    const role = roles[index % 2];
    const userId = `${role.toUpperCase()}-${String(index + 1).padStart(3, '0')}`;
    const department = departments[index % departments.length];
    
    users.push({
      userId,
      name,
      role,
      department: department.name,
      email: `${name.toLowerCase().replace(/[^a-z]/g, '.')}@mapoly.edu.ng`,
      hasChangedPassword: Math.random() > 0.5,
      status: 'active',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: 'admin'
    });
  });

  return users;
};

// Generate exam timetable
const generateExamTimetable = (courses) => {
  const exams = [];
  const examDates = [];
  
  // Generate exam dates for October 2025 (5-day exam period)
  const startDate = new Date('2025-10-06'); // Monday
  for (let i = 0; i < 5; i++) {
    const examDate = new Date(startDate);
    examDate.setDate(startDate.getDate() + i);
    examDates.push(examDate);
  }

  const timeSlots = [
    { start: '09:00', end: '12:00' },
    { start: '14:00', end: '17:00' }
  ];

  // Select random courses for exams
  const selectedCourses = courses.filter(() => Math.random() > 0.6); // ~40% of courses have exams

  selectedCourses.forEach((course, index) => {
    const examDate = examDates[index % examDates.length];
    const timeSlot = timeSlots[index % timeSlots.length];
    
    exams.push({
      courseCode: course.courseCode,
      courseTitle: course.courseTitle,
      departmentName: course.departmentName,
      facultyName: course.facultyName,
      levelName: course.levelName,
      examDate: examDate,
      startTime: timeSlot.start,
      endTime: timeSlot.end,
      duration: 180, // 3 hours
      session: '2024/2025',
      semester: 'First',
      instructions: 'Bring your student ID and writing materials. No electronic devices allowed.',
      status: 'scheduled',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: 'admin'
    });
  });

  return exams;
};

// Main population function
async function populateTestData() {
  console.log('🚀 Starting test data population...');

  try {
    // Clear existing data warning
    console.log('⚠️  This will add test data to your Firestore database');
    console.log('📊 Generating test data...');

    // Generate all data
    const courses = generateCourses();
    const students = generateStudents();
    const systemUsers = generateSystemUsers();
    const examTimetable = generateExamTimetable(courses);

    console.log(`📈 Generated:
    - ${faculties.length} faculties
    - ${departments.length} departments  
    - ${courses.length} courses
    - ${students.length} students
    - ${systemUsers.length} system users
    - ${halls.length} halls
    - ${examTimetable.length} exams`);

    // Populate collections using batches for better performance
    const batch = writeBatch(db);
    let batchCount = 0;

    // Add faculties
    console.log('📝 Adding faculties...');
    for (const faculty of faculties) {
      const docRef = doc(collection(db, 'faculties'));
      batch.set(docRef, { ...faculty, createdAt: serverTimestamp() });
      batchCount++;
    }

    // Add departments
    console.log('📝 Adding departments...');
    for (const department of departments) {
      const docRef = doc(collection(db, 'departments'));
      batch.set(docRef, { ...department, createdAt: serverTimestamp() });
      batchCount++;
    }

    // Add halls
    console.log('📝 Adding halls...');
    for (const hall of halls) {
      const docRef = doc(collection(db, 'halls'));
      batch.set(docRef, { ...hall, createdAt: serverTimestamp() });
      batchCount++;
    }

    // Commit first batch
    if (batchCount > 0) {
      await batch.commit();
      console.log('✅ Basic data committed');
    }

    // Add courses in batches
    console.log('📝 Adding courses...');
    for (let i = 0; i < courses.length; i += 500) {
      const courseBatch = writeBatch(db);
      const batchCourses = courses.slice(i, i + 500);
      
      for (const course of batchCourses) {
        const docRef = doc(collection(db, 'courses'));
        courseBatch.set(docRef, course);
      }
      
      await courseBatch.commit();
      console.log(`✅ Added ${Math.min(i + 500, courses.length)} courses`);
    }

    // Add students in batches
    console.log('📝 Adding students...');
    for (let i = 0; i < students.length; i += 500) {
      const studentBatch = writeBatch(db);
      const batchStudents = students.slice(i, i + 500);
      
      for (const student of batchStudents) {
        const docRef = doc(collection(db, 'students'));
        studentBatch.set(docRef, student);
      }
      
      await studentBatch.commit();
      console.log(`✅ Added ${Math.min(i + 500, students.length)} students`);
    }

    // Add system users
    console.log('📝 Adding system users...');
    const userBatch = writeBatch(db);
    for (const user of systemUsers) {
      const docRef = doc(collection(db, 'system_users'));
      userBatch.set(docRef, user);
    }
    await userBatch.commit();

    // Add exam timetable
    console.log('📝 Adding exam timetable...');
    const examBatch = writeBatch(db);
    for (const exam of examTimetable) {
      const docRef = doc(collection(db, 'exam_timetable'));
      examBatch.set(docRef, exam);
    }
    await examBatch.commit();

    console.log('🎉 Test data population completed successfully!');
    console.log(`
📊 Summary:
✅ ${faculties.length} faculties added
✅ ${departments.length} departments added
✅ ${courses.length} courses added
✅ ${students.length} students added
✅ ${systemUsers.length} system users added
✅ ${halls.length} halls added
✅ ${examTimetable.length} exams scheduled

🔑 Admin Login:
- ID: auto-admin-id-9aa745-mapoly-exam-hall-automata
- Password: 706_113

🎯 Your Hall Automata system is now ready for testing!
    `);

  } catch (error) {
    console.error('❌ Error populating test data:', error);
  }
}

// Run the population script
populateTestData();
