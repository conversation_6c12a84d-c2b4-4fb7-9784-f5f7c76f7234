// Exam Timetable Generation Script for Hall Automata
// Creates a realistic 5-day exam schedule for all departments and levels

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, getDocs, query, where, writeBatch, doc } from 'firebase/firestore';
import { firebaseConfig } from '../lib/firebase/config';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Standard exam time slots (Nigerian polytechnic schedule)
const EXAM_TIME_SLOTS = [
  { startTime: '08:00', endTime: '10:00', duration: 120, session: 'Morning 1' },
  { startTime: '10:30', endTime: '12:30', duration: 120, session: 'Morning 2' },
  { startTime: '14:00', endTime: '16:00', duration: 120, session: 'Afternoon 1' },
  { startTime: '16:30', endTime: '18:30', duration: 120, session: 'Afternoon 2' }
];

// 5-day exam schedule (Monday to Friday) - October 2025
const EXAM_DAYS = [
  { day: 'Monday', date: new Date('2025-10-06') },
  { day: 'Tuesday', date: new Date('2025-10-07') },
  { day: 'Wednesday', date: new Date('2025-10-08') },
  { day: 'Thursday', date: new Date('2025-10-09') },
  { day: 'Friday', date: new Date('2025-10-10') }
];

// Priority courses that should be scheduled first (core courses)
const PRIORITY_COURSE_PATTERNS = [
  'mathematics', 'math', 'calculus', 'algebra',
  'english', 'communication', 'technical writing',
  'programming', 'computer', 'software',
  'physics', 'chemistry', 'biology',
  'engineering', 'mechanics', 'thermodynamics',
  'accounting', 'business', 'management'
];

interface ExamSlot {
  day: string;
  date: Date;
  timeSlot: typeof EXAM_TIME_SLOTS[0];
  assignedExams: any[];
}

interface CourseData {
  id: string;
  code: string;
  title: string;
  isElective: boolean;
  departmentId: string;
  departmentName: string;
  facultyId: string;
  facultyName: string;
  levelId: string;
  levelName: string;
}

interface LevelData {
  id: string;
  name: string;
  code: string;
  departmentId: string;
  departmentName: string;
  facultyId: string;
  facultyName: string;
}

interface SessionData {
  id: string;
  name: string;
}

interface SemesterData {
  id: string;
  name: string;
  code: string;
  sessionId: string;
}

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function isPriorityCourse(courseTitle: string): boolean {
  const title = courseTitle.toLowerCase();
  return PRIORITY_COURSE_PATTERNS.some(pattern => title.includes(pattern));
}

function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Data fetching functions
async function getActiveSession(): Promise<SessionData> {
  console.log('📅 Fetching active academic session...');
  
  const sessionsQuery = query(
    collection(db, 'academic_sessions'),
    where('isActive', '==', true)
  );
  
  const sessionsSnapshot = await getDocs(sessionsQuery);
  
  if (sessionsSnapshot.empty) {
    throw new Error('No active academic session found. Please run the database seeding first.');
  }
  
  const sessionDoc = sessionsSnapshot.docs[0];
  const sessionData = sessionDoc.data();
  
  console.log(`✅ Found active session: ${sessionData.name}`);
  
  return {
    id: sessionDoc.id,
    name: sessionData.name
  };
}

async function getFirstSemester(sessionId: string): Promise<SemesterData> {
  console.log('📚 Fetching first semester...');
  
  const semestersQuery = query(
    collection(db, 'semesters'),
    where('sessionId', '==', sessionId),
    where('code', '==', '1ST')
  );
  
  const semestersSnapshot = await getDocs(semestersQuery);
  
  if (semestersSnapshot.empty) {
    throw new Error('First semester not found. Please run the database seeding first.');
  }
  
  const semesterDoc = semestersSnapshot.docs[0];
  const semesterData = semesterDoc.data();
  
  console.log(`✅ Found first semester: ${semesterData.name}`);
  
  return {
    id: semesterDoc.id,
    name: semesterData.name,
    code: semesterData.code,
    sessionId: sessionId
  };
}

async function getAllLevels(): Promise<LevelData[]> {
  console.log('🎓 Fetching all academic levels...');
  
  const levelsSnapshot = await getDocs(collection(db, 'levels'));
  const levels: LevelData[] = [];
  
  levelsSnapshot.forEach((doc) => {
    const data = doc.data();
    levels.push({
      id: doc.id,
      name: data.name,
      code: data.code,
      departmentId: data.departmentId,
      departmentName: data.departmentName,
      facultyId: data.facultyId,
      facultyName: data.facultyName
    });
  });
  
  console.log(`✅ Found ${levels.length} academic levels`);
  return levels;
}

async function getCoursesByLevel(levelId: string): Promise<CourseData[]> {
  const coursesQuery = query(
    collection(db, 'courses'),
    where('levelId', '==', levelId),
    where('status', '==', 'active')
  );
  
  const coursesSnapshot = await getDocs(coursesQuery);
  const courses: CourseData[] = [];
  
  coursesSnapshot.forEach((doc) => {
    const data = doc.data();
    courses.push({
      id: doc.id,
      code: data.code,
      title: data.title,
      isElective: data.isElective,
      departmentId: data.departmentId,
      departmentName: data.departmentName,
      facultyId: data.facultyId,
      facultyName: data.facultyName,
      levelId: data.levelId,
      levelName: data.levelName
    });
  });
  
  return courses;
}

// Timetable generation functions
function createExamSlots(): ExamSlot[] {
  const slots: ExamSlot[] = [];
  
  for (const day of EXAM_DAYS) {
    for (const timeSlot of EXAM_TIME_SLOTS) {
      slots.push({
        day: day.day,
        date: day.date,
        timeSlot: timeSlot,
        assignedExams: []
      });
    }
  }
  
  return slots;
}

function canScheduleExam(slot: ExamSlot, course: CourseData, allSlots: ExamSlot[]): boolean {
  // Check if the same department already has an exam at this time
  const conflictingExam = slot.assignedExams.find(exam => 
    exam.departmentId === course.departmentId
  );
  
  if (conflictingExam) {
    return false;
  }
  
  // Check if students from the same level have another exam on the same day
  const sameDaySlots = allSlots.filter(s => 
    s.day === slot.day && s !== slot
  );
  
  for (const daySlot of sameDaySlots) {
    const levelConflict = daySlot.assignedExams.find(exam => 
      exam.levelId === course.levelId
    );
    
    if (levelConflict) {
      // Allow maximum 2 exams per level per day (morning and afternoon)
      const sameLevelExamsToday = sameDaySlots.reduce((count, s) => {
        return count + s.assignedExams.filter(e => e.levelId === course.levelId).length;
      }, 0);
      
      if (sameLevelExamsToday >= 2) {
        return false;
      }
    }
  }
  
  return true;
}

async function generateExamTimetable(
  session: SessionData, 
  semester: SemesterData, 
  levels: LevelData[]
): Promise<void> {
  console.log('📋 Generating comprehensive exam timetable...');
  
  const examSlots = createExamSlots();
  const allExams: any[] = [];
  
  // Get all courses for all levels
  console.log('📚 Collecting all courses...');
  const allCourses: CourseData[] = [];
  
  for (const level of levels) {
    const courses = await getCoursesByLevel(level.id);
    allCourses.push(...courses);
  }
  
  console.log(`📖 Found ${allCourses.length} total courses to schedule`);
  
  // Separate core and elective courses
  const coreCourses = allCourses.filter(c => !c.isElective);
  const electiveCourses = allCourses.filter(c => c.isElective);
  
  // Sort core courses by priority
  const priorityCoreCourses = coreCourses.filter(c => isPriorityCourse(c.title));
  const regularCoreCourses = coreCourses.filter(c => !isPriorityCourse(c.title));
  
  // Schedule courses in order of priority
  const coursesToSchedule = [
    ...shuffleArray(priorityCoreCourses),
    ...shuffleArray(regularCoreCourses),
    ...shuffleArray(electiveCourses)
  ];
  
  console.log(`🎯 Scheduling ${priorityCoreCourses.length} priority courses first`);
  console.log(`📚 Then ${regularCoreCourses.length} regular core courses`);
  console.log(`📖 Finally ${electiveCourses.length} elective courses`);
  
  let scheduledCount = 0;
  let skippedCount = 0;
  
  // Schedule each course
  for (const course of coursesToSchedule) {
    let scheduled = false;
    
    // Try to find an available slot
    for (const slot of examSlots) {
      if (canScheduleExam(slot, course, examSlots)) {
        // Create exam data
        const examData = {
          courseId: course.id,
          courseCode: course.code,
          courseTitle: course.title,
          sessionId: session.id,
          sessionName: session.name,
          semesterId: semester.id,
          semesterName: semester.name,
          facultyId: course.facultyId,
          facultyName: course.facultyName,
          departmentId: course.departmentId,
          departmentName: course.departmentName,
          levelId: course.levelId,
          levelName: course.levelName,
          examDate: slot.date,
          startTime: slot.timeSlot.startTime,
          endTime: slot.timeSlot.endTime,
          duration: slot.timeSlot.duration,
          selectedHalls: [], // Will be populated during hall allocation
          hallCapacityOverride: {},
          status: 'scheduled',
          studentCount: 0, // Will be updated when students are counted
          isAllocated: false,
          isApproved: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'timetable-generator'
        };
        
        slot.assignedExams.push(examData);
        allExams.push(examData);
        scheduled = true;
        scheduledCount++;
        
        if (scheduledCount % 50 === 0) {
          console.log(`📅 Scheduled ${scheduledCount} exams so far...`);
        }
        
        break;
      }
    }
    
    if (!scheduled) {
      skippedCount++;
      console.warn(`⚠️ Could not schedule: ${course.code} - ${course.title} (${course.departmentName})`);
    }
  }
  
  console.log(`✅ Scheduling complete: ${scheduledCount} scheduled, ${skippedCount} skipped`);
  
  // Save exams to Firestore in batches
  console.log('💾 Saving exams to database...');
  
  const batchSize = 500;
  let savedCount = 0;
  
  for (let i = 0; i < allExams.length; i += batchSize) {
    const batch = writeBatch(db);
    const examBatch = allExams.slice(i, i + batchSize);
    
    for (const examData of examBatch) {
      const examRef = doc(collection(db, 'exams'));
      batch.set(examRef, examData);
    }
    
    await batch.commit();
    savedCount += examBatch.length;
    console.log(`💾 Saved ${savedCount}/${allExams.length} exams to database`);
  }
  
  console.log('📊 Generating timetable summary...');
  
  // Generate summary
  const summary = {
    totalExams: allExams.length,
    examsByDay: {} as Record<string, number>,
    examsByTimeSlot: {} as Record<string, number>,
    examsByFaculty: {} as Record<string, number>,
    examsByLevel: {} as Record<string, number>
  };
  
  for (const exam of allExams) {
    const day = exam.examDate.toLocaleDateString('en-US', { weekday: 'long' });
    const timeSlot = `${exam.startTime}-${exam.endTime}`;
    
    summary.examsByDay[day] = (summary.examsByDay[day] || 0) + 1;
    summary.examsByTimeSlot[timeSlot] = (summary.examsByTimeSlot[timeSlot] || 0) + 1;
    summary.examsByFaculty[exam.facultyName] = (summary.examsByFaculty[exam.facultyName] || 0) + 1;
    summary.examsByLevel[exam.levelName] = (summary.examsByLevel[exam.levelName] || 0) + 1;
  }
  
  console.log('\n📊 EXAM TIMETABLE SUMMARY');
  console.log('========================');
  console.log(`📚 Total Exams Scheduled: ${summary.totalExams}`);
  console.log(`⏰ Exam Period: 5 days (${EXAM_DAYS[0].day} to ${EXAM_DAYS[4].day})`);
  console.log(`🕐 Time Slots: 4 per day (${EXAM_TIME_SLOTS.length} slots total)`);
  
  console.log('\n📅 Exams by Day:');
  Object.entries(summary.examsByDay).forEach(([day, count]) => {
    console.log(`  ${day}: ${count} exams`);
  });
  
  console.log('\n🕐 Exams by Time Slot:');
  Object.entries(summary.examsByTimeSlot).forEach(([slot, count]) => {
    console.log(`  ${slot}: ${count} exams`);
  });
  
  console.log('\n🏛️ Exams by Faculty:');
  Object.entries(summary.examsByFaculty).forEach(([faculty, count]) => {
    console.log(`  ${faculty}: ${count} exams`);
  });
  
  console.log('\n🎓 Exams by Level:');
  Object.entries(summary.examsByLevel).forEach(([level, count]) => {
    console.log(`  ${level}: ${count} exams`);
  });
}

// Main function
export async function generateExamTimetableScript() {
  console.log('📋 Hall Automata Exam Timetable Generator');
  console.log('=========================================');
  console.log('');
  
  try {
    // Get active session and semester
    const session = await getActiveSession();
    const semester = await getFirstSemester(session.id);
    
    // Get all levels
    const levels = await getAllLevels();
    
    if (levels.length === 0) {
      throw new Error('No academic levels found. Please run the database seeding first.');
    }
    
    // Generate the timetable
    await generateExamTimetable(session, semester, levels);
    
    console.log('\n🎉 Exam timetable generation completed successfully!');
    console.log('\n🚀 Next Steps:');
    console.log('1. Go to /admin/hall-allocation');
    console.log('2. Select the 2024/2025 session');
    console.log('3. Choose First Semester');
    console.log('4. Select multiple exams for allocation');
    console.log('5. Test the hall allocation system!');
    console.log('');
    
  } catch (error) {
    console.error('❌ Error generating exam timetable:', error);
    throw error;
  }
}

// Run if executed directly
if (require.main === module) {
  generateExamTimetableScript()
    .then(() => {
      console.log('✅ Timetable generation completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Timetable generation failed:', error);
      process.exit(1);
    });
}
