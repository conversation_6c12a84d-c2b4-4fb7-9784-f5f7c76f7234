# 👨‍💻 Hall Automata: Development Guide

## 🚀 Getting Started

This guide will help you set up the development environment and start contributing to Hall Automata.

---

## 📋 Prerequisites

### Required Software

#### Node.js and Package Manager

```bash
# Install Node.js 18+ (recommended: use nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Verify installation
node --version  # Should be 18.x.x or higher
npm --version   # Should be 9.x.x or higher
```

#### Git

```bash
# Install Git (if not already installed)
# Windows: Download from https://git-scm.com/
# macOS: brew install git
# Linux: sudo apt-get install git

# Verify installation
git --version
```

#### Code Editor

- **Recommended**: Visual Studio Code with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint

### Firebase Account Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use existing one
3. Enable Authentication, Firestore, and Hosting
4. Generate configuration keys (we'll use these later)

---

## 🛠️ Environment Setup

### 1. Clone the Repository

```bash
# Clone the project
git clone <repository-url>
cd hall-automata

# Create and switch to development branch
git checkout -b develop
```

### 2. Install Dependencies

```bash
# Install all project dependencies
npm install

# Verify installation
npm list --depth=0
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit the environment file
# Add your Firebase configuration
```

#### Environment Variables (.env.local)

```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Application Configuration
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Biometric Configuration (optional for development)
NEXT_PUBLIC_BIOMETRIC_ENABLED=false
NEXT_PUBLIC_BIOMETRIC_DEBUG=true
```

### 4. Firebase Setup

```bash
# Install Firebase CLI globally
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in project
firebase init

# Select the following services:
# - Firestore
# - Hosting
# - Storage (optional)
```

#### Firestore Security Rules (firestore.rules)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // More specific rules will be added during development
  }
}
```

### 5. Start Development Server

```bash
# Start the development server
npm run dev

# Open browser and navigate to:
# http://localhost:3000
```

---

## 📁 Project Structure

### Directory Overview

```
hall-automata/
├── src/                          # Source code
│   ├── app/                      # Next.js App Router
│   │   ├── (auth)/              # Authentication routes
│   │   ├── admin/               # Admin dashboard
│   │   ├── invigilator/         # Invigilator portal
│   │   ├── student/             # Student portal
│   │   ├── api/                 # API routes
│   │   ├── globals.css          # Global styles
│   │   ├── layout.tsx           # Root layout
│   │   └── page.tsx             # Home page
│   ├── components/              # Reusable components
│   │   ├── ui/                  # shadcn/ui components
│   │   ├── forms/               # Form components
│   │   ├── tables/              # Data tables
│   │   ├── charts/              # Chart components
│   │   ├── biometric/           # Biometric components
│   │   └── layout/              # Layout components
│   ├── hooks/                   # Custom React hooks
│   │   ├── useAuth.ts           # Authentication
│   │   ├── useBiometric.ts      # Biometric operations
│   │   ├── useOfflineSync.ts    # Offline sync
│   │   └── useRealtime.ts       # Real-time data
│   ├── lib/                     # Utility libraries
│   │   ├── firebase.ts          # Firebase config
│   │   ├── biometric.ts         # Biometric utilities
│   │   ├── utils.ts             # General utilities
│   │   └── validations.ts       # Form validations
│   ├── types/                   # TypeScript definitions
│   │   ├── auth.ts              # Auth types
│   │   ├── student.ts           # Student types
│   │   ├── exam.ts              # Exam types
│   │   └── attendance.ts        # Attendance types
│   └── styles/                  # Additional styles
├── docs/                        # Documentation
├── public/                      # Static assets
├── tests/                       # Test files
│   ├── __mocks__/              # Test mocks
│   ├── components/             # Component tests
│   ├── hooks/                  # Hook tests
│   ├── lib/                    # Utility tests
│   └── e2e/                    # End-to-end tests
├── .env.example                # Environment template
├── .env.local                  # Local environment (gitignored)
├── firebase.json               # Firebase configuration
├── firestore.rules            # Firestore security rules
├── next.config.ts             # Next.js configuration
├── package.json               # Dependencies and scripts
├── tailwind.config.ts         # Tailwind configuration
└── tsconfig.json              # TypeScript configuration
```

### Key Files Explained

#### `src/app/layout.tsx` - Root Layout

```typescript
import type { Metadata } from "next";
import { Geist, Geist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Hall Automata",
  description: "Smart Exam Hall Allocation & Biometric Attendance System",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
```

#### `src/lib/firebase.ts` - Firebase Configuration

```typescript
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;
```

#### `src/lib/utils.ts` - Utility Functions

```typescript
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}
```

---

## 🔧 Development Workflow

### Branch Strategy

```bash
# Main branches
main                    # Production-ready code
staging                 # Pre-production testing
develop                 # Integration branch

# Feature branches
feature/authentication  # New features
feature/biometric      # Biometric integration
feature/hall-allocation # Hall allocation system

# Bug fix branches
bugfix/login-issue     # Bug fixes
hotfix/security-patch  # Critical fixes
```

### Creating a New Feature

```bash
# 1. Start from develop branch
git checkout develop
git pull origin develop

# 2. Create feature branch
git checkout -b feature/your-feature-name

# 3. Make your changes
# ... code changes ...

# 4. Commit changes
git add .
git commit -m "feat: add your feature description"

# 5. Push to remote
git push origin feature/your-feature-name

# 6. Create pull request to develop branch
```

### Commit Message Convention

```bash
# Format: type(scope): description

# Types:
feat:     # New feature
fix:      # Bug fix
docs:     # Documentation changes
style:    # Code style changes (formatting, etc.)
refactor: # Code refactoring
test:     # Adding or updating tests
chore:    # Maintenance tasks

# Examples:
feat(auth): add user authentication system
fix(biometric): resolve scanner connection issue
docs(api): update API documentation
test(components): add unit tests for Button component
```

---

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run end-to-end tests
npm run test:e2e

# Run specific test file
npm test -- Button.test.tsx
```

### Writing Tests

#### Unit Test Example

```typescript
// tests/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  it('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

#### Integration Test Example

```typescript
// tests/api/students.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/app/api/students/route';

describe('/api/students', () => {
  it('creates a new student', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        matricNumber: 'CS/2021/001',
        firstName: 'John',
        lastName: 'Doe',
        departmentId: 'dept1'
      }
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(201);
    expect(JSON.parse(res._getData())).toMatchObject({
      matricNumber: 'CS/2021/001',
      firstName: 'John'
    });
  });
});
```

### Test Coverage Goals

- **Unit Tests**: 80% minimum coverage
- **Integration Tests**: All API endpoints
- **E2E Tests**: Critical user workflows
- **Component Tests**: All UI components

---

## 🎨 UI Development

### Using shadcn/ui Components

```bash
# Add new shadcn/ui components
npx shadcn@latest add button
npx shadcn@latest add input
npx shadcn@latest add table
npx shadcn@latest add dialog

# Components will be added to src/components/ui/
```

### Creating Custom Components

```typescript
// src/components/StudentCard.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Student } from '@/types/student';

interface StudentCardProps {
  student: Student;
  onEdit?: (student: Student) => void;
}

export function StudentCard({ student, onEdit }: StudentCardProps) {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {student.firstName} {student.lastName}
          <Badge variant="secondary">{student.studyMode}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          {student.matricNumber}
        </p>
        <p className="text-sm">
          {student.department} - {student.level}
        </p>
      </CardContent>
    </Card>
  );
}
```

### Styling Guidelines

```typescript
// Use Tailwind CSS classes
className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md"

// Use CSS variables for theming
className="bg-background text-foreground border-border"

// Use the cn() utility for conditional classes
className={cn(
  "base-classes",
  isActive && "active-classes",
  variant === "primary" && "primary-classes"
)}
```

---

## 🔌 API Development

### Creating API Routes

```typescript
// src/app/api/students/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs } from 'firebase/firestore';

export async function GET() {
  try {
    const studentsRef = collection(db, 'students');
    const snapshot = await getDocs(studentsRef);
    const students = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json(students);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch students' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const studentsRef = collection(db, 'students');
    const docRef = await addDoc(studentsRef, {
      ...body,
      createdAt: new Date(),
      isActive: true
    });

    return NextResponse.json(
      { id: docRef.id, ...body },
      { status: 201 }
    );
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create student' },
      { status: 500 }
    );
  }
}
```

### API Error Handling

```typescript
// src/lib/api-errors.ts
export class APIError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export function handleAPIError(error: unknown) {
  if (error instanceof APIError) {
    return NextResponse.json(
      { error: error.message, code: error.code },
      { status: error.statusCode }
    );
  }

  console.error('Unexpected API error:', error);
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  );
}
```

---

## 🔐 Authentication Implementation

### Auth Hook

```typescript
// src/hooks/useAuth.ts
import { useEffect, useState } from 'react';
import { User, onAuthStateChanged, signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { auth } from '@/lib/firebase';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const login = async (email: string, password: string) => {
    return signInWithEmailAndPassword(auth, email, password);
  };

  const logout = async () => {
    return signOut(auth);
  };

  return {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user
  };
}
```

### Protected Routes

```typescript
// src/components/ProtectedRoute.tsx
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
}
```

---

## 📱 Biometric Integration

### Biometric Hook

```typescript
// src/hooks/useBiometric.ts
import { useState, useCallback } from 'react';

interface BiometricDevice {
  id: string;
  name: string;
  connected: boolean;
}

export function useBiometric() {
  const [devices, setDevices] = useState<BiometricDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<BiometricDevice | null>(null);
  const [isScanning, setIsScanning] = useState(false);

  const detectDevices = useCallback(async () => {
    try {
      // WebUSB device detection
      const devices = await navigator.usb.getDevices();
      const biometricDevices = devices.map(device => ({
        id: device.serialNumber || 'unknown',
        name: device.productName || 'Unknown Scanner',
        connected: true
      }));
      
      setDevices(biometricDevices);
    } catch (error) {
      console.error('Failed to detect biometric devices:', error);
    }
  }, []);

  const scanFingerprint = useCallback(async () => {
    if (!selectedDevice) {
      throw new Error('No device selected');
    }

    setIsScanning(true);
    try {
      // Implement actual biometric scanning logic
      // This is a placeholder for the actual implementation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Return mock template for development
      return 'mock-biometric-template-' + Date.now();
    } finally {
      setIsScanning(false);
    }
  }, [selectedDevice]);

  return {
    devices,
    selectedDevice,
    isScanning,
    detectDevices,
    setSelectedDevice,
    scanFingerprint
  };
}
```

---

## 🚀 Deployment

### Development Deployment

```bash
# Build the application
npm run build

# Test the build locally
npm start

# Deploy to Firebase Hosting
firebase deploy --only hosting
```

### Environment-Specific Deployment

```bash
# Deploy to staging
firebase use staging
firebase deploy

# Deploy to production
firebase use production
firebase deploy
```

### Continuous Deployment

```yaml
# .github/workflows/deploy.yml
name: Deploy to Firebase

on:
  push:
    branches: [main, staging]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
      
      - name: Deploy to Firebase
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          projectId: hall-automata-prod
```

---

## 🐛 Debugging

### Common Issues and Solutions

#### Firebase Connection Issues

```bash
# Check Firebase configuration
firebase projects:list

# Verify environment variables
echo $NEXT_PUBLIC_FIREBASE_PROJECT_ID

# Test Firebase connection
firebase firestore:indexes
```

#### Build Errors

```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check TypeScript errors
npx tsc --noEmit
```

#### Biometric Scanner Issues

```bash
# Check browser support
console.log('WebUSB supported:', 'usb' in navigator);
console.log('Web Bluetooth supported:', 'bluetooth' in navigator);

# Enable experimental features in Chrome
# chrome://flags/#enable-experimental-web-platform-features
```

### Development Tools

#### Browser DevTools

- **React Developer Tools**: Debug React components
- **Firebase DevTools**: Monitor Firebase operations
- **Network Tab**: Debug API calls and performance
- **Console**: Check for JavaScript errors

#### VS Code Extensions

- **Thunder Client**: API testing within VS Code
- **Firebase Explorer**: Browse Firebase data
- **GitLens**: Enhanced Git integration
- **Error Lens**: Inline error highlighting

---

## 📚 Resources

### Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com/)

### Learning Resources

- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)
- [Firebase Web Codelab](https://firebase.google.com/codelabs/firebase-web)
- [Next.js Learn Course](https://nextjs.org/learn)

### Community

- [Next.js Discord](https://discord.gg/nextjs)
- [Firebase Community](https://firebase.google.com/community)
- [React Community](https://reactjs.org/community/support.html)

---

## 🤝 Contributing

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

### Pull Request Process

1. Create feature branch from `develop`
2. Make changes and add tests
3. Ensure all tests pass
4. Update documentation if needed
5. Create pull request to `develop`
6. Request code review
7. Address review feedback
8. Merge after approval

### Getting Help

- Check existing issues and documentation first
- Create detailed issue reports with reproduction steps
- Join development discussions in project channels
- Ask questions in code reviews

---

## 🎨 Professional UI Design Guidelines

### Design System & Theme Configuration

#### Color Palette (Professional Academic Theme)

```typescript
// tailwind.config.ts - Professional Color Scheme
const config = {
  theme: {
    extend: {
      colors: {
        // Primary Brand Colors
        primary: {
          50: '#eff6ff',   // Very light blue
          100: '#dbeafe',  // Light blue
          500: '#3b82f6',  // Main blue (professional)
          600: '#2563eb',  // Darker blue
          900: '#1e3a8a',  // Very dark blue
        },
        // Secondary Colors
        secondary: {
          50: '#f8fafc',   // Light gray
          100: '#f1f5f9',  // Lighter gray
          500: '#64748b',  // Medium gray
          600: '#475569',  // Darker gray
          900: '#0f172a',  // Very dark gray
        },
        // Success/Error States
        success: {
          50: '#f0fdf4',   // Light green
          500: '#22c55e',  // Success green
          600: '#16a34a',  // Darker green
        },
        error: {
          50: '#fef2f2',   // Light red
          500: '#ef4444',  // Error red
          600: '#dc2626',  // Darker red
        },
        warning: {
          50: '#fffbeb',   // Light yellow
          500: '#f59e0b',  // Warning yellow
          600: '#d97706',  // Darker yellow
        },
        // Neutral Grays (Professional)
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          900: '#171717',
        }
      },
      // Professional Typography
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
        display: ['Poppins', 'system-ui', 'sans-serif'],
      },
      // Professional Spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Professional Shadows
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1)',
      },
      // Professional Border Radius
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      }
    }
  }
}
```

#### Professional Font Setup

```typescript
// src/app/layout.tsx - Professional Typography
import { Inter, JetBrains_Mono, Poppins } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable} ${poppins.variable}`}>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
```

### Professional Component Library Setup

#### Enhanced shadcn/ui Configuration

```json
// components.json - Professional Configuration
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "src/app/globals.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks",
    "types": "@/types"
  },
  "iconLibrary": "lucide"
}
```

#### Professional Global Styles

```css
/* src/app/globals.css - Professional Styling */
@import "tailwindcss";
@import "tw-animate-css";

/* Professional CSS Variables */
@layer base {
  :root {
    /* Light Theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Professional Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .dark {
    /* Dark Theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

/* Professional Base Styles */
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Professional Scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

/* Professional Animations */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
```

### Professional Component Examples

#### Professional Dashboard Layout

```typescript
// src/components/layout/DashboardLayout.tsx
import { cn } from '@/lib/utils';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

interface DashboardLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-screen">
        {/* Sidebar */}
        <aside className="hidden lg:flex lg:w-64 lg:flex-col">
          <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-card border-r border-border">
            <Sidebar />
          </div>
        </aside>

        {/* Main Content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          <Header />
          <main className={cn(
            "flex-1 overflow-y-auto bg-muted/10 p-6",
            className
          )}>
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
```

#### Professional Card Component

```typescript
// src/components/ui/professional-card.tsx
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ProfessionalCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  headerAction?: React.ReactNode;
  variant?: 'default' | 'gradient' | 'bordered';
}

export function ProfessionalCard({
  title,
  description,
  children,
  className,
  headerAction,
  variant = 'default'
}: ProfessionalCardProps) {
  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-medium",
      variant === 'gradient' && "bg-gradient-to-br from-card to-muted/20",
      variant === 'bordered' && "border-2 border-primary/20",
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-xl font-semibold tracking-tight">
              {title}
            </CardTitle>
            {description && (
              <CardDescription className="text-muted-foreground">
                {description}
              </CardDescription>
            )}
          </div>
          {headerAction}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {children}
      </CardContent>
    </Card>
  );
}
```

### Professional Icons & Illustrations

#### Recommended Icon Libraries

```bash
# Install professional icon libraries
npm install lucide-react @heroicons/react @tabler/icons-react

# For custom illustrations
npm install @phosphor-icons/react react-icons
```

#### Professional Icon Usage

```typescript
// src/components/icons/index.tsx
import {
  Users,
  Calendar,
  BarChart3,
  Settings,
  Shield,
  Fingerprint,
  Building,
  GraduationCap,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  TrendingUp,
  Eye,
  Download,
  Upload,
  Search,
  Filter,
  MoreHorizontal,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Menu,
  Bell,
  User,
  LogOut,
  Home,
  FileText,
  Database,
  Wifi,
  WifiOff
} from 'lucide-react';

// Export organized icon groups
export const NavigationIcons = {
  Home,
  Users,
  Calendar,
  BarChart3,
  Settings,
  Database,
  FileText
};

export const ActionIcons = {
  Plus,
  Edit,
  Trash2,
  Save,
  Search,
  Filter,
  Download,
  Upload,
  Eye,
  MoreHorizontal
};

export const StatusIcons = {
  CheckCircle,
  AlertCircle,
  XCircle,
  Clock,
  TrendingUp,
  Shield,
  Fingerprint
};

export const SystemIcons = {
  Menu,
  X,
  Bell,
  User,
  LogOut,
  Wifi,
  WifiOff,
  Building,
  GraduationCap
};
```

### Professional Data Visualization

#### Chart Configuration (Recharts)

```typescript
// src/components/charts/ProfessionalChart.tsx
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

const chartTheme = {
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  grid: {
    stroke: '#e2e8f0',
    strokeDasharray: '3 3',
  },
  tooltip: {
    backgroundColor: '#ffffff',
    border: '1px solid #e2e8f0',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  }
};

interface AttendanceChartProps {
  data: Array<{
    date: string;
    present: number;
    absent: number;
    total: number;
  }>;
}

export function AttendanceChart({ data }: AttendanceChartProps) {
  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid {...chartTheme.grid} />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12, fill: '#64748b' }}
            axisLine={{ stroke: '#e2e8f0' }}
          />
          <YAxis
            tick={{ fontSize: 12, fill: '#64748b' }}
            axisLine={{ stroke: '#e2e8f0' }}
          />
          <Tooltip
            contentStyle={chartTheme.tooltip}
            labelStyle={{ color: '#1e293b', fontWeight: 600 }}
          />
          <Legend />
          <Bar
            dataKey="present"
            fill={chartTheme.colors.success}
            name="Present"
            radius={[4, 4, 0, 0]}
          />
          <Bar
            dataKey="absent"
            fill={chartTheme.colors.error}
            name="Absent"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
```

### Professional Form Design

#### Enhanced Form Components

```typescript
// src/components/forms/ProfessionalForm.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const formSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  matricNumber: z.string().regex(/^[A-Z]{2}\/\d{4}\/\d{3}$/, 'Invalid matric number format'),
});

type FormData = z.infer<typeof formSchema>;

export function StudentRegistrationForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    resolver: zodResolver(formSchema)
  });

  const onSubmit = async (data: FormData) => {
    // Handle form submission
    console.log(data);
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold">Student Registration</CardTitle>
        <CardDescription>
          Enter student information to create a new account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                {...register('firstName')}
                className={errors.firstName ? 'border-destructive' : ''}
                placeholder="Enter first name"
              />
              {errors.firstName && (
                <p className="text-sm text-destructive">{errors.firstName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                {...register('lastName')}
                className={errors.lastName ? 'border-destructive' : ''}
                placeholder="Enter last name"
              />
              {errors.lastName && (
                <p className="text-sm text-destructive">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              className={errors.email ? 'border-destructive' : ''}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="matricNumber">Matric Number</Label>
            <Input
              id="matricNumber"
              {...register('matricNumber')}
              className={errors.matricNumber ? 'border-destructive' : ''}
              placeholder="CS/2021/001"
            />
            {errors.matricNumber && (
              <p className="text-sm text-destructive">{errors.matricNumber.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline">
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Student'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
```

---

**Happy coding! 🎉**

*For more detailed information, refer to the [System Architecture Plan](./SYSTEM_ARCHITECTURE.md) and [Project Documentation](./PROJECT_DOCUMENTATION.md).*
