// Firebase App Initialization
import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { firebaseConfig } from './config';

// Initialize Firebase App (singleton pattern)
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

console.log('🔥 Firebase initialized with project:', firebaseConfig.projectId);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

console.log('🔥 Firebase services initialized:', {
  auth: !!auth,
  db: !!db,
  storage: !!storage
});

// Connect to Firebase emulators in development (optional)
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Uncomment these lines if you want to use Firebase emulators for local development
  
  // if (!auth._delegate._config.emulator) {
  //   connectAuthEmulator(auth, 'http://localhost:9099');
  // }
  
  // if (!db._delegate._databaseId.projectId.includes('demo-')) {
  //   connectFirestoreEmulator(db, 'localhost', 8080);
  // }
  
  // if (!storage._delegate._host.includes('localhost')) {
  //   connectStorageEmulator(storage, 'localhost', 9199);
  // }
}

export default app;
