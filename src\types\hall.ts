// Hall Management Types
export interface Hall {
  id: string;
  name: string;
  code: string; // e.g., "LT1", "LAB-A", "HALL-001"
  capacity: number;
  location: string; // Building or area
  type: HallType;
  status: HallStatus;
  facilities: string[]; // e.g., ["Projector", "AC", "Sound System"]
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // Admin ID
}

export type HallType = 
  | "lecture_hall" 
  | "laboratory" 
  | "auditorium" 
  | "classroom" 
  | "computer_lab" 
  | "examination_hall";

export type HallStatus = 
  | "active" 
  | "maintenance" 
  | "unavailable" 
  | "reserved";

export interface HallFormData {
  name: string;
  code: string;
  capacity: number;
  location: string;
  type: HallType;
  status: HallStatus;
  facilities: string[];
  description?: string;
}

// Hall availability for exam scheduling
export interface HallAvailability {
  hallId: string;
  date: string;
  timeSlots: TimeSlot[];
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  examId?: string; // If occupied
}

// Hall allocation for exams
export interface HallAllocation {
  id: string;
  examId: string;
  hallId: string;
  studentCount: number;
  supervisorIds: string[];
  seatArrangement?: SeatArrangement;
  createdAt: Date;
}

export interface SeatArrangement {
  totalSeats: number;
  occupiedSeats: number;
  seatMap?: string; // JSON string of seat layout
}
