"use client";

import { useState, useEffect } from "react";
import { StudentLayout } from "@/components/student/student-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { TypographyH2, TypographyP } from "@/components/ui/typography";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Building, 
  MapPin, 
  Calendar, 
  Clock, 
  Search,
  Filter,
  Download,
  RefreshCw,
  Loader2,
  AlertCircle,
  Info,
  Navigation,
  Users,
  Printer
} from "lucide-react";

interface HallAllocation {
  id: string;
  courseCode: string;
  courseTitle: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  hallName: string;
  hallLocation: string;
  hallCapacity: number;
  seatNumber: string;
  rowNumber: string;
  instructions: string;
  status: 'upcoming' | 'ongoing' | 'completed';
  studentsCount: number;
}

export default function StudentAllocationsPage() {
  const [allocations, setAllocations] = useState<HallAllocation[]>([]);
  const [filteredAllocations, setFilteredAllocations] = useState<HallAllocation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  useEffect(() => {
    loadAllocations();
  }, []);

  useEffect(() => {
    filterAllocations();
  }, [allocations, searchTerm, selectedStatus]);

  const loadAllocations = async () => {
    setIsLoading(true);
    try {
      // Simulate loading data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with real Firebase queries
      const mockAllocations: HallAllocation[] = [
        {
          id: '1',
          courseCode: 'CSC201',
          courseTitle: 'Data Structures & Algorithms',
          examDate: new Date('2024-12-15'),
          startTime: '09:00',
          endTime: '12:00',
          hallName: 'Main Hall A',
          hallLocation: 'Ground Floor, Main Building',
          hallCapacity: 200,
          seatNumber: 'A-045',
          rowNumber: 'Row 5',
          instructions: 'Bring your student ID and writing materials. No electronic devices allowed except approved calculators.',
          status: 'upcoming',
          studentsCount: 180
        },
        {
          id: '2',
          courseCode: 'CSC202',
          courseTitle: 'Database Management Systems',
          examDate: new Date('2024-12-17'),
          startTime: '14:00',
          endTime: '17:00',
          hallName: 'ICT Hall B',
          hallLocation: 'First Floor, ICT Building',
          hallCapacity: 150,
          seatNumber: 'B-023',
          rowNumber: 'Row 3',
          instructions: 'This is a practical exam. Laptops will be provided. Bring your student ID.',
          status: 'upcoming',
          studentsCount: 120
        },
        {
          id: '3',
          courseCode: 'MTH201',
          courseTitle: 'Advanced Mathematics',
          examDate: new Date('2024-12-20'),
          startTime: '09:00',
          endTime: '12:00',
          hallName: 'Science Hall C',
          hallLocation: 'Ground Floor, Science Building',
          hallCapacity: 180,
          seatNumber: 'C-067',
          rowNumber: 'Row 7',
          instructions: 'Scientific calculators are allowed. Show all working clearly.',
          status: 'upcoming',
          studentsCount: 165
        },
        {
          id: '4',
          courseCode: 'CSC101',
          courseTitle: 'Introduction to Computer Science',
          examDate: new Date('2024-12-05'),
          startTime: '09:00',
          endTime: '12:00',
          hallName: 'Main Hall A',
          hallLocation: 'Ground Floor, Main Building',
          hallCapacity: 200,
          seatNumber: 'A-089',
          rowNumber: 'Row 9',
          instructions: 'Multiple choice and essay questions. Use blue or black ink only.',
          status: 'completed',
          studentsCount: 195
        }
      ];
      
      setAllocations(mockAllocations);
    } catch (error) {
      console.error('Error loading allocations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterAllocations = () => {
    let filtered = allocations;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(allocation =>
        allocation.courseCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        allocation.courseTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        allocation.hallName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (selectedStatus !== "all") {
      filtered = filtered.filter(allocation => allocation.status === selectedStatus);
    }

    setFilteredAllocations(filtered);
  };

  const getStatusBadge = (status: HallAllocation['status']) => {
    switch (status) {
      case 'upcoming':
        return <Badge className="bg-blue-600">Upcoming</Badge>;
      case 'ongoing':
        return <Badge className="bg-green-600">Ongoing</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    return new Date(`2024-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const printAllocation = (allocation: HallAllocation) => {
    // Create a printable version
    const printContent = `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h1>Exam Hall Allocation</h1>
        <h2>Moshood Abiola Polytechnic</h2>
        <hr>
        <p><strong>Course:</strong> ${allocation.courseCode} - ${allocation.courseTitle}</p>
        <p><strong>Date:</strong> ${formatDate(allocation.examDate)}</p>
        <p><strong>Time:</strong> ${formatTime(allocation.startTime)} - ${formatTime(allocation.endTime)}</p>
        <p><strong>Hall:</strong> ${allocation.hallName}</p>
        <p><strong>Location:</strong> ${allocation.hallLocation}</p>
        <p><strong>Seat Number:</strong> ${allocation.seatNumber}</p>
        <p><strong>Row:</strong> ${allocation.rowNumber}</p>
        <hr>
        <p><strong>Instructions:</strong></p>
        <p>${allocation.instructions}</p>
        <hr>
        <p><em>Please arrive 30 minutes before the exam time.</em></p>
      </div>
    `;
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const upcomingCount = allocations.filter(a => a.status === 'upcoming').length;
  const completedCount = allocations.filter(a => a.status === 'completed').length;

  return (
    <StudentLayout
      title="Hall Allocations"
      description="View your exam venue assignments and seat numbers"
    >
      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 lg:gap-6 mb-8">
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Allocations</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{allocations.length}</div>
            <p className="text-xs text-muted-foreground">This semester</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{upcomingCount}</div>
            <p className="text-xs text-muted-foreground">Exams scheduled</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{completedCount}</div>
            <p className="text-xs text-muted-foreground">Exams taken</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/80 backdrop-blur-sm mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <Button variant="outline" onClick={loadAllocations} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by course code, title, or hall..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="upcoming">Upcoming</option>
              <option value="ongoing">Ongoing</option>
              <option value="completed">Completed</option>
            </select>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredAllocations.length} of {allocations.length} allocations
          </div>
        </CardContent>
      </Card>

      {/* Allocations List */}
      {isLoading ? (
        <div className="text-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <TypographyP className="text-gray-600">Loading your hall allocations...</TypographyP>
        </div>
      ) : filteredAllocations.length === 0 ? (
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <TypographyP className="text-gray-600 mb-2">
              {searchTerm || selectedStatus !== "all" 
                ? "No allocations match your search criteria"
                : "No hall allocations found"
              }
            </TypographyP>
            <TypographyP className="text-sm text-gray-500">
              Your exam hall assignments will appear here when available
            </TypographyP>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4 lg:space-y-6">
          {filteredAllocations.map((allocation) => (
            <Card key={allocation.id} className="bg-white/80 backdrop-blur-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-900">
                      {allocation.courseCode} - {allocation.courseTitle}
                    </CardTitle>
                    <TypographyP className="text-gray-600 mt-1">
                      {formatDate(allocation.examDate)} • {formatTime(allocation.startTime)} - {formatTime(allocation.endTime)}
                    </TypographyP>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(allocation.status)}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => printAllocation(allocation)}
                    >
                      <Printer className="h-4 w-4 mr-2" />
                      Print
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Hall Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Building className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">{allocation.hallName}</p>
                        <p className="text-sm text-gray-600">{allocation.hallLocation}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium text-gray-900">Seat: {allocation.seatNumber}</p>
                        <p className="text-sm text-gray-600">{allocation.rowNumber}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-purple-600" />
                      <div>
                        <p className="font-medium text-gray-900">Capacity: {allocation.hallCapacity}</p>
                        <p className="text-sm text-gray-600">{allocation.studentsCount} students allocated</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-orange-600" />
                      <div>
                        <p className="font-medium text-gray-900">Duration: 3 hours</p>
                        <p className="text-sm text-gray-600">Arrive 30 minutes early</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Instructions */}
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Exam Instructions:</strong> {allocation.instructions}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </StudentLayout>
  );
}
