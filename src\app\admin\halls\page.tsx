"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { AddHallForm } from "@/components/admin/add-hall-form";
import { EditHallForm } from "@/components/admin/edit-hall-form";
import { HallList } from "@/components/admin/hall-list";
import { HallDetailsModal } from "@/components/admin/hall-details-modal";
import { DeleteHallDialog } from "@/components/admin/delete-hall-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import {
  MapPin,
  Plus,
  Users,
  Building,
  Settings,
  BarChart3,
  AlertCircle,
  Loader2
} from "lucide-react";
import { Hall, HallFormData } from "@/types/hall";
import {
  addHall,
  getAllHalls,
  updateHall,
  deleteHall as deleteHallFromFirestore
} from "@/lib/firebase/hall-service";

type ViewMode = 'list' | 'add' | 'edit' | 'view' | 'delete';

export default function HallManagementPage() {
  const [halls, setHalls] = useState<Hall[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedHall, setSelectedHall] = useState<Hall | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingHalls, setIsLoadingHalls] = useState(true);
  const [error, setError] = useState<string>("");

  // Load halls from Firestore on component mount
  useEffect(() => {
    loadHalls();
  }, []);

  const loadHalls = async () => {
    try {
      setIsLoadingHalls(true);
      setError("");
      const hallsData = await getAllHalls();
      setHalls(hallsData);
      console.log(`✅ Loaded ${hallsData.length} halls from Firestore`);
    } catch (error) {
      console.error("❌ Error loading halls:", error);
      setError(error instanceof Error ? error.message : "Failed to load halls");
    } finally {
      setIsLoadingHalls(false);
    }
  };

  const handleAddHall = async (formData: HallFormData) => {
    setIsLoading(true);
    setError("");

    try {
      // TODO: Get actual admin ID from auth context
      const createdBy = "admin"; // Replace with actual admin ID

      const newHall = await addHall(formData, createdBy);
      setHalls(prev => [newHall, ...prev]); // Add to beginning of list
      setViewMode('list');

      console.log("✅ Hall added successfully:", newHall);
    } catch (error) {
      console.error("❌ Error adding hall:", error);
      setError(error instanceof Error ? error.message : "Failed to add hall");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditHall = (hall: Hall) => {
    setSelectedHall(hall);
    setViewMode('edit');
  };

  const handleUpdateHall = async (hallId: string, updates: Partial<HallFormData>) => {
    setIsLoading(true);
    setError("");

    try {
      const updatedHall = await updateHall(hallId, updates);
      setHalls(prev => prev.map(h => h.id === hallId ? updatedHall : h));
      setViewMode('list');
      setSelectedHall(null);

      console.log("✅ Hall updated successfully:", updatedHall);
    } catch (error) {
      console.error("❌ Error updating hall:", error);
      setError(error instanceof Error ? error.message : "Failed to update hall");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteHall = (hall: Hall) => {
    setSelectedHall(hall);
    setViewMode('delete');
  };

  const handleConfirmDelete = async (hallId: string) => {
    setIsLoading(true);
    setError("");

    try {
      await deleteHallFromFirestore(hallId);
      setHalls(prev => prev.filter(h => h.id !== hallId));
      setViewMode('list');
      setSelectedHall(null);
      console.log("✅ Hall deleted successfully");
    } catch (error) {
      console.error("❌ Error deleting hall:", error);
      setError(error instanceof Error ? error.message : "Failed to delete hall");
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewHall = (hall: Hall) => {
    setSelectedHall(hall);
    setViewMode('view');
  };

  const handleCloseModal = () => {
    setViewMode('list');
    setSelectedHall(null);
    setError("");
  };

  // Calculate statistics
  const totalCapacity = halls.reduce((sum, hall) => sum + hall.capacity, 0);
  const activeHalls = halls.filter(h => h.status === "active").length;
  const maintenanceHalls = halls.filter(h => h.status === "maintenance").length;

  return (
    <AdminLayout
      title="Hall Management"
      description="Manage exam halls, lecture theatres, and other venues"
      badge="Infrastructure"
    >
      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="font-medium">Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setError("")}
            className="mt-2"
          >
            Dismiss
          </Button>
        </div>
      )}

      {viewMode === 'add' ? (
        <AddHallForm
          onSubmit={handleAddHall}
          onCancel={handleCloseModal}
          isLoading={isLoading}
        />
      ) : viewMode === 'edit' && selectedHall ? (
        <EditHallForm
          hall={selectedHall}
          onSubmit={handleUpdateHall}
          onCancel={handleCloseModal}
          isLoading={isLoading}
        />
      ) : isLoadingHalls ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <TypographyP className="text-muted-foreground">Loading halls...</TypographyP>
          </div>
        </div>
      ) : (
        <>
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Halls</CardTitle>
                <MapPin className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{halls.length}</div>
                <p className="text-xs text-muted-foreground">Registered venues</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Capacity</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalCapacity.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">Students capacity</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Halls</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{activeHalls}</div>
                <p className="text-xs text-muted-foreground">Ready for use</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{maintenanceHalls}</div>
                <p className="text-xs text-muted-foreground">Under maintenance</p>
              </CardContent>
            </Card>
          </div>

          {/* Action Bar */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <TypographyP className="text-muted-foreground">
                Manage your institution&apos;s halls and venues for examinations and lectures.
              </TypographyP>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={loadHalls}
                disabled={isLoadingHalls}
              >
                {isLoadingHalls ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <BarChart3 className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
              <Button onClick={() => setViewMode('add')}>
                <Plus className="mr-2 h-4 w-4" />
                Add New Hall
              </Button>
            </div>
          </div>

          {/* Hall List */}
          <HallList
            halls={halls}
            onEdit={handleEditHall}
            onDelete={handleDeleteHall}
            onView={handleViewHall}
          />
        </>
      )}

      {/* Modals */}
      {viewMode === 'view' && selectedHall && (
        <HallDetailsModal
          hall={selectedHall}
          onClose={handleCloseModal}
          onEdit={handleEditHall}
          onDelete={handleDeleteHall}
        />
      )}

      {viewMode === 'delete' && selectedHall && (
        <DeleteHallDialog
          hall={selectedHall}
          onConfirm={handleConfirmDelete}
          onCancel={handleCloseModal}
          isLoading={isLoading}
        />
      )}
    </AdminLayout>
  );
}
