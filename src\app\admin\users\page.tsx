"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { AddSystemUserForm } from "@/components/admin/add-system-user-form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import {
  Users,
  UserPlus,
  Shield,
  Plus,
  Loader2,
  AlertCircle,
  Eye,
  Settings as SettingsIcon
} from "lucide-react";
import {
  SystemUser,
  SystemUserFormData,
  getUserRoleLabel,
  getUserStatusLabel
} from "@/types/department";
import {
  addSystemUser,
  getAllSystemUsers,
  updateSystemUserStatus
} from "@/lib/firebase/department-service";

type ViewMode = 'list' | 'add-user';

export default function UserManagementPage() {
  const [systemUsers, setSystemUsers] = useState<SystemUser[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<string>("");

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoadingData(true);
      setError("");
      const usersData = await getAllSystemUsers();
      setSystemUsers(usersData);
      console.log(`✅ Loaded ${usersData.length} system users`);
    } catch (error) {
      console.error("❌ Error loading data:", error);
      setError(error instanceof Error ? error.message : "Failed to load data");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleAddUser = async (userData: SystemUserFormData) => {
    setIsLoading(true);
    setError("");

    try {
      const createdBy = "admin"; // TODO: Get actual admin ID
      const newUser = await addSystemUser(userData, createdBy);
      setSystemUsers(prev => [newUser, ...prev]);
      setViewMode('list');
      console.log("✅ System user added successfully:", newUser);
    } catch (error) {
      console.error("❌ Error adding system user:", error);
      setError(error instanceof Error ? error.message : "Failed to add user");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (userId: string, status: 'active' | 'inactive' | 'suspended') => {
    try {
      await updateSystemUserStatus(userId, status);
      setSystemUsers(prev => prev.map(user =>
        user.id === userId ? { ...user, status } : user
      ));
      console.log("✅ User status updated successfully");
    } catch (error) {
      console.error("❌ Error updating user status:", error);
      setError(error instanceof Error ? error.message : "Failed to update user status");
    }
  };

  const handleCloseForm = () => {
    setViewMode('list');
    setError("");
  };

  // Calculate statistics
  const supervisors = systemUsers.filter(u => u.role === 'supervisor');
  const invigilators = systemUsers.filter(u => u.role === 'invigilator');
  const activeUsers = systemUsers.filter(u => u.status === 'active');

  return (
    <AdminLayout
      title="User Management"
      description="Manage supervisors, invigilators, and system users"
      badge="Staff Management"
    >
      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="font-medium">Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setError("")}
            className="mt-2"
          >
            Dismiss
          </Button>
        </div>
      )}

      {viewMode === 'add-user' ? (
        <AddSystemUserForm
          onSubmit={handleAddUser}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : isLoadingData ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <TypographyP className="text-muted-foreground">Loading users...</TypographyP>
          </div>
        </div>
      ) : (
        <>
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <Button
              className="h-20 flex-col space-y-2"
              onClick={() => setViewMode('add-user')}
            >
              <UserPlus className="h-6 w-6" />
              <span>Add System User</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <SettingsIcon className="h-6 w-6" />
              <span>Manage Permissions</span>
            </Button>
          </div>

          {/* User Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemUsers.length}</div>
                <p className="text-xs text-muted-foreground">System users</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Supervisors</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{supervisors.length}</div>
                <p className="text-xs text-muted-foreground">Supervisor accounts</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Invigilators</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{invigilators.length}</div>
                <p className="text-xs text-muted-foreground">Invigilator accounts</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{activeUsers.length}</div>
                <p className="text-xs text-muted-foreground">Currently active</p>
              </CardContent>
            </Card>
          </div>

          {/* System Users List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                System Users ({systemUsers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {systemUsers.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <TypographyP className="text-muted-foreground mb-4">
                    No system users created yet.
                  </TypographyP>
                  <Button onClick={() => setViewMode('add-user')}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add First User
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {systemUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {user.userId}
                          </Badge>
                          <span className="font-medium">{user.name}</span>
                          <Badge
                            variant={user.role === 'supervisor' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {getUserRoleLabel(user.role)}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          {user.email && <span>📧 {user.email}</span>}
                          {user.phoneNumber && <span>📱 {user.phoneNumber}</span>}
                          <span>🗓️ {user.createdAt.toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          className={
                            user.status === 'active' ? 'bg-green-100 text-green-800' :
                            user.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                            'bg-red-100 text-red-800'
                          }
                        >
                          {getUserStatusLabel(user.status)}
                        </Badge>
                        <select
                          value={user.status}
                          onChange={(e) => handleStatusChange(user.id, e.target.value as any)}
                          className="text-xs border rounded px-2 py-1"
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                          <option value="suspended">Suspended</option>
                        </select>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Bar */}
          <div className="flex justify-between items-center mt-6">
            <div>
              <TypographyP className="text-muted-foreground">
                Manage supervisors and invigilators for exam administration.
              </TypographyP>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={loadData}
                disabled={isLoadingData}
              >
                {isLoadingData ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <SettingsIcon className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
            </div>
          </div>
        </>
      )}
    </AdminLayout>
  );
}
