"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TypographyH1, TypographyP } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  Loader2,
  GraduationCap,
  AlertCircle,
  ArrowRight,
  Bell,
  RefreshCw
} from "lucide-react";

export default function StudentSetupPage() {
  const [setupData, setSetupData] = useState({
    email: "",
    password: "",
    confirmPassword: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [step, setStep] = useState(1);
  const [userEmail, setUserEmail] = useState("");
  const router = useRouter();

  // Get student info from temporary session
  const [studentInfo, setStudentInfo] = useState({
    matricNumber: "",
    name: "",
    departmentName: "",
    facultyName: "",
    levelName: ""
  });

  useEffect(() => {
    // Get temporary setup data
    const tempData = localStorage.getItem('temp_student_setup');
    if (tempData) {
      const data = JSON.parse(tempData);
      setStudentInfo({
        matricNumber: data.matricNumber || "Unknown",
        name: data.name || "Student",
        departmentName: data.departmentName || "Unknown",
        facultyName: data.facultyName || "Unknown",
        levelName: data.levelName || "Unknown"
      });
    } else {
      // Redirect back to sign-in if no temp data
      router.push('/sign-in');
    }
  }, [router]);

  const handleSetup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validation
    if (setupData.password !== setupData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (setupData.password.length < 6) {
      setError("Password must be at least 6 characters long");
      setIsLoading(false);
      return;
    }

    try {
      // Create Firebase user account with email and password
      const { createUserWithEmailAndPassword, sendEmailVerification } = await import('firebase/auth');
      const { auth } = await import('@/lib/firebase/firebase');
      const { collection, query, where, getDocs, updateDoc, doc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      console.log("Creating Firebase user account...");
      const userCredential = await createUserWithEmailAndPassword(auth, setupData.email, setupData.password);

      console.log("Sending email verification...");
      await sendEmailVerification(userCredential.user);

      console.log("Updating student record in Firestore...");
      // Find and update the student record
      const studentsRef = collection(db, 'students');
      const q = query(studentsRef, where('matricNumber', '==', studentInfo.matricNumber));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const studentDoc = querySnapshot.docs[0];
        await updateDoc(doc(db, 'students', studentDoc.id), {
          email: setupData.email,
          hasSetupAccount: true,
          hasChangedPassword: true,
          emailVerified: false, // Will be true after verification
          updatedAt: new Date()
        });

        console.log("✅ Student account setup completed successfully");

        // Store email for verification step
        setUserEmail(setupData.email);

        // Clear temporary setup data
        localStorage.removeItem('temp_student_setup');

        // Move to email verification step
        setStep(2);

      } else {
        throw new Error('Student record not found');
      }

    } catch (error: any) {
      console.error('Setup error:', error);
      if (error.code === 'auth/email-already-in-use') {
        setError('This email is already registered. Please use a different email or contact admin.');
      } else if (error.code === 'auth/weak-password') {
        setError('Password is too weak. Please choose a stronger password.');
      } else if (error.code === 'auth/invalid-email') {
        setError('Invalid email format. Please enter a valid email address.');
      } else {
        setError(error.message || "Setup failed. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      const { sendEmailVerification } = await import('firebase/auth');
      const { auth } = await import('@/lib/firebase/firebase');

      if (auth.currentUser) {
        await sendEmailVerification(auth.currentUser);
        alert('Verification email sent! Please check your inbox.');
      }
    } catch (error) {
      console.error('Error resending verification:', error);
      alert('Failed to resend verification email. Please try again.');
    }
  };

  const handleCheckVerification = async () => {
    try {
      const { auth } = await import('@/lib/firebase/firebase');
      const { collection, query, where, getDocs, updateDoc, doc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      if (auth.currentUser) {
        await auth.currentUser.reload();

        if (auth.currentUser.emailVerified) {
          // Update student record
          const studentsRef = collection(db, 'students');
          const q = query(studentsRef, where('matricNumber', '==', studentInfo.matricNumber));
          const querySnapshot = await getDocs(q);

          if (!querySnapshot.empty) {
            const studentDoc = querySnapshot.docs[0];
            await updateDoc(doc(db, 'students', studentDoc.id), {
              emailVerified: true,
              updatedAt: new Date()
            });
          }

          // Set proper session
          localStorage.setItem('student_session', JSON.stringify({
            matricNumber: studentInfo.matricNumber,
            email: userEmail,
            loginTime: new Date().toISOString()
          }));

          // Move to final success step
          setStep(3);

          // Auto-redirect after 3 seconds
          setTimeout(() => {
            router.push('/dashboard');
          }, 3000);
        } else {
          alert('Email not verified yet. Please check your inbox and click the verification link.');
        }
      }
    } catch (error) {
      console.error('Error checking verification:', error);
      alert('Failed to check verification status. Please try again.');
    }
  };

  if (step === 2) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <div className="bg-green-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <Bell className="h-10 w-10 text-green-700" />
            </div>
            <TypographyH1 className="text-2xl font-bold text-gray-900 mb-4">
              Verify Your Email
            </TypographyH1>
            <TypographyP className="text-gray-600 mb-6">
              We've sent a verification link to <strong>{userEmail}</strong>. Please check your inbox and click the link to verify your account.
            </TypographyP>

            <div className="space-y-4">
              <Button
                onClick={handleCheckVerification}
                className="w-full bg-green-700 hover:bg-green-800"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                I've Verified My Email
              </Button>

              <Button
                variant="outline"
                onClick={handleResendVerification}
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend Verification Email
              </Button>
            </div>

            <TypographyP className="text-xs text-gray-500 mt-6">
              Can't find the email? Check your spam folder or try resending.
            </TypographyP>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <div className="bg-green-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <TypographyH1 className="text-2xl font-bold text-gray-900 mb-4">
              Account Verified!
            </TypographyH1>
            <TypographyP className="text-gray-600 mb-6">
              Your email has been verified successfully. You can now access your student portal.
            </TypographyP>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Loader2 className="h-4 w-4 animate-spin" />
              Redirecting to dashboard...
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-green-700 p-2 rounded-lg">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hall Automata</h1>
                <p className="text-sm text-gray-600">Account Setup</p>
              </div>
            </div>
            <Badge variant="outline" className="font-mono">
              🎓 MAPOLY
            </Badge>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-md mx-auto px-4 py-8 sm:py-12">
        <div className="text-center mb-8">
          <div className="bg-green-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <User className="h-10 w-10 text-green-700" />
          </div>
          <TypographyH1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Complete Your Setup
          </TypographyH1>
          <TypographyP className="text-gray-600">
            Set up your email and password to secure your account
          </TypographyP>
        </div>

        {/* Student Info Card */}
        <Card className="mb-6 bg-white/80 backdrop-blur-sm border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg">Your Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Name:</span>
              <span className="text-sm font-medium">{studentInfo.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Matric Number:</span>
              <span className="text-sm font-mono font-medium">{studentInfo.matricNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Department:</span>
              <span className="text-sm font-medium">{studentInfo.departmentName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Faculty:</span>
              <span className="text-sm font-medium">{studentInfo.facultyName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Level:</span>
              <span className="text-sm font-medium">{studentInfo.levelName}</span>
            </div>
          </CardContent>
        </Card>

        {/* Setup Form */}
        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-center">Account Security Setup</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSetup} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <strong>Required:</strong> You must set up an email and new password to continue using the system.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={setupData.email}
                    onChange={(e) => setSetupData(prev => ({ ...prev, email: e.target.value }))}
                    className="pl-10"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500">
                  Use your preferred email address for account recovery
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">New Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Create a strong password"
                    value={setupData.password}
                    onChange={(e) => setSetupData(prev => ({ ...prev, password: e.target.value }))}
                    className="pl-10 pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={setupData.confirmPassword}
                    onChange={(e) => setSetupData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className="pl-10 pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-green-700 hover:bg-green-800"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting up account...
                  </>
                ) : (
                  <>
                    Complete Setup
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <TypographyP className="text-xs text-gray-500">
                After setup, you can sign in with either your email or matric number
              </TypographyP>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
