"use client";

import { useState, useRef } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Upload, 
  Download, 
  FileSpreadsheet, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  Eye,
  X
} from "lucide-react";

interface ExcelExamData {
  courseCode: string;
  courseTitle: string;
  startTime: string;
  endTime: string;
  isElective: boolean;
  errors?: string[];
}

interface ExcelImportFormProps {
  onImport: (exams: ExcelExamData[]) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function ExcelImportForm({ onImport, onCancel, isLoading = false }: ExcelImportFormProps) {
  const [file, setFile] = useState<File | null>(null);
  const [exams, setExams] = useState<ExcelExamData[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const downloadTemplate = () => {
    const template = `Course Code,Course Title,Start Time,End Time,Is Elective
CSC101,Introduction to Computer Science,09:00,11:00,FALSE
MTH201,Calculus II,14:00,16:00,FALSE
PHY301,Modern Physics,10:00,12:00,TRUE
ENG102,Technical Writing,08:00,10:00,FALSE`;

    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'exam_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setExams([]);
      setErrors([]);
      setShowPreview(false);
    }
  };

  const parseCSV = (text: string): ExcelExamData[] => {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      throw new Error('File must contain at least a header row and one data row');
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const requiredHeaders = ['course code', 'course title', 'start time', 'end time'];
    
    // Check if all required headers are present
    const missingHeaders = requiredHeaders.filter(header => 
      !headers.some(h => h.includes(header.replace(' ', '')))
    );
    
    if (missingHeaders.length > 0) {
      throw new Error(`Missing required columns: ${missingHeaders.join(', ')}`);
    }

    const exams: ExcelExamData[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const examErrors: string[] = [];
      
      if (values.length < 4) {
        examErrors.push('Insufficient columns');
        continue;
      }

      const courseCode = values[0]?.toUpperCase() || '';
      const courseTitle = values[1] || '';
      const startTime = values[2] || '';
      const endTime = values[3] || '';
      const isElective = values[4]?.toLowerCase() === 'true' || values[4]?.toLowerCase() === 'yes';

      // Validate required fields
      if (!courseCode) examErrors.push('Course code is required');
      if (!courseTitle) examErrors.push('Course title is required');
      if (!startTime) examErrors.push('Start time is required');
      if (!endTime) examErrors.push('End time is required');

      // Validate time format (HH:MM)
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (startTime && !timeRegex.test(startTime)) {
        examErrors.push('Start time must be in HH:MM format');
      }
      if (endTime && !timeRegex.test(endTime)) {
        examErrors.push('End time must be in HH:MM format');
      }

      // Validate time range
      if (startTime && endTime && timeRegex.test(startTime) && timeRegex.test(endTime)) {
        const start = new Date(`2000-01-01 ${startTime}`);
        const end = new Date(`2000-01-01 ${endTime}`);
        if (end <= start) {
          examErrors.push('End time must be after start time');
        }
      }

      exams.push({
        courseCode,
        courseTitle,
        startTime,
        endTime,
        isElective,
        errors: examErrors.length > 0 ? examErrors : undefined
      });
    }

    return exams;
  };

  const processFile = async () => {
    if (!file) return;

    setIsProcessing(true);
    setUploadProgress(0);
    setErrors([]);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const text = await file.text();
      
      // Complete progress
      setUploadProgress(100);
      clearInterval(progressInterval);

      const parsedExams = parseCSV(text);
      setExams(parsedExams);

      // Check for errors
      const examErrors = parsedExams.filter(exam => exam.errors && exam.errors.length > 0);
      if (examErrors.length > 0) {
        const allErrors = examErrors.flatMap((exam, index) => 
          exam.errors?.map(error => `Row ${index + 2}: ${error}`) || []
        );
        setErrors(allErrors);
      }

      // Check for duplicate course codes
      const courseCodes = parsedExams.map(e => e.courseCode).filter(Boolean);
      const duplicates = courseCodes.filter((code, index) => courseCodes.indexOf(code) !== index);
      if (duplicates.length > 0) {
        setErrors(prev => [...prev, `Duplicate course codes found: ${[...new Set(duplicates)].join(', ')}`]);
      }

      setShowPreview(true);
    } catch (error) {
      setErrors([error instanceof Error ? error.message : 'Failed to process file']);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImport = async () => {
    const validExams = exams.filter(exam => !exam.errors || exam.errors.length === 0);
    if (validExams.length === 0) {
      setErrors(['No valid exams to import']);
      return;
    }

    await onImport(validExams);
  };

  const clearFile = () => {
    setFile(null);
    setExams([]);
    setErrors([]);
    setShowPreview(false);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validExams = exams.filter(exam => !exam.errors || exam.errors.length === 0);
  const invalidExams = exams.filter(exam => exam.errors && exam.errors.length > 0);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSpreadsheet className="h-5 w-5" />
          Import Exams from Excel/CSV
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Upload a CSV or Excel file to create multiple exams at once
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Template Download */}
        <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div>
            <h4 className="font-medium text-blue-900">Need a template?</h4>
            <p className="text-sm text-blue-700">
              Download our CSV template with the correct format and sample data
            </p>
          </div>
          <Button variant="outline" onClick={downloadTemplate} size="sm">
            <Download className="h-4 w-4 mr-2" />
            Download Template
          </Button>
        </div>

        {/* File Upload */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Label htmlFor="file-upload">Select CSV/Excel File</Label>
              <Input
                ref={fileInputRef}
                id="file-upload"
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileSelect}
                className="mt-2"
              />
            </div>
            {file && (
              <Button variant="ghost" size="sm" onClick={clearFile}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {file && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  <span className="text-sm font-medium">{file.name}</span>
                  <Badge variant="outline">{(file.size / 1024).toFixed(1)} KB</Badge>
                </div>
                <Button onClick={processFile} disabled={isProcessing} size="sm">
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Process File
                    </>
                  )}
                </Button>
              </div>

              {isProcessing && (
                <div className="mt-4">
                  <Progress value={uploadProgress} className="w-full" />
                  <p className="text-xs text-muted-foreground mt-1">
                    Processing file... {uploadProgress}%
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Errors */}
        {errors.length > 0 && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800 mb-2">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Import Errors ({errors.length})</span>
            </div>
            <ul className="text-sm text-red-700 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Preview */}
        {showPreview && exams.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Import Preview</h4>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span>{validExams.length} valid</span>
                </div>
                {invalidExams.length > 0 && (
                  <div className="flex items-center gap-1 text-red-600">
                    <AlertCircle className="h-4 w-4" />
                    <span>{invalidExams.length} invalid</span>
                  </div>
                )}
              </div>
            </div>

            <div className="border rounded-lg max-h-64 overflow-y-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="text-left p-3">Course Code</th>
                    <th className="text-left p-3">Course Title</th>
                    <th className="text-left p-3">Time</th>
                    <th className="text-left p-3">Type</th>
                    <th className="text-left p-3">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {exams.map((exam, index) => (
                    <tr key={index} className={`border-b ${exam.errors ? 'bg-red-50' : 'bg-white'}`}>
                      <td className="p-3 font-mono">{exam.courseCode}</td>
                      <td className="p-3">{exam.courseTitle}</td>
                      <td className="p-3">{exam.startTime} - {exam.endTime}</td>
                      <td className="p-3">
                        {exam.isElective ? (
                          <Badge variant="secondary">Elective</Badge>
                        ) : (
                          <Badge variant="outline">Core</Badge>
                        )}
                      </td>
                      <td className="p-3">
                        {exam.errors && exam.errors.length > 0 ? (
                          <div className="flex items-center gap-1 text-red-600">
                            <AlertCircle className="h-3 w-3" />
                            <span className="text-xs">
                              {exam.errors.length} error{exam.errors.length !== 1 ? 's' : ''}
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-3 w-3" />
                            <span className="text-xs">Valid</span>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Actions */}
        {showPreview && validExams.length > 0 && (
          <div className="flex gap-4 pt-4">
            <Button onClick={handleImport} disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing {validExams.length} Exams...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Import {validExams.length} Valid Exams
                </>
              )}
            </Button>
            <Button variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
