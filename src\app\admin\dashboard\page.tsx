"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TypographyP, TypographyH3 } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Calendar,
  Building,
  Shield,
  Settings,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Activity,
  Loader2,
  RefreshCw,
  Eye,
  Plus,
  ArrowRight,
  BookOpen,
  GraduationCap
} from "lucide-react";
import Link from "next/link";
import { getAllStudents, getAllFaculties, getAllDepartments } from "@/lib/firebase/department-service";
import { getAllHalls } from "@/lib/firebase/hall-service";
import { getAllExams } from "@/lib/firebase/exam-service";
import { getRecentActivities, ActivityItem } from "@/lib/firebase/activity-service";

interface DashboardStats {
  totalStudents: number;
  totalExams: number;
  scheduledExams: number;
  completedExams: number;
  totalHalls: number;
  availableHalls: number;
  totalFaculties: number;
  totalDepartments: number;
  systemUsers: number;
  recentActivity: ActivityItem[];
}

interface ActivityItem {
  id: string;
  type: 'exam_created' | 'hall_allocated' | 'student_added' | 'user_created' | 'course_added' | 'department_created';
  message: string;
  timestamp: Date;
  user: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    totalExams: 0,
    scheduledExams: 0,
    completedExams: 0,
    totalHalls: 0,
    availableHalls: 0,
    totalFaculties: 0,
    totalDepartments: 0,
    systemUsers: 1,
    recentActivity: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load dashboard data
  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      console.log('📊 Loading dashboard data from Firestore...');

      // Load real data from Firestore in parallel
      const [
        studentsData,
        examsData,
        hallsData,
        facultiesData,
        departmentsData,
        recentActivities
      ] = await Promise.all([
        getAllStudents(),
        getAllExams(),
        getAllHalls(),
        getAllFaculties(),
        getAllDepartments(),
        getRecentActivities(5)
      ]);

      console.log('✅ Dashboard data loaded:', {
        students: studentsData.length,
        exams: examsData.length,
        halls: hallsData.length,
        faculties: facultiesData.length,
        departments: departmentsData.length
      });

      // Calculate real statistics
      const activeHalls = hallsData.filter(hall => hall.status === 'active');
      const scheduledExams = examsData.filter(exam => exam.status === 'scheduled');
      const completedExams = examsData.filter(exam => exam.isAllocated);
      const activeStudents = studentsData.filter(student => student.status === 'active');
      const studentsWithSetup = studentsData.filter(student => student.hasSetupAccount);

      setStats({
        totalStudents: studentsData.length,
        totalExams: examsData.length,
        scheduledExams: scheduledExams.length,
        completedExams: completedExams.length,
        totalHalls: hallsData.length,
        availableHalls: activeHalls.length,
        totalFaculties: facultiesData.length,
        totalDepartments: departmentsData.length,
        systemUsers: 1, // Admin user count - can be expanded
        recentActivity: recentActivities
      });

      setLastUpdated(new Date());
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      // Set default values on error
      setStats({
        totalStudents: 0,
        totalExams: 0,
        scheduledExams: 0,
        completedExams: 0,
        totalHalls: 0,
        availableHalls: 0,
        totalFaculties: 0,
        totalDepartments: 0,
        systemUsers: 1,
        recentActivity: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'exam_created': return <Calendar className="h-4 w-4 text-blue-500" />;
      case 'hall_allocated': return <Building className="h-4 w-4 text-green-500" />;
      case 'student_added': return <Users className="h-4 w-4 text-purple-500" />;
      case 'user_created': return <Shield className="h-4 w-4 text-orange-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <AdminLayout
      title="Dashboard Overview"
      description="Hall Automata System Administration"
      badge="🎓 MAPOLY"
    >
      {/* Header Actions */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <TypographyP className="text-muted-foreground">
            Welcome back! Here's what's happening with your examination system.
          </TypographyP>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadDashboardData}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Badge variant="outline" className="text-xs">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.totalStudents.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Across all departments</p>
            <div className="mt-2">
              <Progress value={85} className="h-1" />
              <p className="text-xs text-muted-foreground mt-1">85% have setup accounts</p>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled Exams</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.scheduledExams}
            </div>
            <p className="text-xs text-muted-foreground">Out of {stats.totalExams} total</p>
            <div className="mt-2">
              <Progress value={(stats.scheduledExams / stats.totalExams) * 100} className="h-1" />
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((stats.scheduledExams / stats.totalExams) * 100)}% scheduled
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Halls</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.availableHalls}
            </div>
            <p className="text-xs text-muted-foreground">Out of {stats.totalHalls} total</p>
            <div className="mt-2">
              <Progress value={(stats.availableHalls / stats.totalHalls) * 100} className="h-1" />
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((stats.availableHalls / stats.totalHalls) * 100)}% available
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : '98%'}
            </div>
            <p className="text-xs text-muted-foreground">System uptime</p>
            <div className="mt-2 flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <p className="text-xs text-green-600">All systems operational</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-6 lg:mb-8">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center gap-3">
                    <div className="h-8 w-8 bg-muted rounded-full animate-pulse" />
                    <div className="flex-1 space-y-1">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
                    </div>
                  </div>
                ))}
              </div>
            ) : stats.recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <TypographyP className="text-muted-foreground">
                  No recent activity
                </TypographyP>
              </div>
            ) : (
              <div className="space-y-3">
                {stats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="mt-0.5">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.message}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {activity.user}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatTimeAgo(activity.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Link href="/admin/exams">
                <Button variant="outline" className="h-20 w-full flex-col space-y-2 hover:bg-blue-50">
                  <Calendar className="h-6 w-6 text-blue-600" />
                  <span className="text-sm">Schedule Exam</span>
                </Button>
              </Link>

              <Link href="/admin/hall-allocation">
                <Button variant="outline" className="h-20 w-full flex-col space-y-2 hover:bg-green-50">
                  <Building className="h-6 w-6 text-green-600" />
                  <span className="text-sm">Allocate Halls</span>
                </Button>
              </Link>

              <Link href="/admin/departments">
                <Button variant="outline" className="h-20 w-full flex-col space-y-2 hover:bg-purple-50">
                  <Users className="h-6 w-6 text-purple-600" />
                  <span className="text-sm">Add Students</span>
                </Button>
              </Link>

              <Link href="/admin/halls">
                <Button variant="outline" className="h-20 w-full flex-col space-y-2 hover:bg-orange-50">
                  <Shield className="h-6 w-6 text-orange-600" />
                  <span className="text-sm">Manage Halls</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
        <Link href="/admin/users">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Management
                </div>
                <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm text-muted-foreground">
                Create and manage supervisors, invigilators, and student accounts
              </TypographyP>
              <div className="mt-3 flex items-center gap-2">
                <Badge variant="secondary">{stats.systemUsers} users</Badge>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/departments">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Department Access
                </div>
                <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm text-muted-foreground">
                Grant department permissions and manage registration portals
              </TypographyP>
              <div className="mt-3 flex items-center gap-2">
                <Badge variant="secondary">{stats.totalFaculties} faculties</Badge>
                <Badge variant="secondary">{stats.totalDepartments} departments</Badge>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/exams">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Exam Scheduling
                </div>
                <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm text-muted-foreground">
                Schedule exams and manage hall allocations
              </TypographyP>
              <div className="mt-3 flex items-center gap-2">
                <Badge variant="secondary">{stats.totalExams} exams</Badge>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  {stats.scheduledExams} scheduled
                </Badge>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/halls">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Hall Management
                </div>
                <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm text-muted-foreground">
                Manage examination halls and capacity settings
              </TypographyP>
              <div className="mt-3 flex items-center gap-2">
                <Badge variant="secondary">{stats.totalHalls} halls</Badge>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  {stats.availableHalls} available
                </Badge>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/reports">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Reports & Analytics
                </div>
                <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm text-muted-foreground">
                View attendance reports and system analytics
              </TypographyP>
              <div className="mt-3">
                <Badge variant="outline" className="text-blue-600 border-blue-600">
                  Coming Soon
                </Badge>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/settings">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  System Settings
                </div>
                <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm text-muted-foreground">
                Configure system settings and default passwords
              </TypographyP>
              <div className="mt-3">
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Active
                </Badge>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </AdminLayout>
  );
}
