# Hall Automata - Database Population Script

This script populates your Firestore database with comprehensive test data for the Hall Automata system.

## 🚀 Quick Start

### Prerequisites
- Node.js installed on your system
- Firebase project set up
- Firestore database enabled

### Installation
1. Install Firebase SDK:
```bash
npm install firebase
```

2. Update Firebase Configuration:
   - Open `populate-test-data.js`
   - Replace the `firebaseConfig` object with your actual Firebase configuration
   - Get your config from: Firebase Console > Project Settings > General > Your apps

3. Run the Script:
```bash
node scripts/populate-test-data.js
```

## 📊 Generated Test Data

### Faculties (5)
- Faculty of Engineering
- Faculty of Science  
- Faculty of Business Administration
- Faculty of Environmental Studies
- Faculty of Applied Arts

### Departments (15)
**Engineering:**
- Computer Science (CSC)
- Electrical Engineering (EEE)
- Mechanical Engineering (MEE)
- Civil Engineering (CVE)

**Science:**
- Mathematics (MTH)
- Physics (PHY)
- Chemistry (CHM)
- Biology (BIO)

**Business:**
- Accounting (ACC)
- Business Administration (BUA)
- Marketing (MKT)

**Environmental:**
- Estate Management (ESM)
- Urban Planning (URP)

**Arts:**
- Mass Communication (MCM)
- Fine Arts (FNA)

### Students (2000+)
- **Distribution:** 40-70 full-time, 15-35 part-time per department/level
- **Levels:** ND1, ND2, HND1, HND2
- **Data:** Realistic names, emails, phone numbers, matric numbers
- **Status:** 70% have setup accounts, 60% changed passwords

### Courses (400+)
- **Per Department:** 8 courses across all levels
- **Realistic Codes:** CSC101, EEE201, ACC301, etc.
- **Credit Units:** 2-6 units per course
- **Semesters:** First and Second semester courses

### System Users (14)
- **Roles:** Supervisors and Invigilators
- **Distribution:** Across all departments
- **Realistic Data:** Professional names and email addresses

### Examination Halls (10)
- **Variety:** Different capacities (80-200 students)
- **Locations:** Across campus buildings
- **Realistic Names:** Main Hall A, ICT Hall B, Science Hall, etc.

### Exam Timetable (100+)
- **Period:** October 2025 (5-day exam period)
- **Time Slots:** Morning (9:00-12:00) and Afternoon (14:00-17:00)
- **Coverage:** ~40% of courses have scheduled exams
- **Session:** 2024/2025 Academic Session

## 🗃️ Database Collections

### Core Collections
- `faculties` - Faculty information
- `departments` - Department details with faculty relationships
- `students` - Complete student records
- `courses` - Course information by department and level
- `system_users` - Staff (supervisors/invigilators)
- `halls` - Examination hall details
- `exam_timetable` - Scheduled examinations

### Activity Tracking
- `system_activities` - System activity logs (auto-generated)

## 🔑 Default Login Credentials

### Admin Account
- **ID:** `auto-admin-id-9aa745-mapoly-exam-hall-automata`
- **Password:** `706_113`

### Student Accounts
- **Login:** Use matric number (e.g., `CSC/2024/001`) or email
- **Default Password:** `student123` (for first-time login)
- **Email Format:** `<EMAIL>`

### Staff Accounts
- **Login:** Use staff ID (e.g., `SUPERVISOR-001`)
- **Default Password:** `staff123` (for first-time login)
- **Email Format:** `<EMAIL>`

## 📋 Data Schema Examples

### Student Record
```javascript
{
  matricNumber: "CSC/2024/001",
  name: "John Smith",
  departmentName: "Computer Science",
  facultyName: "Faculty of Engineering", 
  levelName: "ND1",
  email: "<EMAIL>",
  phoneNumber: "+*************",
  gender: "male",
  studyMode: "full_time",
  status: "active",
  hasSetupAccount: true,
  hasChangedPassword: false,
  createdAt: Timestamp,
  updatedAt: Timestamp,
  createdBy: "admin"
}
```

### Course Record
```javascript
{
  courseCode: "CSC201",
  courseTitle: "Data Structures and Algorithms",
  departmentName: "Computer Science",
  facultyName: "Faculty of Engineering",
  levelName: "ND2", 
  creditUnits: 4,
  semester: "First",
  isElective: false,
  status: "active",
  createdAt: Timestamp,
  updatedAt: Timestamp,
  createdBy: "admin"
}
```

### Exam Record
```javascript
{
  courseCode: "CSC201",
  courseTitle: "Data Structures and Algorithms",
  departmentName: "Computer Science",
  facultyName: "Faculty of Engineering",
  levelName: "ND2",
  examDate: Timestamp, // October 2025
  startTime: "09:00",
  endTime: "12:00", 
  duration: 180,
  session: "2024/2025",
  semester: "First",
  instructions: "Bring your student ID and writing materials...",
  status: "scheduled",
  createdAt: Timestamp,
  updatedAt: Timestamp,
  createdBy: "admin"
}
```

## ⚠️ Important Notes

### Before Running
- **Backup existing data** if you have any important records
- **Test on a development database** first
- **Ensure proper Firebase permissions** for write operations

### After Running
- **Verify data integrity** by checking a few records in Firebase Console
- **Test login functionality** with the provided credentials
- **Check system activities** to ensure logging is working

### Performance
- Script uses **batch operations** for optimal performance
- **Large datasets** may take 2-3 minutes to complete
- **Progress indicators** show real-time status

## 🔧 Troubleshooting

### Common Issues
1. **Firebase Config Error:** Ensure your configuration is correct
2. **Permission Denied:** Check Firestore security rules
3. **Network Issues:** Ensure stable internet connection
4. **Memory Issues:** For very large datasets, consider running in smaller batches

### Support
If you encounter issues:
1. Check the console output for detailed error messages
2. Verify your Firebase project settings
3. Ensure Firestore is properly initialized
4. Check your internet connection

## 🎯 Next Steps

After running the script:
1. **Login to admin panel** with the provided credentials
2. **Test student registration** and login flows
3. **Verify exam scheduling** and hall allocation features
4. **Check system logs** for activity tracking
5. **Test all user roles** (admin, students, staff)

Your Hall Automata system is now ready for comprehensive testing and demonstration! 🚀
