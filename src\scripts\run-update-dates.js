// Simple Node.js script to update exam dates to October 2025
// Run this with: node src/scripts/run-update-dates.js

const { execSync } = require('child_process');

console.log('📅 Hall Automata Exam Date Update Script');
console.log('=======================================');
console.log('');
console.log('This script will update all exam dates to October 2025:');
console.log('');
console.log('📅 New Exam Schedule:');
console.log('  Monday:    October 6, 2025');
console.log('  Tuesday:   October 7, 2025');
console.log('  Wednesday: October 8, 2025');
console.log('  Thursday:  October 9, 2025');
console.log('  Friday:    October 10, 2025');
console.log('');
console.log('🔄 This will update ALL existing exams in your database');
console.log('⚠️  Make sure you have generated exams first (npm run generate-exams)');
console.log('');

// Ask for confirmation
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Do you want to update all exam dates to October 2025? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
    console.log('');
    console.log('🚀 Updating exam dates...');
    console.log('This may take a few minutes...');
    console.log('');
    
    try {
      // Run the TypeScript date update script
      execSync('npx tsx src/scripts/update-exam-dates.ts', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('');
      console.log('🎉 Exam dates updated successfully!');
      console.log('');
      console.log('✅ All exams now scheduled for October 2025');
      console.log('');
      console.log('🧪 Ready for Testing:');
      console.log('1. 📋 Go to /admin/hall-allocation');
      console.log('2. 📅 Select "2024/2025" session');
      console.log('3. 📚 Choose "First Semester"');
      console.log('4. 🔍 Verify exams show October 2025 dates');
      console.log('5. 🎯 Select exams for hall allocation');
      console.log('6. 🚀 Test the complete workflow');
      console.log('');
      console.log('🎯 Perfect timing for:');
      console.log('- Final year project demonstration');
      console.log('- Hall allocation testing');
      console.log('- System evaluation');
      console.log('- Professional presentation');
      console.log('');
      
    } catch (error) {
      console.error('');
      console.error('❌ Error during date update:', error.message);
      console.error('');
      console.error('Troubleshooting:');
      console.error('1. Make sure you have generated exams first: npm run generate-exams');
      console.error('2. Check your Firebase configuration');
      console.error('3. Ensure you have proper Firestore permissions');
      console.error('4. Verify exams collection exists in Firestore');
      console.error('');
      process.exit(1);
    }
  } else {
    console.log('');
    console.log('❌ Date update cancelled.');
    console.log('');
    console.log('💡 When you\'re ready:');
    console.log('1. Generate exams first: npm run generate-exams');
    console.log('2. Then update dates: npm run update-dates');
    console.log('');
  }
  
  rl.close();
});
