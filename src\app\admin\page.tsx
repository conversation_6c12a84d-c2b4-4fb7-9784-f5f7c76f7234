import { AdminSignInForm } from "@/components/auth/admin-sign-in-form";
import { TypographyH1, TypographyP } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";

export default function AdminSignInPage() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Badge variant="outline" className="font-mono text-foreground border-border mb-4">
            🔐 Administrative Portal
          </Badge>
          <TypographyH1 className="text-3xl font-bold mb-2">Hall Automata</TypographyH1>
          <TypographyP className="text-muted-foreground">
            Smart Exam Management System
          </TypographyP>
        </div>

        {/* Admin Sign In Form */}
        <AdminSignInForm />

        {/* Footer */}
        <div className="text-center mt-8">
          <TypographyP className="text-sm text-muted-foreground">
            © 2024 Hall Automata • MAPOLY Innovation
          </TypographyP>
          <TypographyP className="text-xs text-muted-foreground mt-1 font-mono">
            Secure Administrative Access Portal
          </TypographyP>
        </div>
      </div>
    </div>
  );
}
