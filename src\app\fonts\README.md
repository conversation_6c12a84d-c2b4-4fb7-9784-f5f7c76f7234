# Fonts Directory

## Winky Rough Font Setup

To use the Winky Rough font in your Hall Automata project, you need to add the font files to this directory.

### Required Files:
- `WinkyRough-Regular.woff2`
- `WinkyRough-Bold.woff2`

### Where to Get Winky Rough Font:
1. **Google Fonts**: Search for "Winky Rough" or similar creative fonts
2. **Font Squirrel**: Free fonts for commercial use
3. **DaFont**: Large collection of creative fonts
4. **Adobe Fonts**: If you have Adobe Creative Cloud subscription

### Alternative Creative Fonts:
If you can't find Winky Rough, here are similar creative fonts you can use:

1. **Kalam** (Google Fonts) - Handwritten style
2. **Caveat** (Google Fonts) - Casual handwriting
3. **Architects Daughter** (Google Fonts) - Architectural handwriting
4. **Indie Flower** (Google Fonts) - Friendly handwriting
5. **Permanent Marker** (Google Fonts) - Marker pen style

### How to Add the Font:
1. Download the font files in WOFF2 format
2. Place them in this directory (`src/app/fonts/`)
3. Make sure the filenames match:
   - `WinkyRough-Regular.woff2`
   - `WinkyRough-Bold.woff2`
4. The font is already configured in `layout.tsx` and will work automatically

### Using Google Fonts Alternative:
If you prefer to use a Google Font instead, update `layout.tsx`:

```typescript
// Replace the localFont import with Google Font
import { Kalam } from "next/font/google";

const kalam = Kalam({
  subsets: ["latin"],
  variable: "--font-winky-rough",
  display: "swap",
  weight: ["300", "400", "700"],
});

// Use kalam.variable instead of winkyRough.variable in the body className
```

### Current Status:
- ✅ Font configuration is ready
- ⏳ Font files need to be added
- ✅ CSS classes are configured
- ✅ Typography components are ready

Once you add the font files, the creative typography will work perfectly in your Hall Automata project!
