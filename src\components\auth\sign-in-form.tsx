"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TypographyH2, TypographyP, TypographyMuted } from "@/components/ui/typography";
import { GraduationCap, Mail, Lock, Eye, EyeOff, Loader2, User } from "lucide-react";
import { signInWithIdOrEmail, getStudentDataForSetup, getStaffDataForSetup } from "@/lib/firebase/custom-auth";

interface SignInFormProps {
  onClose?: () => void;
  isModal?: boolean;
}

export function SignInForm({ onClose, isModal = false }: SignInFormProps) {
  const [identifier, setIdentifier] = useState(""); // Can be email or ID
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isLoading) return; // Prevent double submission

    setIsLoading(true);
    setError("");

    try {
      console.log("🚀 Starting login process for:", identifier);
      console.log("🔍 Form submission prevented, handling with JavaScript");

      const result = await signInWithIdOrEmail(identifier, password);
      console.log('✅ Authentication result:', result);

      const { user, userData } = result;
      console.log('✅ User signed in successfully:', userData);

      // Handle different user types and first-time login
      if (userData.isFirstLogin) {
        if (userData.role === 'student') {
          // Store temporary session data for setup
          console.log("goint to set up")
          localStorage.setItem('temp_student_setup', JSON.stringify({
            matricNumber: userData.id,
            name: userData.displayName,
            department: userData.department
          }));
          window.location.href = '/setup/student';
          return;
        } else if (userData.role === 'supervisor' || userData.role === 'invigilator') {
          window.location.href = '/setup/staff';
          return;
        }
      }

      console.log(userData.role)

      // Store session data and redirect based on user role
      switch (userData.role) {
        case 'admin':
          localStorage.setItem('admin_session', JSON.stringify({
            userId: userData.id,
            email: userData.email,
            role: userData.role,
            loginTime: new Date().toISOString()
          }));
          window.location.href = '/admin/dashboard';
          break;
        case 'supervisor':
        case 'invigilator':
          localStorage.setItem('staff_session', JSON.stringify({
            userId: userData.id,
            email: userData.email,
            role: userData.role,
            loginTime: new Date().toISOString()
          }));
          window.location.href = '/staff/dashboard';
          break;
        case 'student':
          console.log("Knew its a ", userData)
          localStorage.setItem('student_session', JSON.stringify({
            matricNumber: userData.id,
            email: userData.email,
            name: userData.displayName,
            loginTime: new Date().toISOString()
          }));
          window.location.href = '/dashboard';
          break;
        case 'department':
          localStorage.setItem('department_session', JSON.stringify({
            userId: userData.id,
            email: userData.email,
            role: userData.role,
            loginTime: new Date().toISOString()
          }));
          window.location.href = '/department/dashboard';
          break;
        default:
          window.location.href = '/dashboard';
      }

      // Close modal if it's a modal
      if (isModal && onClose) {
        onClose();
      }

      console.log('✅ Login process completed successfully');
      return; // Prevent any further processing
    } catch (err: any) {
      console.log(`This is the error itself, ${err.code}`)
      console.error('❌ Sign in error:', err.message);

      // Handle specific error types with helpful messages
      console.log('🔍 Error Details:', {
        name: err.name,
        code: err.code,
        message: err.message,
        constructor: err.constructor.name
      });

      const errorCode = err.name || err.code || 'UNKNOWN_ERROR';
      console.log(`🎯 Using errorCode: ${errorCode}`);

      switch (errorCode) {
        case 'FIRST_TIME_LOGIN':
          setError(err.message + '. Redirecting to account setup...');
          console.log(`this is identifier ${identifier}`)
          // For first-time login, we need to redirect to setup
          setTimeout(() => {
            if (identifier.includes('/')) {
              console.log(`Getting student data for: ${identifier}`)
              // Student matric number - get real data from Firestore
              getStudentDataForSetup(identifier.toUpperCase()).then((studentData) => {
                console.log('✅ Student data retrieved:', studentData);
                localStorage.setItem('temp_student_setup', JSON.stringify(studentData));
                window.location.href = '/setup/student';
              }).catch((error) => {
                console.error('Failed to get student data:', error);
                // Fallback with minimal data
                localStorage.setItem('temp_student_setup', JSON.stringify({
                  matricNumber: identifier.toUpperCase(),
                  name: 'Student',
                  departmentName: 'Unknown',
                  levelName: 'Unknown',
                  facultyName: 'Unknown'
                }));
                window.location.href = '/setup/student';
              });
            } else {
              // Staff ID - get real data from Firestore
              console.log(`Getting staff data for: ${identifier}`)
              getStaffDataForSetup(identifier).then((staffData) => {
                console.log('✅ Staff data retrieved:', staffData);
                localStorage.setItem('temp_staff_setup', JSON.stringify(staffData));
                window.location.href = '/setup/staff';
              }).catch((error) => {
                console.error('Failed to get staff data:', error);
                // Fallback with minimal data
                localStorage.setItem('temp_staff_setup', JSON.stringify({
                  userId: identifier,
                  name: 'Staff Member',
                  role: 'supervisor',
                  department: 'Unknown',
                  email: ''
                }));
                window.location.href = '/setup/staff';
              });
            }
          }, 1500);
          break;
        case 'STUDENT_NOT_FOUND':
          setError(err.message + '. Please check your matric number or contact the admin.');
          break;
        case 'STAFF_NOT_FOUND':
          setError(err.message + '. Please check your staff ID or contact the admin.');
          break;
        case 'ACCOUNT_NOT_FOUND':
          setError('No account found with this ID. Please check your ID or contact the admin.');
          break;
        case 'INVALID_DEFAULT_PASSWORD':
          setError(err.message + '. For first-time login, use your ID and default password.');
          break;
        case 'INVALID_EMAIL_PASSWORD':
          setError(err.message + '. Please check your credentials.');
          break;
        case 'EMAIL_NOT_VERIFIED':
          setError(err.message + '. Check your inbox for the verification link.');
          break;
        case 'INVALID_CREDENTIALS':
          setError(err.message + '. Please check your credentials.');
          break;
        default:
          console.log(`Unhandled error code: ${errorCode}, message: ${err.message}`);
          setError(err.message || 'An error occurred during sign in. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`w-full max-w-md mx-auto ${isModal ? 'p-0' : 'p-4'}`}>
      <Card className="border-0 shadow-lg">
        <CardHeader className="text-center space-y-4 pb-6">
          <div className="flex items-center justify-center">
            <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
              <GraduationCap className="h-7 w-7 text-white" />
            </div>
          </div>
          <div>
            <TypographyH2 className="text-2xl font-bold">Welcome Back</TypographyH2>
            <CardDescription className="font-sans text-muted-foreground mt-2">
              Sign in to access Hall Automata
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="identifier" className="font-sans text-sm font-medium">
                Email or ID
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="identifier"
                  type="text"
                  placeholder="<EMAIL> or CS/2021/001"
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                  className="pl-10 font-sans"
                  required
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Enter your email address or student ID (matric number)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="font-sans text-sm font-medium">
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 pr-10 font-sans"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between text-sm">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" className="rounded border-border" />
                <span className="font-sans text-muted-foreground">Remember me</span>
              </label>
              <a href="#" className="font-sans text-primary hover:underline">
                Forgot password?
              </a>
            </div>

            <Button
              type="submit"
              className="w-full font-sans"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </Button>
          </form>

          <div className="text-center">
            <TypographyMuted className="text-sm">
              Don&apos;t have an account?{" "}
              <a href="/sign-up" className="text-primary hover:underline font-medium">
                Contact Administrator
              </a>
            </TypographyMuted>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
