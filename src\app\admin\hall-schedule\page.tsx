"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { TypographyH2, TypographyP } from "@/components/ui/typography";
import { 
  Calendar, 
  Clock,
  MapPin,
  Users,
  Building,
  RefreshCw,
  Eye,
  Download,
  Loader2
} from "lucide-react";

interface HallScheduleItem {
  id: string;
  examId: string;
  courseCode: string;
  courseTitle: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  hallName: string;
  hallLocation: string;
  hallCapacity: number;
  studentsCount: number;
  departmentGroups: Array<{
    departmentName: string;
    departmentCode: string;
    levelName: string;
    studyMode: string;
    studentCount: number;
  }>;
  session: string;
  semester: string;
}

export default function HallSchedulePage() {
  const [scheduleData, setScheduleData] = useState<HallScheduleItem[]>([]);
  const [filteredData, setFilteredData] = useState<HallScheduleItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSession, setSelectedSession] = useState<string>("2024/2025");
  const [selectedSemester, setSelectedSemester] = useState<string>("First");
  const [selectedDate, setSelectedDate] = useState<string>("all");
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedScheduleItem, setSelectedScheduleItem] = useState<HallScheduleItem | null>(null);

  useEffect(() => {
    loadHallSchedule();
  }, []);

  useEffect(() => {
    filterScheduleData();
  }, [scheduleData, selectedSession, selectedSemester, selectedDate]);

  const loadHallSchedule = async () => {
    setIsLoading(true);
    try {
      const { collection, query, where, getDocs, orderBy } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      // Get all hall allocations
      const hallAllocationsRef = collection(db, 'hall_allocations');
      const allocationsQuery = query(hallAllocationsRef, orderBy('examDate', 'asc'));
      const allocationsSnapshot = await getDocs(allocationsQuery);

      const scheduleItems: HallScheduleItem[] = [];

      for (const allocationDoc of allocationsSnapshot.docs) {
        const allocationData = allocationDoc.data();

        // Get exam details
        const examTimetableRef = collection(db, 'exam_timetable');
        const examQuery = query(examTimetableRef, where('__name__', '==', allocationData.examId));
        const examSnapshot = await getDocs(examQuery);

        if (!examSnapshot.empty) {
          const examData = examSnapshot.docs[0].data();

          // Get hall details
          const hallsRef = collection(db, 'halls');
          const hallQuery = query(hallsRef, where('name', '==', allocationData.hallName));
          const hallSnapshot = await getDocs(hallQuery);

          let hallDetails = {
            location: 'Location TBA',
            capacity: allocationData.hallCapacity || 100
          };

          if (!hallSnapshot.empty) {
            const hallData = hallSnapshot.docs[0].data();
            hallDetails = {
              location: hallData.location || 'Location TBA',
              capacity: hallData.capacity || 100
            };
          }

          // Get student groups for this exam
          const studentsRef = collection(db, 'students');
          const studentsQuery = query(
            studentsRef,
            where('levelName', '==', examData.levelName),
            where('departmentName', '==', examData.departmentName)
          );
          const studentsSnapshot = await getDocs(studentsQuery);

          // Group students by department and study mode
          const departmentGroups: { [key: string]: any } = {};
          studentsSnapshot.forEach((studentDoc) => {
            const studentData = studentDoc.data();
            const groupKey = `${studentData.departmentName}-${studentData.levelName}-${studentData.studyMode}`;
            
            if (!departmentGroups[groupKey]) {
              departmentGroups[groupKey] = {
                departmentName: studentData.departmentName,
                departmentCode: studentData.departmentName.substring(0, 3).toUpperCase(),
                levelName: studentData.levelName,
                studyMode: studentData.studyMode,
                studentCount: 0
              };
            }
            departmentGroups[groupKey].studentCount++;
          });

          scheduleItems.push({
            id: allocationDoc.id,
            examId: allocationData.examId,
            courseCode: examData.courseCode,
            courseTitle: examData.courseTitle,
            examDate: examData.examDate.toDate(),
            startTime: examData.startTime,
            endTime: examData.endTime,
            hallName: allocationData.hallName,
            hallLocation: hallDetails.location,
            hallCapacity: hallDetails.capacity,
            studentsCount: allocationData.studentsCount || 0,
            departmentGroups: Object.values(departmentGroups),
            session: examData.session || '2024/2025',
            semester: examData.semester || 'First'
          });
        }
      }

      setScheduleData(scheduleItems);
      console.log(`✅ Loaded ${scheduleItems.length} hall schedule items`);
    } catch (error) {
      console.error('❌ Error loading hall schedule:', error);
      setScheduleData([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filterScheduleData = () => {
    let filtered = scheduleData;

    // Session filter
    if (selectedSession !== "all") {
      filtered = filtered.filter(item => item.session === selectedSession);
    }

    // Semester filter
    if (selectedSemester !== "all") {
      filtered = filtered.filter(item => item.semester === selectedSemester);
    }

    // Date filter
    if (selectedDate !== "all") {
      const filterDate = new Date(selectedDate);
      filtered = filtered.filter(item => 
        item.examDate.toDateString() === filterDate.toDateString()
      );
    }

    setFilteredData(filtered);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const formatTime = (startTime: string, endTime: string) => {
    return `${startTime} - ${endTime}`;
  };

  const exportSchedule = (format: 'csv' | 'excel') => {
    console.log(`Exporting ${filteredData.length} schedule items as ${format}`);
    // TODO: Implement export functionality
  };

  // Group data by date and time for better visualization
  const groupedData = filteredData.reduce((acc, item) => {
    const dateKey = item.examDate.toDateString();
    const timeKey = `${item.startTime}-${item.endTime}`;
    
    if (!acc[dateKey]) {
      acc[dateKey] = {};
    }
    if (!acc[dateKey][timeKey]) {
      acc[dateKey][timeKey] = [];
    }
    acc[dateKey][timeKey].push(item);
    
    return acc;
  }, {} as { [date: string]: { [time: string]: HallScheduleItem[] } });

  const uniqueDates = [...new Set(scheduleData.map(item => item.examDate.toISOString().split('T')[0]))];

  return (
    <AdminLayout
      title="Hall Schedule Overview"
      description="Comprehensive view of hall allocations by semester, date, and time"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Allocations</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{scheduleData.length}</div>
            <p className="text-xs text-muted-foreground">All sessions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current View</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredData.length}</div>
            <p className="text-xs text-muted-foreground">Filtered results</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredData.reduce((sum, item) => sum + item.studentsCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Allocated</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Halls Used</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(filteredData.map(item => item.hallName)).size}
            </div>
            <p className="text-xs text-muted-foreground">Unique halls</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle>Filter Schedule</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" onClick={loadHallSchedule} disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
              <Button variant="outline" onClick={() => exportSchedule('excel')}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Select value={selectedSession} onValueChange={setSelectedSession}>
              <SelectTrigger>
                <SelectValue placeholder="Session" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sessions</SelectItem>
                <SelectItem value="2024/2025">2024/2025</SelectItem>
                <SelectItem value="2023/2024">2023/2024</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedSemester} onValueChange={setSelectedSemester}>
              <SelectTrigger>
                <SelectValue placeholder="Semester" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Semesters</SelectItem>
                <SelectItem value="First">First Semester</SelectItem>
                <SelectItem value="Second">Second Semester</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedDate} onValueChange={setSelectedDate}>
              <SelectTrigger>
                <SelectValue placeholder="Date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Dates</SelectItem>
                {uniqueDates.map((date) => (
                  <SelectItem key={date} value={date}>
                    {formatDate(new Date(date))}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setSelectedSession("2024/2025");
                setSelectedSemester("First");
                setSelectedDate("all");
              }}
            >
              Reset Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Schedule Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Hall Allocation Schedule
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-12">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-muted-foreground">Loading hall schedule...</p>
            </div>
          ) : Object.keys(groupedData).length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-muted-foreground mb-2">No hall allocations found</p>
              <p className="text-sm text-gray-500">
                Hall allocations will appear here once exams are scheduled
              </p>
            </div>
          ) : (
            <div className="space-y-8">
              {Object.entries(groupedData).map(([date, timeSlots]) => (
                <div key={date} className="space-y-4">
                  <div className="flex items-center gap-2 pb-2 border-b">
                    <Calendar className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">{formatDate(new Date(date))}</h3>
                  </div>
                  
                  {Object.entries(timeSlots).map(([timeSlot, items]) => (
                    <div key={timeSlot} className="space-y-2">
                      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>{timeSlot.replace('-', ' - ')}</span>
                      </div>
                      
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Course</TableHead>
                              <TableHead>Hall</TableHead>
                              <TableHead>Capacity</TableHead>
                              <TableHead>Students</TableHead>
                              <TableHead>Department Groups</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {items.map((item) => (
                              <TableRow key={item.id}>
                                <TableCell>
                                  <div>
                                    <div className="font-medium">{item.courseCode}</div>
                                    <div className="text-sm text-muted-foreground">
                                      {item.courseTitle}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div>
                                    <div className="font-medium">{item.hallName}</div>
                                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                                      <MapPin className="h-3 w-3" />
                                      {item.hallLocation}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline" className="font-mono">
                                    {item.hallCapacity}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-1">
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                    <span className="font-medium">{item.studentsCount}</span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex flex-wrap gap-1">
                                    {item.departmentGroups.slice(0, 2).map((group, index) => (
                                      <Badge key={index} variant="outline" className="text-xs">
                                        {group.departmentCode}-{group.levelName}
                                        {group.studyMode === 'part_time' && ' (PT)'}
                                        ({group.studentCount})
                                      </Badge>
                                    ))}
                                    {item.departmentGroups.length > 2 && (
                                      <Badge variant="outline" className="text-xs">
                                        +{item.departmentGroups.length - 2} more
                                      </Badge>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedScheduleItem(item);
                                      setShowDetailsDialog(true);
                                    }}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Hall Allocation Details</DialogTitle>
          </DialogHeader>
          {selectedScheduleItem && (
            <div className="space-y-6 py-4">
              {/* Exam Information */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Exam Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Course</label>
                    <p className="text-sm font-medium">
                      {selectedScheduleItem.courseCode} - {selectedScheduleItem.courseTitle}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Date & Time</label>
                    <p className="text-sm">
                      {formatDate(selectedScheduleItem.examDate)} • {formatTime(selectedScheduleItem.startTime, selectedScheduleItem.endTime)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Session</label>
                    <p className="text-sm">{selectedScheduleItem.session}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Semester</label>
                    <p className="text-sm">{selectedScheduleItem.semester}</p>
                  </div>
                </div>
              </div>

              {/* Hall Information */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Hall Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Hall Name</label>
                    <p className="text-sm font-medium">{selectedScheduleItem.hallName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Location</label>
                    <p className="text-sm">{selectedScheduleItem.hallLocation}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Capacity</label>
                    <p className="text-sm">{selectedScheduleItem.hallCapacity} students</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Allocated</label>
                    <p className="text-sm">{selectedScheduleItem.studentsCount} students</p>
                  </div>
                </div>
              </div>

              {/* Student Groups */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Student Groups</h3>
                <div className="space-y-2">
                  {selectedScheduleItem.departmentGroups.map((group, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div>
                        <div className="font-medium">
                          {group.departmentName} - {group.levelName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {group.studyMode === 'full_time' ? 'Full Time' : 'Part Time'}
                        </div>
                      </div>
                      <Badge variant="outline" className="font-mono">
                        {group.studentCount} students
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => setShowDetailsDialog(false)}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
