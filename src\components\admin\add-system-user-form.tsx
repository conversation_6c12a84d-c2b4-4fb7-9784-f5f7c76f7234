"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Shield, Plus, Loader2, AlertCircle, Users } from "lucide-react";
import { 
  SystemUserFormData, 
  Department,
  UserRole,
  UserStatus,
  generateSystemUserId,
  generateSystemUserPassword
} from "@/types/department";
import { getAllDepartments } from "@/lib/firebase/department-service";

interface AddSystemUserFormProps {
  onSubmit: (data: SystemUserFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function AddSystemUserForm({ onSubmit, onCancel, isLoading = false }: AddSystemUserFormProps) {
  const [formData, setFormData] = useState<SystemUserFormData>({
    name: "",
    role: "supervisor",
    email: "",
    phoneNumber: "",
    departmentAffiliation: [],
    status: "active"
  });

  const [departments, setDepartments] = useState<Department[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(true);

  // Load departments on component mount
  useEffect(() => {
    loadDepartments();
  }, []);

  const loadDepartments = async () => {
    try {
      const departmentsData = await getAllDepartments();
      setDepartments(departmentsData);
    } catch (error) {
      console.error("Error loading departments:", error);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate required fields
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    // Validate email format if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = validateForm();
    if (!isValid) {
      return;
    }

    await onSubmit(formData);
  };

  const handleDepartmentToggle = (departmentId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      departmentAffiliation: checked
        ? [...prev.departmentAffiliation || [], departmentId]
        : (prev.departmentAffiliation || []).filter(id => id !== departmentId)
    }));
  };

  const previewUserId = generateSystemUserId(formData.role);
  const previewPassword = generateSystemUserPassword(formData.role);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Add System User
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Dr. John Smith"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={errors.name ? "border-red-500" : ""}
                required
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label>Role *</Label>
              <Select 
                value={formData.role} 
                onValueChange={(value: UserRole) => setFormData(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="supervisor">
                    <div>
                      <p className="font-medium">Supervisor</p>
                      <p className="text-xs text-muted-foreground">Can manage exams and oversee invigilators</p>
                    </div>
                  </SelectItem>
                  <SelectItem value="invigilator">
                    <div>
                      <p className="font-medium">Invigilator</p>
                      <p className="text-xs text-muted-foreground">Can invigilate exams and mark attendance</p>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address (Optional)</Label>
              <Input
                id="email"
                type="email"
                placeholder="e.g., <EMAIL>"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.email}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Phone Number (Optional)</Label>
              <Input
                id="phoneNumber"
                placeholder="e.g., +234 ************"
                value={formData.phoneNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
              />
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label>Account Status *</Label>
            <Select 
              value={formData.status} 
              onValueChange={(value: UserStatus) => setFormData(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">
                  <span className="text-green-600">Active</span>
                </SelectItem>
                <SelectItem value="inactive">
                  <span className="text-gray-600">Inactive</span>
                </SelectItem>
                <SelectItem value="suspended">
                  <span className="text-red-600">Suspended</span>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Department Affiliation */}
          <div className="space-y-4">
            <div>
              <Label>Department Affiliation (Optional)</Label>
              <p className="text-sm text-muted-foreground">
                Select departments this user can work with. Leave empty for all departments.
              </p>
            </div>
            
            {isLoadingDepartments ? (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading departments...</span>
              </div>
            ) : departments.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-48 overflow-y-auto border rounded-lg p-4">
                {departments.map((department) => (
                  <div key={department.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`dept-${department.id}`}
                      checked={(formData.departmentAffiliation || []).includes(department.id)}
                      onCheckedChange={(checked) => handleDepartmentToggle(department.id, checked as boolean)}
                    />
                    <Label htmlFor={`dept-${department.id}`} className="text-sm">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          {department.facultyCode}-{department.code}
                        </Badge>
                        <span>{department.name}</span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No departments available</p>
            )}
          </div>

          {/* Account Preview */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Account Preview</h4>
            <div className="space-y-1 text-sm">
              <p><strong>Role:</strong> {formData.role === "supervisor" ? "Supervisor" : "Invigilator"}</p>
              <p><strong>User ID:</strong> <code className="bg-background px-1 rounded">{previewUserId}</code></p>
              <p><strong>Default Password:</strong> <code className="bg-background px-1 rounded">{previewPassword}</code></p>
              <p><strong>Status:</strong> 
                <Badge 
                  variant="outline" 
                  className={`ml-2 ${
                    formData.status === "active" ? "text-green-600" :
                    formData.status === "inactive" ? "text-gray-600" : "text-red-600"
                  }`}
                >
                  {formData.status.charAt(0).toUpperCase() + formData.status.slice(1)}
                </Badge>
              </p>
              {formData.departmentAffiliation && formData.departmentAffiliation.length > 0 && (
                <p><strong>Departments:</strong> {formData.departmentAffiliation.length} selected</p>
              )}
            </div>
          </div>

          {/* Role Description */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              {formData.role === "supervisor" ? "Supervisor" : "Invigilator"} Responsibilities
            </h4>
            <div className="text-sm text-blue-800 space-y-1">
              {formData.role === "supervisor" ? (
                <>
                  <p>• Manage exam sessions and hall allocations</p>
                  <p>• Oversee invigilators during examinations</p>
                  <p>• Generate and review attendance reports</p>
                  <p>• Handle exam-related issues and incidents</p>
                </>
              ) : (
                <>
                  <p>• Invigilate assigned examination halls</p>
                  <p>• Mark student attendance using biometric system</p>
                  <p>• Monitor exam conduct and report issues</p>
                  <p>• Assist with exam logistics as needed</p>
                </>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Create {formData.role === "supervisor" ? "Supervisor" : "Invigilator"}
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
