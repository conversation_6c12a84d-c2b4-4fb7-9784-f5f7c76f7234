"use client";

import { <PERSON> } from "@/types/hall";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import { AlertTriangle, Trash2, X, Loader2 } from "lucide-react";

interface DeleteHallDialogProps {
  hall: Hall;
  onConfirm: (hallId: string) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function DeleteHallDialog({ hall, onConfirm, onCancel, isLoading = false }: DeleteHallDialogProps) {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isLoading) {
      onCancel();
    }
  };

  const handleConfirm = async () => {
    await onConfirm(hall.id);
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <Card className="w-full max-w-md">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Delete Hall
            </CardTitle>
            {!isLoading && (
              <Button variant="ghost" size="sm" onClick={onCancel}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Warning Message */}
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-red-800">This action cannot be undone</p>
                <p className="text-sm text-red-700 mt-1">
                  Deleting this hall will permanently remove it from the system. Any scheduled exams using this hall will be affected.
                </p>
              </div>
            </div>
          </div>

          {/* Hall Information */}
          <div className="p-4 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground mb-2">You are about to delete:</p>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">{hall.name}</span>
                <Badge variant="outline" className="font-mono">
                  {hall.code}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                <p>📍 {hall.location}</p>
                <p>👥 Capacity: {hall.capacity} students</p>
                <p>🏢 Type: {hall.type.replace('_', ' ')}</p>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <TypographyP className="text-sm text-muted-foreground">
              Type <strong className="font-mono text-foreground">{hall.code}</strong> to confirm deletion:
            </TypographyP>
            <input
              type="text"
              placeholder={`Type "${hall.code}" here`}
              className="w-full px-3 py-2 border border-border rounded-md font-mono text-sm"
              onChange={(e) => {
                const button = document.getElementById('confirm-delete-btn') as HTMLButtonElement;
                if (button) {
                  button.disabled = e.target.value !== hall.code || isLoading;
                }
              }}
              disabled={isLoading}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button 
              variant="outline" 
              onClick={onCancel} 
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button 
              id="confirm-delete-btn"
              variant="destructive" 
              onClick={handleConfirm}
              disabled={true} // Initially disabled until confirmation text is entered
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Hall
                </>
              )}
            </Button>
          </div>

          {/* Additional Warning */}
          <div className="text-xs text-muted-foreground text-center pt-2 border-t border-border">
            ⚠️ This will also remove any associated exam schedules and allocations
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
