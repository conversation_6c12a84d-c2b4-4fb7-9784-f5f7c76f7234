// Simple test to verify Firebase connection and basic seeding
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc } from 'firebase/firestore';
import { firebaseConfig } from '../lib/firebase/config';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function testFirebaseConnection() {
  try {
    console.log('🔥 Testing Firebase connection...');
    
    // Try to create a simple test document
    const testRef = await addDoc(collection(db, 'test'), {
      message: 'Firebase connection test',
      timestamp: new Date(),
      createdBy: 'test-script'
    });
    
    console.log('✅ Firebase connection successful!');
    console.log('📄 Test document created with ID:', testRef.id);
    
    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    return false;
  }
}

// Run the test
testFirebaseConnection()
  .then((success) => {
    if (success) {
      console.log('🎉 Ready to run full database seeding!');
      console.log('Run: npm run seed');
    } else {
      console.log('❌ Fix Firebase configuration before seeding');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
