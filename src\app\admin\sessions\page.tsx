"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Calendar,
  Plus,
  Edit,
  Trash2,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  BookOpen,
  GraduationCap,
  AlertTriangle
} from "lucide-react";
import { getAllAcademicSessions, getSemestersBySession } from "@/lib/firebase/exam-service";
import { getAllExams } from "@/lib/firebase/exam-service";

interface AcademicSession {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  status: 'upcoming' | 'active' | 'completed';
  semesters: Semester[];
  createdAt: Date;
}

interface Semester {
  id: string;
  name: string;
  code: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  sessionId: string;
  sessionName: string;
  examCount: number;
  createdAt: Date;
}

export default function SessionsPage() {
  const [sessions, setSessions] = useState<AcademicSession[]>([]);
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'sessions' | 'semesters'>('sessions');

  const loadData = async () => {
    setIsLoading(true);
    try {
      console.log('📅 Loading academic sessions and semesters from Firestore...');

      // Load real data from Firestore
      const [sessionsData, examsData] = await Promise.all([
        getAllAcademicSessions(),
        getAllExams()
      ]);

      console.log(`✅ Loaded ${sessionsData.length} sessions from Firestore`);

      // Convert sessions to match our interface and determine status
      const sessionsWithStatus: AcademicSession[] = sessionsData.map(session => {
        const now = new Date();
        let status: 'upcoming' | 'active' | 'completed' = 'upcoming';

        if (session.isActive) {
          status = 'active';
        } else if (new Date(session.endDate) < now) {
          status = 'completed';
        }

        return {
          ...session,
          status,
          semesters: [] // Will be populated separately
        };
      });

      // Load semesters for all sessions
      const allSemesters: Semester[] = [];
      for (const session of sessionsData) {
        try {
          const sessionSemesters = await getSemestersBySession(session.id);

          // Add exam count to each semester
          const semestersWithExamCount = sessionSemesters.map(semester => ({
            ...semester,
            examCount: examsData.filter(exam => exam.semesterId === semester.id).length
          }));

          allSemesters.push(...semestersWithExamCount);
        } catch (error) {
          console.warn(`Failed to load semesters for session ${session.name}:`, error);
        }
      }

      console.log(`✅ Loaded ${allSemesters.length} semesters from Firestore`);

      setSessions(sessionsWithStatus);
      setSemesters(allSemesters);
    } catch (error) {
      console.error('❌ Error loading sessions and semesters:', error);
      setSessions([]);
      setSemesters([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getSessionStatusBadge = (status: AcademicSession['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-600">Active</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      case 'upcoming':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Upcoming</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDateRange = (startDate: Date, endDate: Date) => {
    return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
  };

  const getDuration = (startDate: Date, endDate: Date) => {
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const months = Math.floor(diffDays / 30);
    return `${months} months`;
  };

  const stats = {
    totalSessions: sessions.length,
    activeSessions: sessions.filter(s => s.isActive).length,
    totalSemesters: semesters.length,
    activeSemesters: semesters.filter(s => s.isActive).length,
    totalExams: semesters.reduce((sum, s) => sum + s.examCount, 0)
  };

  return (
    <AdminLayout
      title="Academic Sessions & Semesters"
      description="Manage academic sessions and semester periods"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.totalSessions}
            </div>
            <p className="text-xs text-muted-foreground">Academic sessions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Session</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.activeSessions}
            </div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Semesters</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.totalSemesters}
            </div>
            <p className="text-xs text-muted-foreground">All semesters</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Semester</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.activeSemesters}
            </div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
            <GraduationCap className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.totalExams}
            </div>
            <p className="text-xs text-muted-foreground">Scheduled exams</p>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6">
        <Button
          variant={activeTab === 'sessions' ? 'default' : 'outline'}
          onClick={() => setActiveTab('sessions')}
          className="flex items-center gap-2"
        >
          <Calendar className="h-4 w-4" />
          Academic Sessions
        </Button>
        <Button
          variant={activeTab === 'semesters' ? 'default' : 'outline'}
          onClick={() => setActiveTab('semesters')}
          className="flex items-center gap-2"
        >
          <BookOpen className="h-4 w-4" />
          Semesters
        </Button>
      </div>

      {/* Sessions Tab */}
      {activeTab === 'sessions' && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Academic Sessions
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadData}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  Refresh
                </Button>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Session
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="h-12 w-24 bg-muted rounded animate-pulse" />
                    <div className="space-y-2 flex-1">
                      <div className="h-4 bg-muted rounded w-1/3 animate-pulse" />
                      <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
                    </div>
                    <div className="h-6 bg-muted rounded w-16 animate-pulse" />
                  </div>
                ))}
              </div>
            ) : sessions.length === 0 ? (
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <TypographyP className="text-muted-foreground">
                  No academic sessions found
                </TypographyP>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Session</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Period</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Semesters</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            {session.isActive && (
                              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                            )}
                            <div>
                              <div className="font-medium text-lg">{session.name}</div>
                              <div className="text-sm text-muted-foreground">
                                Created {session.createdAt.toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {getDuration(session.startDate, session.endDate)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDateRange(session.startDate, session.endDate)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getSessionStatusBadge(session.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">
                              {semesters.filter(s => s.sessionId === session.id).length}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            {!session.isActive && (
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Semesters Tab */}
      {activeTab === 'semesters' && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Semesters
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadData}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  Refresh
                </Button>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Semester
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2].map((i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="h-12 w-24 bg-muted rounded animate-pulse" />
                    <div className="space-y-2 flex-1">
                      <div className="h-4 bg-muted rounded w-1/3 animate-pulse" />
                      <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
                    </div>
                    <div className="h-6 bg-muted rounded w-16 animate-pulse" />
                  </div>
                ))}
              </div>
            ) : semesters.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <TypographyP className="text-muted-foreground">
                  No semesters found
                </TypographyP>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Semester</TableHead>
                      <TableHead>Session</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Period</TableHead>
                      <TableHead>Exams</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {semesters.map((semester) => (
                      <TableRow key={semester.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            {semester.isActive && (
                              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                            )}
                            <div>
                              <div className="font-medium">{semester.name}</div>
                              <Badge variant="outline" className="text-xs font-mono mt-1">
                                {semester.code}
                              </Badge>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {semester.sessionName}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {getDuration(semester.startDate, semester.endDate)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDateRange(semester.startDate, semester.endDate)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <GraduationCap className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{semester.examCount}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {semester.isActive ? (
                            <Badge className="bg-green-600">Active</Badge>
                          ) : (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            {!semester.isActive && (
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </AdminLayout>
  );
}
