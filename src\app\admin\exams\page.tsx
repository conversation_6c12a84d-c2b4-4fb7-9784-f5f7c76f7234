"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TypographyH2, TypographyP } from "@/components/ui/typography";
import { AddExamForm } from "@/components/admin/add-exam-form";
import { HallAllocationInterface } from "@/components/admin/hall-allocation-interface";
import { BulkExamForm } from "@/components/admin/bulk-exam-form";
import { ExcelImportForm } from "@/components/admin/excel-import-form";
import { BulkHallAllocation } from "@/components/admin/bulk-hall-allocation";
import {
  Calendar,
  Plus,
  Search,
  Filter,
  Clock,
  Users,
  Building,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Eye,
  Edit,
  Trash2,
  FileSpreadsheet
} from "lucide-react";
import {
  Exam,
  ExamFormData,
  AcademicSession,
  Semester,
  getExamStatusLabel,
  formatExamTime
} from "@/types/exam";
import {
  addExam,
  getAllExams,
  getExamsBySession,
  getAllAcademicSessions,
  getSemestersBySession,
  checkExamConflicts,
  updateExamStudentCount,
  testFirebaseConnection
} from "@/lib/firebase/exam-service";

type ViewMode = 'list' | 'add-exam' | 'bulk-exam' | 'excel-import' | 'view-exam' | 'allocate-halls' | 'bulk-allocation';

export default function ExamSchedulingPage() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [filteredExams, setFilteredExams] = useState<Exam[]>([]);
  const [sessions, setSessions] = useState<AcademicSession[]>([]);
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [selectedExams, setSelectedExams] = useState<Exam[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<string>("");

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [sessionFilter, setSessionFilter] = useState<string>("all");
  const [semesterFilter, setSemesterFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Load data on component mount
  useEffect(() => {
    loadInitialData();
  }, []);

  // Apply filters when data or filters change
  useEffect(() => {
    applyFilters();
  }, [exams, searchTerm, sessionFilter, semesterFilter, statusFilter]);

  // Load semesters when session filter changes
  useEffect(() => {
    if (sessionFilter && sessionFilter !== "all") {
      loadSemestersForFilter(sessionFilter);
    } else {
      setSemesters([]);
      setSemesterFilter("all");
    }
  }, [sessionFilter]);

  const loadInitialData = async () => {
    try {
      setIsLoadingData(true);
      setError(""); // Clear any previous errors

      console.log("🔄 Loading exam management data...");

      // Test Firebase connection first
      const isConnected = await testFirebaseConnection();
      if (!isConnected) {
        throw new Error("Firebase connection failed. Please check your internet connection and Firebase configuration.");
      }

      const [examsData, sessionsData] = await Promise.all([
        getAllExams().catch(err => {
          console.error("❌ Error loading exams:", err);
          throw new Error(`Failed to load exams: ${err.message}`);
        }),
        getAllAcademicSessions().catch(err => {
          console.error("❌ Error loading sessions:", err);
          throw new Error(`Failed to load sessions: ${err.message}`);
        })
      ]);

      console.log(`✅ Loaded ${examsData.length} exams and ${sessionsData.length} sessions`);

      setExams(examsData);
      setSessions(sessionsData);
    } catch (error) {
      console.error("❌ Error loading exam management data:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load exam data";
      setError(errorMessage);
    } finally {
      setIsLoadingData(false);
    }
  };

  const loadSemestersForFilter = async (sessionId: string) => {
    try {
      const semestersData = await getSemestersBySession(sessionId);
      setSemesters(semestersData);
    } catch (error) {
      console.error("Error loading semesters:", error);
    }
  };

  const applyFilters = () => {
    let filtered = [...exams];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.courseCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.courseTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.departmentName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Session filter
    if (sessionFilter !== "all") {
      filtered = filtered.filter(exam => exam.sessionId === sessionFilter);
    }

    // Semester filter
    if (semesterFilter !== "all") {
      filtered = filtered.filter(exam => exam.semesterId === semesterFilter);
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(exam => exam.status === statusFilter);
    }

    setFilteredExams(filtered);
  };

  const handleAddExam = async (examData: ExamFormData) => {
    try {
      setIsLoading(true);
      setError("");

      console.log("🔄 Creating new exam:", examData.courseId);

      // Check for conflicts before creating
      console.log("🔍 Checking for conflicts...");
      const conflicts = await checkExamConflicts(examData).catch(err => {
        console.warn("⚠️ Could not check conflicts (continuing anyway):", err);
        return []; // Continue without conflict checking if it fails
      });

      if (conflicts.length > 0) {
        const conflictMessages = conflicts.map(c => c.message).join(', ');
        throw new Error(`Conflicts detected: ${conflictMessages}`);
      }

      // Create the exam
      console.log("📝 Creating exam in database...");
      const newExam = await addExam(examData, "admin-user-id"); // TODO: Get actual user ID
      console.log("✅ Exam created successfully:", newExam.id);

      // Update student count (optional - may be 0 if no students registered yet)
      try {
        console.log("👥 Updating student count...");
        await updateExamStudentCount(newExam.id);
        console.log("✅ Student count updated");
      } catch (error) {
        console.warn("⚠️ Could not update student count (continuing anyway):", error);
        // Continue anyway - this is not critical for exam creation
      }

      // Reload exams
      console.log("🔄 Reloading exam data...");
      await loadInitialData();

      console.log("🎉 Exam creation completed successfully!");
      setViewMode('list');
    } catch (error) {
      console.error("❌ Error adding exam:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to add exam";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewExam = (exam: Exam) => {
    setSelectedExam(exam);
    setViewMode('view-exam');
  };

  const handleAllocateHalls = (exam: Exam) => {
    setSelectedExam(exam);
    setViewMode('allocate-halls');
  };

  const handleAllocationComplete = () => {
    // Reload exams to update allocation status
    loadInitialData();
    setViewMode('list');
  };

  const handleBulkExamSubmit = async (bulkExams: any[]) => {
    try {
      setIsLoading(true);
      setError("");

      console.log("🔄 Creating bulk exams:", bulkExams.length);

      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const bulkExam of bulkExams) {
        try {
          // Convert bulk exam data to ExamFormData format
          const examData: ExamFormData = {
            courseId: `course-${bulkExam.courseCode.toLowerCase()}`, // Generate course ID
            sessionId: bulkExam.sessionId,
            semesterId: bulkExam.semesterId,
            facultyId: bulkExam.facultyId,
            departmentId: bulkExam.departmentId,
            levelId: bulkExam.levelId,
            examDate: bulkExam.examDate,
            startTime: bulkExam.startTime,
            endTime: bulkExam.endTime,
            selectedHalls: bulkExam.selectedHalls,
            hallCapacityOverride: bulkExam.hallCapacityOverride
          };

          // Check for conflicts
          const conflicts = await checkExamConflicts(examData).catch(() => []);
          if (conflicts.length > 0) {
            errors.push(`${bulkExam.courseCode}: ${conflicts[0].message}`);
            errorCount++;
            continue;
          }

          // Create the exam
          await addExam(examData, "admin-user-id");
          successCount++;

        } catch (error) {
          console.error(`Error creating exam ${bulkExam.courseCode}:`, error);
          errors.push(`${bulkExam.courseCode}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          errorCount++;
        }
      }

      // Show results
      if (successCount > 0) {
        console.log(`✅ Successfully created ${successCount} exams`);
      }

      if (errorCount > 0) {
        const errorMessage = `Created ${successCount} exams successfully. ${errorCount} failed:\n${errors.slice(0, 5).join('\n')}${errors.length > 5 ? `\n...and ${errors.length - 5} more` : ''}`;
        setError(errorMessage);
      }

      // Reload exams
      await loadInitialData();

      if (errorCount === 0) {
        setViewMode('list');
      }

    } catch (error) {
      console.error("Error in bulk exam creation:", error);
      setError(error instanceof Error ? error.message : "Failed to create bulk exams");
    } finally {
      setIsLoading(false);
    }
  };

  const handleExcelImport = async (excelExams: any[]) => {
    // Convert Excel data to bulk exam format and submit
    const bulkExams = excelExams.map(excel => ({
      id: crypto.randomUUID(),
      courseCode: excel.courseCode,
      courseTitle: excel.courseTitle,
      isElective: excel.isElective,
      startTime: excel.startTime,
      endTime: excel.endTime,
      // These will need to be set from common data in the form
      sessionId: "",
      semesterId: "",
      facultyId: "",
      departmentId: "",
      levelId: "",
      examDate: new Date(),
      selectedHalls: [],
      hallCapacityOverride: {}
    }));

    await handleBulkExamSubmit(bulkExams);
  };

  const handleBulkAllocation = () => {
    const unallocatedExams = exams.filter(exam =>
      exam.status === 'scheduled' && !exam.isAllocated
    );
    setSelectedExams(unallocatedExams);
    setViewMode('bulk-allocation');
  };

  const handleCloseForm = () => {
    setViewMode('list');
    setSelectedExam(null);
    setError("");
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSessionFilter("all");
    setSemesterFilter("all");
    setStatusFilter("all");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft": return "bg-gray-100 text-gray-800";
      case "scheduled": return "bg-blue-100 text-blue-800";
      case "allocated": return "bg-yellow-100 text-yellow-800";
      case "approved": return "bg-green-100 text-green-800";
      case "ongoing": return "bg-purple-100 text-purple-800";
      case "completed": return "bg-green-100 text-green-800";
      case "cancelled": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoadingData) {
    return (
      <AdminLayout
        title="Exam Management"
        description="Create and manage examination schedules and hall allocations"
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading exam data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Exam Management"
      description="Create and manage examination schedules and hall allocations"
    >
      {viewMode === 'add-exam' ? (
        <AddExamForm
          onSubmit={handleAddExam}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : viewMode === 'bulk-exam' ? (
        <BulkExamForm
          onSubmit={handleBulkExamSubmit}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : viewMode === 'excel-import' ? (
        <ExcelImportForm
          onImport={handleExcelImport}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : viewMode === 'bulk-allocation' ? (
        <BulkHallAllocation
          exams={selectedExams}
          onComplete={handleAllocationComplete}
          onCancel={handleCloseForm}
        />
      ) : viewMode === 'view-exam' && selectedExam ? (
        <div>
          {/* TODO: Create ExamDetailView component */}
          <Card>
            <CardHeader>
              <CardTitle>Exam Details</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Exam detail view coming soon...</p>
              <Button onClick={handleCloseForm}>Back to List</Button>
            </CardContent>
          </Card>
        </div>
      ) : viewMode === 'allocate-halls' && selectedExam ? (
        <HallAllocationInterface
          exam={selectedExam}
          onBack={handleCloseForm}
          onAllocationComplete={handleAllocationComplete}
        />
      ) : (
        <>
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <TypographyH2 className="flex items-center gap-2">
                <Calendar className="h-6 w-6" />
                Exam Management
              </TypographyH2>
              <TypographyP className="text-muted-foreground">
                Create and manage examination schedules and hall allocations
              </TypographyP>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => setViewMode('add-exam')}>
                <Plus className="mr-2 h-4 w-4" />
                Schedule Exam
              </Button>
              <Button onClick={() => setViewMode('bulk-exam')} variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                Bulk Create
              </Button>
              <Button onClick={() => setViewMode('excel-import')} variant="outline">
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Import Excel
              </Button>
              <Button
                onClick={handleBulkAllocation}
                variant="outline"
                disabled={exams.filter(e => e.status === 'scheduled' && !e.isAllocated).length === 0}
              >
                <Building className="mr-2 h-4 w-4" />
                Bulk Allocate ({exams.filter(e => e.status === 'scheduled' && !e.isAllocated).length})
              </Button>
            </div>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-6">
              <div className="flex items-center gap-2 text-red-800">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Error</span>
              </div>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          )}

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{exams.length}</div>
                <p className="text-xs text-muted-foreground">All scheduled exams</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {exams.filter(e => e.status === 'scheduled').length}
                </div>
                <p className="text-xs text-muted-foreground">Ready for allocation</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Allocated</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {exams.filter(e => e.isAllocated).length}
                </div>
                <p className="text-xs text-muted-foreground">Halls assigned</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Approved</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {exams.filter(e => e.isApproved).length}
                </div>
                <p className="text-xs text-muted-foreground">Ready to conduct</p>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter Exams
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by course code, title, or department..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filter Dropdowns */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Session</label>
                  <Select value={sessionFilter} onValueChange={setSessionFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All sessions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sessions</SelectItem>
                      {sessions.map((session) => (
                        <SelectItem key={session.id} value={session.id}>
                          {session.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Semester</label>
                  <Select value={semesterFilter} onValueChange={setSemesterFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All semesters" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Semesters</SelectItem>
                      {semesters.map((semester) => (
                        <SelectItem key={semester.id} value={semester.id}>
                          {semester.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="allocated">Allocated</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="ongoing">Ongoing</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" onClick={clearFilters} className="w-full">
                    Clear Filters
                  </Button>
                </div>
              </div>

              {/* Filter Summary */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Showing {filteredExams.length} of {exams.length} exams</span>
                {(searchTerm || sessionFilter !== "all" || semesterFilter !== "all" || statusFilter !== "all") && (
                  <Badge variant="secondary">Filtered</Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Exams List */}
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Exams ({filteredExams.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredExams.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <TypographyP className="text-muted-foreground">
                    {exams.length === 0 ? "No exams scheduled yet." : "No exams match your filters."}
                  </TypographyP>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredExams.map((exam) => (
                    <div key={exam.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {exam.courseCode}
                          </Badge>
                          <span className="font-medium">{exam.courseTitle}</span>
                          <Badge className={getStatusColor(exam.status)}>
                            {getExamStatusLabel(exam.status)}
                          </Badge>
                          {exam.isAllocated && (
                            <Badge variant="secondary" className="text-xs">
                              <Building className="h-3 w-3 mr-1" />
                              Allocated
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>🏛️ {exam.departmentName}</span>
                          <span>📚 {exam.levelName}</span>
                          <span>📅 {exam.examDate.toLocaleDateString()}</span>
                          <span>🕐 {formatExamTime(exam.startTime, exam.endTime)}</span>
                          <span>🏢 {exam.selectedHalls.length} halls</span>
                          {exam.studentCount && (
                            <span>👥 {exam.studentCount} students</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {exam.status === 'scheduled' && !exam.isAllocated && (
                          <Button
                            size="sm"
                            onClick={() => handleAllocateHalls(exam)}
                          >
                            <Building className="h-3 w-3 mr-1" />
                            Allocate Halls
                          </Button>
                        )}
                        {exam.isAllocated && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAllocateHalls(exam)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View Allocation
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewExam(exam)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </AdminLayout>
  );
}
