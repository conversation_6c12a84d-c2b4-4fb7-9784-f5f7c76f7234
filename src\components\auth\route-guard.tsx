"use client";

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { SessionManager } from '@/lib/session-manager';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
}

export function RouteGuard({ children }: RouteGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    const checkAccess = () => {
      try {
        // Allow public routes and setup pages without session check
        const publicRoutes = ['/sign-in', '/setup/student', '/setup/staff', '/'];
        if (publicRoutes.some(route => pathname.startsWith(route))) {
          setIsAuthorized(true);
          setIsLoading(false);
          return;
        }

        // Check URL access permissions for protected routes
        const hasAccess = SessionManager.checkUrlAccess(pathname, router);
        setIsAuthorized(hasAccess);
      } catch (error) {
        console.error('Route guard error:', error);
        setIsAuthorized(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAccess();
  }, [pathname, router]);

  // Update last activity on route changes
  useEffect(() => {
    if (SessionManager.isAuthenticated()) {
      SessionManager.updateLastActivity();
    }
  }, [pathname]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-orange-50">
        <div className="text-center">
          <div className="bg-red-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <Loader2 className="h-10 w-10 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">Redirecting to appropriate page...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
