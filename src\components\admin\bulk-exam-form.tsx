"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Plus, 
  Trash2, 
  Upload, 
  Download, 
  Loader2, 
  AlertCircle,
  FileSpreadsheet,
  Calendar,
  Copy
} from "lucide-react";
import { 
  ExamFormData, 
  AcademicSession, 
  Semester, 
  Course
} from "@/types/exam";
import { Faculty, Department, Level } from "@/types/department";
import { Hall } from "@/types/hall";
import { 
  getAllAcademicSessions,
  getSemestersBySession,
  getCoursesByDepartment,
  ensureSemestersExist
} from "@/lib/firebase/exam-service";
import { 
  getAllFaculties, 
  getDepartmentsByFaculty, 
  getLevelsByDepartment
} from "@/lib/firebase/department-service";
import { getAllHalls } from "@/lib/firebase/hall-service";

interface BulkExamData extends Omit<ExamFormData, 'courseId'> {
  id: string;
  courseCode: string;
  courseTitle: string;
  isElective: boolean;
}

interface BulkExamFormProps {
  onSubmit: (exams: BulkExamData[]) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function BulkExamForm({ onSubmit, onCancel, isLoading = false }: BulkExamFormProps) {
  const [sessions, setSessions] = useState<AcademicSession[]>([]);
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [faculties, setFaculties] = useState<Faculty[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [levels, setLevels] = useState<Level[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [halls, setHalls] = useState<Hall[]>([]);

  const [commonData, setCommonData] = useState({
    sessionId: "",
    semesterId: "",
    facultyId: "",
    departmentId: "",
    levelId: "",
    examDate: new Date(),
    selectedHalls: [] as string[]
  });

  const [exams, setExams] = useState<BulkExamData[]>([
    {
      id: crypto.randomUUID(),
      courseCode: "",
      courseTitle: "",
      isElective: false,
      sessionId: "",
      semesterId: "",
      facultyId: "",
      departmentId: "",
      levelId: "",
      examDate: new Date(),
      startTime: "",
      endTime: "",
      selectedHalls: [],
      hallCapacityOverride: {}
    }
  ]);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load dependent data when common selections change
  useEffect(() => {
    if (commonData.sessionId) {
      loadSemesters(commonData.sessionId);
    }
  }, [commonData.sessionId]);

  useEffect(() => {
    if (commonData.facultyId) {
      loadDepartments(commonData.facultyId);
    }
  }, [commonData.facultyId]);

  useEffect(() => {
    if (commonData.departmentId) {
      loadLevels(commonData.departmentId);
      loadCourses(commonData.departmentId);
    }
  }, [commonData.departmentId]);

  const loadInitialData = async () => {
    try {
      const [sessionsData, facultiesData, hallsData] = await Promise.all([
        getAllAcademicSessions(),
        getAllFaculties(),
        getAllHalls()
      ]);
      
      setSessions(sessionsData);
      setFaculties(facultiesData);
      setHalls(hallsData.filter(h => h.status === "active"));
    } catch (error) {
      console.error("Error loading initial data:", error);
    } finally {
      setIsLoadingData(false);
    }
  };

  const loadSemesters = async (sessionId: string) => {
    try {
      await ensureSemestersExist(sessionId, "admin-user-id");
      const semestersData = await getSemestersBySession(sessionId);
      setSemesters(semestersData);
    } catch (error) {
      console.error("Error loading semesters:", error);
    }
  };

  const loadDepartments = async (facultyId: string) => {
    try {
      const departmentsData = await getDepartmentsByFaculty(facultyId);
      setDepartments(departmentsData);
    } catch (error) {
      console.error("Error loading departments:", error);
    }
  };

  const loadLevels = async (departmentId: string) => {
    try {
      const levelsData = await getLevelsByDepartment(departmentId);
      setLevels(levelsData);
    } catch (error) {
      console.error("Error loading levels:", error);
    }
  };

  const loadCourses = async (departmentId: string) => {
    try {
      const coursesData = await getCoursesByDepartment(departmentId);
      setCourses(coursesData);
    } catch (error) {
      console.error("Error loading courses:", error);
    }
  };

  const addExam = () => {
    const newExam: BulkExamData = {
      id: crypto.randomUUID(),
      courseCode: "",
      courseTitle: "",
      isElective: false,
      sessionId: commonData.sessionId,
      semesterId: commonData.semesterId,
      facultyId: commonData.facultyId,
      departmentId: commonData.departmentId,
      levelId: commonData.levelId,
      examDate: commonData.examDate,
      startTime: "",
      endTime: "",
      selectedHalls: [...commonData.selectedHalls],
      hallCapacityOverride: {}
    };
    setExams(prev => [...prev, newExam]);
  };

  const removeExam = (id: string) => {
    setExams(prev => prev.filter(exam => exam.id !== id));
  };

  const updateExam = (id: string, field: keyof BulkExamData, value: any) => {
    setExams(prev => prev.map(exam => 
      exam.id === id ? { ...exam, [field]: value } : exam
    ));
  };

  const applyCommonData = () => {
    setExams(prev => prev.map(exam => ({
      ...exam,
      sessionId: commonData.sessionId,
      semesterId: commonData.semesterId,
      facultyId: commonData.facultyId,
      departmentId: commonData.departmentId,
      levelId: commonData.levelId,
      examDate: commonData.examDate,
      selectedHalls: [...commonData.selectedHalls]
    })));
  };

  const fillFromCourse = (examId: string, courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    if (course) {
      updateExam(examId, 'courseCode', course.code);
      updateExam(examId, 'courseTitle', course.title);
      updateExam(examId, 'isElective', course.isElective);
    }
  };

  const validateExams = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate common data
    if (!commonData.sessionId) newErrors.sessionId = "Please select a session";
    if (!commonData.semesterId) newErrors.semesterId = "Please select a semester";
    if (!commonData.facultyId) newErrors.facultyId = "Please select a faculty";
    if (!commonData.departmentId) newErrors.departmentId = "Please select a department";
    if (!commonData.levelId) newErrors.levelId = "Please select a level";
    if (commonData.selectedHalls.length === 0) newErrors.selectedHalls = "Please select at least one hall";

    // Validate individual exams
    exams.forEach((exam, index) => {
      if (!exam.courseCode.trim()) newErrors[`exam_${index}_courseCode`] = "Course code is required";
      if (!exam.courseTitle.trim()) newErrors[`exam_${index}_courseTitle`] = "Course title is required";
      if (!exam.startTime) newErrors[`exam_${index}_startTime`] = "Start time is required";
      if (!exam.endTime) newErrors[`exam_${index}_endTime`] = "End time is required";
      
      if (exam.startTime && exam.endTime) {
        const start = new Date(`2000-01-01 ${exam.startTime}`);
        const end = new Date(`2000-01-01 ${exam.endTime}`);
        if (end <= start) {
          newErrors[`exam_${index}_endTime`] = "End time must be after start time";
        }
      }
    });

    // Check for duplicate course codes
    const courseCodes = exams.map(e => e.courseCode.trim().toUpperCase()).filter(Boolean);
    const duplicates = courseCodes.filter((code, index) => courseCodes.indexOf(code) !== index);
    if (duplicates.length > 0) {
      newErrors.duplicateCourses = `Duplicate course codes found: ${[...new Set(duplicates)].join(', ')}`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateExams()) {
      return;
    }

    // Apply common data to all exams before submitting
    const finalExams = exams.map(exam => ({
      ...exam,
      sessionId: commonData.sessionId,
      semesterId: commonData.semesterId,
      facultyId: commonData.facultyId,
      departmentId: commonData.departmentId,
      levelId: commonData.levelId,
      examDate: commonData.examDate,
      selectedHalls: [...commonData.selectedHalls]
    }));

    await onSubmit(finalExams);
  };

  const selectedSession = sessions.find(s => s.id === commonData.sessionId);
  const selectedSemester = semesters.find(s => s.id === commonData.semesterId);
  const selectedFaculty = faculties.find(f => f.id === commonData.facultyId);
  const selectedDepartment = departments.find(d => d.id === commonData.departmentId);
  const selectedLevel = levels.find(l => l.id === commonData.levelId);

  if (isLoadingData) {
    return (
      <Card className="w-full max-w-6xl mx-auto">
        <CardContent className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading bulk exam form...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Bulk Exam Creation
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Create multiple exams quickly with shared settings
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Common Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Common Settings</CardTitle>
              <p className="text-sm text-muted-foreground">
                These settings will be applied to all exams
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Session and Semester */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Academic Session *</Label>
                  <Select
                    value={commonData.sessionId}
                    onValueChange={(value) => setCommonData(prev => ({ ...prev, sessionId: value, semesterId: "" }))}
                  >
                    <SelectTrigger className={errors.sessionId ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select session" />
                    </SelectTrigger>
                    <SelectContent>
                      {sessions.map((session) => (
                        <SelectItem key={session.id} value={session.id}>
                          {session.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.sessionId && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.sessionId}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Semester *</Label>
                  <Select
                    value={commonData.semesterId}
                    onValueChange={(value) => setCommonData(prev => ({ ...prev, semesterId: value }))}
                    disabled={!commonData.sessionId}
                  >
                    <SelectTrigger className={errors.semesterId ? "border-red-500" : ""}>
                      <SelectValue placeholder={!commonData.sessionId ? "Select session first" : "Select semester"} />
                    </SelectTrigger>
                    <SelectContent>
                      {semesters.map((semester) => (
                        <SelectItem key={semester.id} value={semester.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {semester.code}
                            </Badge>
                            <span>{semester.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.semesterId && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.semesterId}
                    </p>
                  )}
                </div>
              </div>

              {/* Academic Structure */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Faculty *</Label>
                  <Select
                    value={commonData.facultyId}
                    onValueChange={(value) => setCommonData(prev => ({
                      ...prev,
                      facultyId: value,
                      departmentId: "",
                      levelId: ""
                    }))}
                  >
                    <SelectTrigger className={errors.facultyId ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select faculty" />
                    </SelectTrigger>
                    <SelectContent>
                      {faculties.map((faculty) => (
                        <SelectItem key={faculty.id} value={faculty.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {faculty.code}
                            </Badge>
                            <span>{faculty.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.facultyId && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.facultyId}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Department *</Label>
                  <Select
                    value={commonData.departmentId}
                    onValueChange={(value) => setCommonData(prev => ({ ...prev, departmentId: value, levelId: "" }))}
                    disabled={!commonData.facultyId}
                  >
                    <SelectTrigger className={errors.departmentId ? "border-red-500" : ""}>
                      <SelectValue placeholder={!commonData.facultyId ? "Select faculty first" : "Select department"} />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((department) => (
                        <SelectItem key={department.id} value={department.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {department.code}
                            </Badge>
                            <span>{department.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.departmentId && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.departmentId}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Level *</Label>
                  <Select
                    value={commonData.levelId}
                    onValueChange={(value) => setCommonData(prev => ({ ...prev, levelId: value }))}
                    disabled={!commonData.departmentId}
                  >
                    <SelectTrigger className={errors.levelId ? "border-red-500" : ""}>
                      <SelectValue placeholder={!commonData.departmentId ? "Select department first" : "Select level"} />
                    </SelectTrigger>
                    <SelectContent>
                      {levels.map((level) => (
                        <SelectItem key={level.id} value={level.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {level.code}
                            </Badge>
                            <span>{level.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.levelId && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.levelId}
                    </p>
                  )}
                </div>
              </div>

              {/* Exam Date */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="examDate">Exam Date *</Label>
                  <Input
                    id="examDate"
                    type="date"
                    value={commonData.examDate.toISOString().split('T')[0]}
                    onChange={(e) => setCommonData(prev => ({ ...prev, examDate: new Date(e.target.value) }))}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={applyCommonData}
                    className="w-full"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Apply to All Exams
                  </Button>
                </div>
              </div>

              {/* Hall Selection */}
              <div className="space-y-2">
                <Label>Available Halls *</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border rounded-lg p-4">
                  {halls.map((hall) => (
                    <div key={hall.id} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id={`hall-${hall.id}`}
                        checked={commonData.selectedHalls.includes(hall.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setCommonData(prev => ({
                              ...prev,
                              selectedHalls: [...prev.selectedHalls, hall.id]
                            }));
                          } else {
                            setCommonData(prev => ({
                              ...prev,
                              selectedHalls: prev.selectedHalls.filter(id => id !== hall.id)
                            }));
                          }
                        }}
                        className="rounded border-gray-300"
                        aria-label={`Select hall ${hall.code} - ${hall.name}`}
                      />
                      <Label htmlFor={`hall-${hall.id}`} className="flex-1 cursor-pointer">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {hall.code}
                          </Badge>
                          <span className="text-sm">{hall.name}</span>
                          <span className="text-xs text-muted-foreground">({hall.capacity})</span>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
                {errors.selectedHalls && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.selectedHalls}
                  </p>
                )}
                {commonData.selectedHalls.length > 0 && (
                  <p className="text-sm text-muted-foreground">
                    Selected {commonData.selectedHalls.length} hall{commonData.selectedHalls.length !== 1 ? 's' : ''}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Individual Exams */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Individual Exams ({exams.length})</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Configure each exam's specific details
                  </p>
                </div>
                <Button type="button" onClick={addExam} variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Exam
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {errors.duplicateCourses && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.duplicateCourses}
                  </p>
                </div>
              )}

              {exams.map((exam, index) => (
                <div key={exam.id} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Exam {index + 1}</h4>
                    {exams.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeExam(exam.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Course Selection or Manual Entry */}
                    <div className="space-y-2">
                      <Label>Select Existing Course</Label>
                      <Select
                        value=""
                        onValueChange={(value) => fillFromCourse(exam.id, value)}
                        disabled={!commonData.departmentId}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={
                            !commonData.departmentId ? "Select department first" :
                            courses.length === 0 ? "No courses available" :
                            "Select course to auto-fill"
                          } />
                        </SelectTrigger>
                        <SelectContent>
                          {courses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="font-mono text-xs">
                                  {course.code}
                                </Badge>
                                <span>{course.title}</span>
                                {course.isElective && (
                                  <Badge variant="secondary" className="text-xs">
                                    Elective
                                  </Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2 pt-6">
                      <input
                        type="checkbox"
                        id={`elective-${exam.id}`}
                        checked={exam.isElective}
                        onChange={(e) => updateExam(exam.id, 'isElective', e.target.checked)}
                        className="rounded border-gray-300"
                        aria-label="Mark as elective course"
                      />
                      <Label htmlFor={`elective-${exam.id}`} className="text-sm">
                        Elective Course
                      </Label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Course Code *</Label>
                      <Input
                        placeholder="e.g., CSC101"
                        value={exam.courseCode}
                        onChange={(e) => updateExam(exam.id, 'courseCode', e.target.value.toUpperCase())}
                        className={errors[`exam_${index}_courseCode`] ? "border-red-500" : ""}
                      />
                      {errors[`exam_${index}_courseCode`] && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors[`exam_${index}_courseCode`]}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>Course Title *</Label>
                      <Input
                        placeholder="e.g., Introduction to Computer Science"
                        value={exam.courseTitle}
                        onChange={(e) => updateExam(exam.id, 'courseTitle', e.target.value)}
                        className={errors[`exam_${index}_courseTitle`] ? "border-red-500" : ""}
                      />
                      {errors[`exam_${index}_courseTitle`] && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors[`exam_${index}_courseTitle`]}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Start Time *</Label>
                      <Input
                        type="time"
                        value={exam.startTime}
                        onChange={(e) => updateExam(exam.id, 'startTime', e.target.value)}
                        className={errors[`exam_${index}_startTime`] ? "border-red-500" : ""}
                      />
                      {errors[`exam_${index}_startTime`] && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors[`exam_${index}_startTime`]}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>End Time *</Label>
                      <Input
                        type="time"
                        value={exam.endTime}
                        onChange={(e) => updateExam(exam.id, 'endTime', e.target.value)}
                        className={errors[`exam_${index}_endTime`] ? "border-red-500" : ""}
                      />
                      {errors[`exam_${index}_endTime`] && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors[`exam_${index}_endTime`]}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Preview */}
          {selectedSession && selectedSemester && selectedFaculty && selectedDepartment && selectedLevel && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Bulk Creation Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <p><strong>Session:</strong> {selectedSession.name} - {selectedSemester.name}</p>
                  <p><strong>Department:</strong> {selectedDepartment.name} ({selectedFaculty.code})</p>
                  <p><strong>Level:</strong> {selectedLevel.name}</p>
                  <p><strong>Date:</strong> {commonData.examDate.toLocaleDateString()}</p>
                  <p><strong>Halls:</strong> {commonData.selectedHalls.length} selected</p>
                  <p><strong>Exams to create:</strong> {exams.filter(e => e.courseCode.trim() && e.courseTitle.trim()).length}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating {exams.length} Exams...
                </>
              ) : (
                <>
                  <Calendar className="mr-2 h-4 w-4" />
                  Create {exams.length} Exams
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
