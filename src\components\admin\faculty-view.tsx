"use client";

import { Faculty, Department } from "@/types/department";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyH3, TypographyP } from "@/components/ui/typography";
import { ArrowLeft, Building2, Eye, Users, Key } from "lucide-react";

interface FacultyViewProps {
  faculty: Faculty;
  departments: Department[];
  onBack: () => void;
  onViewDepartment: (department: Department) => void;
}

export function FacultyView({ faculty, departments, onBack, onViewDepartment }: FacultyViewProps) {
  const activeDepartments = departments.filter(d => d.hasPortalAccess);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Overview
        </Button>
        <div>
          <TypographyH3 className="flex items-center gap-2">
            <Building2 className="h-6 w-6" />
            {faculty.name}
          </TypographyH3>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="font-mono">
              {faculty.code}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {departments.length} departments
            </span>
          </div>
        </div>
      </div>

      {/* Faculty Description */}
      {faculty.description && (
        <Card>
          <CardContent className="pt-6">
            <TypographyP className="text-muted-foreground">
              {faculty.description}
            </TypographyP>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Departments</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.length}</div>
            <p className="text-xs text-muted-foreground">Under this faculty</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Portal Access</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeDepartments.length}</div>
            <p className="text-xs text-muted-foreground">With portal access</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Created</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{faculty.createdAt.toLocaleDateString()}</div>
            <p className="text-xs text-muted-foreground">Faculty established</p>
          </CardContent>
        </Card>
      </div>

      {/* Departments List */}
      <Card>
        <CardHeader>
          <CardTitle>Departments ({departments.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {departments.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <TypographyP className="text-muted-foreground">
                No departments found under this faculty.
              </TypographyP>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {departments.map((department) => (
                <Card key={department.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="font-mono text-xs">
                            {department.code}
                          </Badge>
                          {department.hasPortalAccess && (
                            <Badge variant="secondary" className="text-xs">
                              <Key className="h-3 w-3 mr-1" />
                              Portal
                            </Badge>
                          )}
                        </div>
                        <h4 className="font-medium">{department.name}</h4>
                        {department.description && (
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {department.description}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Created: {department.createdAt.toLocaleDateString()}
                      </div>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => onViewDepartment(department)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View Students
                      </Button>
                    </div>

                    {department.hasPortalAccess && department.accessId && (
                      <div className="mt-3 p-2 bg-muted rounded text-xs">
                        <span className="text-muted-foreground">Access ID: </span>
                        <code className="font-mono">{department.accessId}</code>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
