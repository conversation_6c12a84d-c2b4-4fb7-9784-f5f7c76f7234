"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyH1, TypographyH2, TypographyP } from "@/components/ui/typography";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Calendar, 
  Building, 
  Clock, 
  Users, 
  Shield,
  LogOut,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  Info,
  Bell,
  UserCheck,
  ClipboardList,
  MapPin
} from "lucide-react";

interface StaffInfo {
  id: string;
  name: string;
  role: 'supervisor' | 'invigilator';
  department: string;
  email: string;
}

interface AssignedExam {
  id: string;
  courseCode: string;
  courseTitle: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  duration: number;
  hallName: string;
  hallLocation: string;
  studentsCount: number;
  status: 'upcoming' | 'ongoing' | 'completed';
  role: 'supervisor' | 'invigilator';
}

export default function StaffDashboard() {
  const [staffInfo, setStaffInfo] = useState<StaffInfo | null>(null);
  const [assignedExams, setAssignedExams] = useState<AssignedExam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const router = useRouter();

  useEffect(() => {
    loadStaffData();
  }, []);

  const loadStaffData = async () => {
    setIsLoading(true);
    try {
      // Check if staff is logged in
      const session = localStorage.getItem('staff_session');
      if (!session) {
        router.push('/sign-in');
        return;
      }

      const sessionData = JSON.parse(session);
      console.log('📋 Staff session data:', sessionData);

      // Get real staff data from Firestore
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      const systemUsersRef = collection(db, 'system_users');
      const staffQuery = query(systemUsersRef, where('userId', '==', sessionData.userId));
      const staffSnapshot = await getDocs(staffQuery);

      if (!staffSnapshot.empty) {
        const staffData = staffSnapshot.docs[0].data();
        console.log('✅ Staff data loaded:', staffData);
        setStaffInfo({
          id: staffData.userId,
          name: staffData.name,
          role: staffData.role,
          department: staffData.department,
          email: staffData.email || sessionData.email
        });
      } else {
        // Fallback data if staff not found
        setStaffInfo({
          id: sessionData.userId || "Unknown",
          name: sessionData.name || "Staff Member",
          role: sessionData.role || "supervisor",
          department: "Unknown",
          email: sessionData.email || "Unknown"
        });
      }

      // Get real assigned exams from Firestore
      try {
        // Get staff assignments from hall_allocations where this staff member is assigned
        const hallAllocationsRef = collection(db, 'hall_allocations');
        const assignmentsQuery = query(
          hallAllocationsRef,
          where('supervisorId', '==', sessionData.userId)
        );
        const assignmentsSnapshot = await getDocs(assignmentsQuery);

        const examAssignments: AssignedExam[] = [];

        for (const assignmentDoc of assignmentsSnapshot.docs) {
          const assignmentData = assignmentDoc.data();

          // Get exam details from exam_timetable
          const examTimetableRef = collection(db, 'exam_timetable');
          const examQuery = query(examTimetableRef, where('__name__', '==', assignmentData.examId));
          const examSnapshot = await getDocs(examQuery);

          if (!examSnapshot.empty) {
            const examData = examSnapshot.docs[0].data();

            // Get hall details
            const hallsRef = collection(db, 'halls');
            const hallQuery = query(hallsRef, where('name', '==', assignmentData.hallName));
            const hallSnapshot = await getDocs(hallQuery);

            let hallLocation = 'Location TBA';
            if (!hallSnapshot.empty) {
              const hallData = hallSnapshot.docs[0].data();
              hallLocation = hallData.location || 'Location TBA';
            }

            // Determine status based on exam date
            const examDate = examData.examDate.toDate();
            const now = new Date();
            let status: 'upcoming' | 'ongoing' | 'completed' = 'upcoming';

            if (examDate < now) {
              status = 'completed';
            } else if (examDate.toDateString() === now.toDateString()) {
              status = 'ongoing';
            }

            examAssignments.push({
              id: assignmentDoc.id,
              courseCode: examData.courseCode,
              courseTitle: examData.courseTitle,
              examDate: examDate,
              startTime: examData.startTime,
              endTime: examData.endTime,
              duration: examData.duration || 180,
              hallName: assignmentData.hallName,
              hallLocation: hallLocation,
              studentsCount: assignmentData.studentsCount || 0,
              status: status,
              role: assignmentData.role || 'supervisor'
            });
          }
        }

        setAssignedExams(examAssignments);
        console.log(`✅ Loaded ${examAssignments.length} exam assignments for staff`);
      } catch (examError) {
        console.error('Error loading exam assignments:', examError);
        // Fallback to empty array
        setAssignedExams([]);
      }

    } catch (error) {
      setError("Failed to load staff data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('staff_session');
    router.push('/sign-in');
  };

  const getStatusBadge = (status: AssignedExam['status']) => {
    switch (status) {
      case 'upcoming':
        return <Badge className="bg-blue-600">Upcoming</Badge>;
      case 'ongoing':
        return <Badge className="bg-green-600">Ongoing</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getRoleBadge = (role: AssignedExam['role']) => {
    return role === 'supervisor' 
      ? <Badge variant="outline" className="text-purple-600 border-purple-600">Supervisor</Badge>
      : <Badge variant="outline" className="text-orange-600 border-orange-600">Invigilator</Badge>;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    return new Date(`2024-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const upcomingExams = assignedExams.filter(exam => exam.status === 'upcoming');
  const completedExams = assignedExams.filter(exam => exam.status === 'completed');
  const supervisorRoles = assignedExams.filter(exam => exam.role === 'supervisor').length;
  const invigilatorRoles = assignedExams.filter(exam => exam.role === 'invigilator').length;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-purple-600 mx-auto mb-4" />
          <TypographyP className="text-gray-600">Loading your dashboard...</TypographyP>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-purple-600 p-2 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hall Automata</h1>
                <p className="text-sm text-gray-600">Staff Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="font-mono hidden sm:inline-flex">
                🎓 MAPOLY
              </Badge>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Welcome Section */}
        {staffInfo && (
          <div className="mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <TypographyH1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                    Welcome back, {staffInfo.name}!
                  </TypographyH1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Shield className="h-4 w-4" />
                      {staffInfo.id}
                    </div>
                    <div className="flex items-center gap-1">
                      <UserCheck className="h-4 w-4" />
                      {staffInfo.role.charAt(0).toUpperCase() + staffInfo.role.slice(1)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Building className="h-4 w-4" />
                      {staffInfo.department}
                    </div>
                  </div>
                </div>
                <Button variant="outline" onClick={loadStaffData} className="mt-4 sm:mt-0">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{assignedExams.length}</div>
              <p className="text-xs text-muted-foreground">This semester</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{upcomingExams.length}</div>
              <p className="text-xs text-muted-foreground">Exams scheduled</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">As Supervisor</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{supervisorRoles}</div>
              <p className="text-xs text-muted-foreground">Primary role</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">As Invigilator</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{invigilatorRoles}</div>
              <p className="text-xs text-muted-foreground">Support role</p>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Assignments */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <TypographyH2 className="text-xl font-bold text-gray-900">
              Upcoming Assignments
            </TypographyH2>
            {upcomingExams.length > 0 && (
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                {upcomingExams.length} scheduled
              </Badge>
            )}
          </div>

          {upcomingExams.length === 0 ? (
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <TypographyP className="text-gray-600 mb-2">No upcoming assignments</TypographyP>
                <TypographyP className="text-sm text-gray-500">
                  Your exam supervision schedule will appear here when available
                </TypographyP>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {upcomingExams.map((exam) => (
                <Card key={exam.id} className="bg-white/80 backdrop-blur-sm hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg font-bold text-gray-900">
                          {exam.courseCode}
                        </CardTitle>
                        <TypographyP className="text-gray-600 mt-1">
                          {exam.courseTitle}
                        </TypographyP>
                      </div>
                      <div className="flex flex-col gap-2">
                        {getStatusBadge(exam.status)}
                        {getRoleBadge(exam.role)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{formatDate(exam.examDate)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-4 w-4 text-green-600" />
                        <span>{formatTime(exam.startTime)} - {formatTime(exam.endTime)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Building className="h-4 w-4 text-purple-600" />
                        <span className="font-medium">{exam.hallName}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-4 w-4 text-orange-600" />
                        <span>{exam.studentsCount} students</span>
                      </div>
                    </div>
                    
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        <strong>Location:</strong> {exam.hallLocation}
                        <br />
                        <strong>Duration:</strong> {exam.duration} minutes
                        <br />
                        <strong>Role:</strong> You are assigned as the {exam.role} for this exam.
                      </AlertDescription>
                    </Alert>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
