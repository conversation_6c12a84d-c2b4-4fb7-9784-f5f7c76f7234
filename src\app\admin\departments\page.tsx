"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { AddFacultyForm } from "@/components/admin/add-faculty-form";
import { AddDepartmentForm } from "@/components/admin/add-department-form";
import { AddStudentForm } from "@/components/admin/add-student-form";
import { FacultyView } from "@/components/admin/faculty-view";
import { DepartmentView } from "@/components/admin/department-view";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyP, TypographyH3 } from "@/components/ui/typography";
import {
  Building,
  Plus,
  Key,
  Users,
  Settings,
  Building2,
  Loader2,
  AlertCircle,
  GraduationCap,
  Eye
} from "lucide-react";
import {
  Faculty,
  Department,
  Student,
  FacultyFormData,
  DepartmentFormData,
  StudentFormData,
  LevelPresetType
} from "@/types/department";
import {
  addFaculty,
  getAllFaculties,
  addDepartment,
  getAllDepartments,
  createLevelsForDepartment,
  addStudent,
  getAllStudents
} from "@/lib/firebase/department-service";

type ViewMode = 'list' | 'add-faculty' | 'add-department' | 'add-student' | 'view-faculty' | 'view-department';

export default function DepartmentAccessPage() {
  const [faculties, setFaculties] = useState<Faculty[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredDepartments, setFilteredDepartments] = useState<Department[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [selectedFaculty, setSelectedFaculty] = useState<Faculty | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<string>("");

  // Filter states
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [modeFilter, setModeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoadingData(true);
      setError("");
      const [facultiesData, departmentsData, studentsData] = await Promise.all([
        getAllFaculties(),
        getAllDepartments(),
        getAllStudents()
      ]);
      setFaculties(facultiesData);
      setDepartments(departmentsData);
      setStudents(studentsData);
      console.log(`✅ Loaded ${facultiesData.length} faculties, ${departmentsData.length} departments, and ${studentsData.length} students`);
    } catch (error) {
      console.error("❌ Error loading data:", error);
      setError(error instanceof Error ? error.message : "Failed to load data");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleAddFaculty = async (facultyData: FacultyFormData) => {
    setIsLoading(true);
    setError("");

    try {
      const createdBy = "admin"; // TODO: Get actual admin ID
      const newFaculty = await addFaculty(facultyData, createdBy);
      setFaculties(prev => [newFaculty, ...prev]);
      setViewMode('list');
      console.log("✅ Faculty added successfully:", newFaculty);
    } catch (error) {
      console.error("❌ Error adding faculty:", error);
      setError(error instanceof Error ? error.message : "Failed to add faculty");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddDepartment = async (
    departmentData: DepartmentFormData,
    levelPreset: LevelPresetType
  ) => {
    setIsLoading(true);
    setError("");

    try {
      const createdBy = "admin"; // TODO: Get actual admin ID
      const newDepartment = await addDepartment(departmentData, createdBy);

      // Create levels for the department
      await createLevelsForDepartment(newDepartment.id, levelPreset);

      setDepartments(prev => [newDepartment, ...prev]);
      setViewMode('list');
      console.log("✅ Department added successfully:", newDepartment);
    } catch (error) {
      console.error("❌ Error adding department:", error);
      setError(error instanceof Error ? error.message : "Failed to add department");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddStudent = async (studentData: StudentFormData, selectedCourses: string[]) => {
    setIsLoading(true);
    setError("");

    try {
      const createdBy = "admin"; // TODO: Get actual admin ID
      const newStudent = await addStudent(studentData, createdBy);

      // TODO: Register student for selected courses
      // This will be implemented when we add student course registration
      console.log('Selected courses for student:', selectedCourses);

      setStudents(prev => [newStudent, ...prev]);
      setViewMode('list');
      console.log("✅ Student added successfully:", newStudent);
    } catch (error) {
      console.error("❌ Error adding student:", error);
      setError(error instanceof Error ? error.message : "Failed to add student");
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewFaculty = (faculty: Faculty) => {
    setSelectedFaculty(faculty);
    const facultyDepartments = departments.filter(d => d.facultyId === faculty.id);
    setFilteredDepartments(facultyDepartments);
    setViewMode('view-faculty');
  };

  const handleViewDepartment = (department: Department) => {
    setSelectedDepartment(department);
    const departmentStudents = students.filter(s => s.departmentId === department.id);
    setFilteredStudents(departmentStudents);
    applyStudentFilters(departmentStudents);
    setViewMode('view-department');
  };

  const applyStudentFilters = (studentList: Student[]) => {
    let filtered = [...studentList];

    if (levelFilter !== 'all') {
      filtered = filtered.filter(s => s.levelId === levelFilter);
    }

    if (modeFilter !== 'all') {
      filtered = filtered.filter(s => s.studyMode === modeFilter);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(s => s.status === statusFilter);
    }

    setFilteredStudents(filtered);
  };

  const handleCloseForm = () => {
    setViewMode('list');
    setSelectedFaculty(null);
    setSelectedDepartment(null);
    setError("");
  };

  // Calculate statistics
  const totalStudents = students.length;
  const departmentsWithAccess = departments.filter(d => d.hasPortalAccess).length;

  return (
    <AdminLayout
      title="Department Management"
      description="Manage faculties, departments, and student registration portals"
      badge="Academic Structure"
    >
      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="font-medium">Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setError("")}
            className="mt-2"
          >
            Dismiss
          </Button>
        </div>
      )}

      {viewMode === 'add-faculty' ? (
        <AddFacultyForm
          onSubmit={handleAddFaculty}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : viewMode === 'add-department' ? (
        <AddDepartmentForm
          onSubmit={handleAddDepartment}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : viewMode === 'add-student' ? (
        <AddStudentForm
          onSubmit={handleAddStudent}
          onCancel={handleCloseForm}
          isLoading={isLoading}
        />
      ) : viewMode === 'view-faculty' && selectedFaculty ? (
        <FacultyView
          faculty={selectedFaculty}
          departments={filteredDepartments}
          onBack={handleCloseForm}
          onViewDepartment={handleViewDepartment}
        />
      ) : viewMode === 'view-department' && selectedDepartment ? (
        <DepartmentView
          department={selectedDepartment}
          students={filteredStudents}
          onBack={() => {
            if (selectedFaculty) {
              setViewMode('view-faculty');
            } else {
              handleCloseForm();
            }
          }}
        />
      ) : isLoadingData ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <TypographyP className="text-muted-foreground">Loading departments...</TypographyP>
          </div>
        </div>
      ) : (
        <>
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Button
              className="h-20 flex-col space-y-2"
              onClick={() => setViewMode('add-faculty')}
            >
              <Building className="h-6 w-6" />
              <span>Add Faculty</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2"
              onClick={() => setViewMode('add-department')}
            >
              <Building2 className="h-6 w-6" />
              <span>Add Department</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2"
              onClick={() => setViewMode('add-student')}
            >
              <GraduationCap className="h-6 w-6" />
              <span>Add Students</span>
            </Button>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Faculties</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{faculties.length}</div>
                <p className="text-xs text-muted-foreground">Academic faculties</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Departments</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{departments.length}</div>
                <p className="text-xs text-muted-foreground">Total departments</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Portal Access</CardTitle>
                <Key className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{departmentsWithAccess}</div>
                <p className="text-xs text-muted-foreground">Departments with access</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Students</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalStudents}</div>
                <p className="text-xs text-muted-foreground">Total students</p>
              </CardContent>
            </Card>
          </div>

          {/* Faculties and Departments */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Faculties */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Faculties ({faculties.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {faculties.length === 0 ? (
                  <div className="text-center py-8">
                    <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <TypographyP className="text-muted-foreground mb-4">
                      No faculties created yet.
                    </TypographyP>
                    <Button onClick={() => setViewMode('add-faculty')}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Faculty
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {faculties.slice(0, 5).map((faculty) => (
                      <div key={faculty.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {faculty.code}
                            </Badge>
                            <span className="font-medium">{faculty.name}</span>
                          </div>
                          {faculty.description && (
                            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                              {faculty.description}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="text-sm text-muted-foreground">
                            {departments.filter(d => d.facultyId === faculty.id).length} depts
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewFaculty(faculty)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                    {faculties.length > 5 && (
                      <div className="text-center text-sm text-muted-foreground">
                        +{faculties.length - 5} more faculties
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Departments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Departments ({departments.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {departments.length === 0 ? (
                  <div className="text-center py-8">
                    <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <TypographyP className="text-muted-foreground mb-4">
                      No departments created yet.
                    </TypographyP>
                    <Button onClick={() => setViewMode('add-department')}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Department
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {departments.slice(0, 5).map((department) => (
                      <div key={department.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-mono text-xs">
                              {department.facultyCode}-{department.code}
                            </Badge>
                            <span className="font-medium">{department.name}</span>
                            {department.hasPortalAccess && (
                              <Badge variant="secondary" className="text-xs">
                                <Key className="h-3 w-3 mr-1" />
                                Portal
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {department.facultyName}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="text-sm text-muted-foreground">
                            {students.filter(s => s.departmentId === department.id).length} students
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDepartment(department)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                    {departments.length > 5 && (
                      <div className="text-center text-sm text-muted-foreground">
                        +{departments.length - 5} more departments
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Action Bar */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <TypographyP className="text-muted-foreground">
                Manage your institution&apos;s academic structure and department access.
              </TypographyP>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={loadData}
                disabled={isLoadingData}
              >
                {isLoadingData ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Settings className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
            </div>
          </div>

          {/* Quick Setup Guide */}
          {faculties.length === 0 && (
            <Card className="mb-6 border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Building className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <TypographyH3 className="text-blue-900 mb-2">Get Started</TypographyH3>
                    <TypographyP className="text-blue-800 mb-4">
                      Set up your institution&apos;s academic structure by following these steps:
                    </TypographyP>
                    <div className="space-y-2 text-sm text-blue-700">
                      <div className="flex items-center gap-2">
                        <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">1</span>
                        <span>Create faculties (e.g., Engineering, Science)</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">2</span>
                        <span>Add departments under each faculty</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">3</span>
                        <span>Grant portal access to departments</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">4</span>
                        <span>Add students to department levels</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </AdminLayout>
  );
}
