"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Plus, Loader2, AlertCircle } from "lucide-react";
import { FacultyFormData } from "@/types/department";
import { isFacultyCodeUnique } from "@/lib/firebase/department-service";

interface AddFacultyFormProps {
  onSubmit: (data: FacultyFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function AddFacultyForm({ onSubmit, onCancel, isLoading = false }: AddFacultyFormProps) {
  const [formData, setFormData] = useState<FacultyFormData>({
    name: "",
    code: "",
    description: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidatingCode, setIsValidatingCode] = useState(false);

  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    // Validate required fields
    if (!formData.name.trim()) {
      newErrors.name = "Faculty name is required";
    }

    if (!formData.code.trim()) {
      newErrors.code = "Faculty code is required";
    } else if (formData.code.length < 2) {
      newErrors.code = "Faculty code must be at least 2 characters";
    } else {
      // Check if faculty code is unique
      setIsValidatingCode(true);
      const isUnique = await isFacultyCodeUnique(formData.code);
      setIsValidatingCode(false);
      
      if (!isUnique) {
        newErrors.code = "Faculty code already exists";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Add New Faculty
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Faculty Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Engineering, Science, Business"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={errors.name ? "border-red-500" : ""}
                required
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="code">Faculty Code *</Label>
              <Input
                id="code"
                placeholder="e.g., ENG, SCI, BUS"
                value={formData.code}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                className={`font-mono ${errors.code ? "border-red-500" : ""}`}
                maxLength={10}
                required
              />
              {errors.code && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.code}
                </p>
              )}
              {isValidatingCode && (
                <p className="text-sm text-blue-600 flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Checking availability...
                </p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Brief description of the faculty..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Examples */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Examples:</h4>
            <div className="space-y-1 text-sm text-muted-foreground">
              <p>• <strong>Engineering</strong> (Code: ENG) - Faculty of Engineering</p>
              <p>• <strong>Science</strong> (Code: SCI) - Faculty of Science and Technology</p>
              <p>• <strong>Business</strong> (Code: BUS) - Faculty of Business Administration</p>
              <p>• <strong>Arts</strong> (Code: ART) - Faculty of Arts and Humanities</p>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding Faculty...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Faculty
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
