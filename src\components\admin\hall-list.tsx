"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { TypographyP } from "@/components/ui/typography";
import { 
  MapPin, 
  Users, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  MoreVertical,
  Settings,
  Eye
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Hall, HallStatus, HallType } from "@/types/hall";

interface HallListProps {
  halls: Hall[];
  onEdit: (hall: Hall) => void;
  onDelete: (hall: Hall) => void;
  onView: (hall: Hall) => void;
}

const getStatusColor = (status: HallStatus) => {
  switch (status) {
    case "active": return "text-green-600 bg-green-50 border-green-200";
    case "maintenance": return "text-orange-600 bg-orange-50 border-orange-200";
    case "unavailable": return "text-red-600 bg-red-50 border-red-200";
    case "reserved": return "text-blue-600 bg-blue-50 border-blue-200";
    default: return "text-gray-600 bg-gray-50 border-gray-200";
  }
};

const getTypeIcon = (type: HallType) => {
  switch (type) {
    case "examination_hall": return "🏛️";
    case "lecture_hall": return "🎓";
    case "auditorium": return "🎭";
    case "classroom": return "📚";
    case "laboratory": return "🔬";
    case "computer_lab": return "💻";
    default: return "🏢";
  }
};

export function HallList({ halls, onEdit, onDelete, onView }: HallListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<HallStatus | "all">("all");

  const filteredHalls = halls.filter(hall => {
    const matchesSearch = 
      hall.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hall.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hall.location.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || hall.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (halls.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <TypographyP className="text-muted-foreground mb-4">
            No halls have been added yet.
          </TypographyP>
          <TypographyP className="text-sm text-muted-foreground">
            Add your first hall to start managing exam venues.
          </TypographyP>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search halls by name, code, or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as HallStatus | "all")}
          className="px-3 py-2 border border-border rounded-md text-sm"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="maintenance">Maintenance</option>
          <option value="unavailable">Unavailable</option>
          <option value="reserved">Reserved</option>
        </select>
      </div>

      {/* Results Count */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredHalls.length} of {halls.length} halls
      </div>

      {/* Hall Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredHalls.map((hall) => (
          <Card key={hall.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{getTypeIcon(hall.type)}</span>
                  <div>
                    <CardTitle className="text-lg">{hall.name}</CardTitle>
                    <Badge variant="outline" className="font-mono text-xs mt-1">
                      {hall.code}
                    </Badge>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onView(hall)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEdit(hall)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Hall
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDelete(hall)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Hall
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Status and Capacity */}
              <div className="flex items-center justify-between">
                <Badge className={getStatusColor(hall.status)}>
                  {hall.status.charAt(0).toUpperCase() + hall.status.slice(1)}
                </Badge>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Users className="h-4 w-4" />
                  <span className="font-mono">{hall.capacity}</span>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>{hall.location}</span>
              </div>

              {/* Type */}
              <div className="text-sm">
                <span className="text-muted-foreground">Type: </span>
                <span className="capitalize">
                  {hall.type.replace('_', ' ')}
                </span>
              </div>

              {/* Facilities */}
              {hall.facilities.length > 0 && (
                <div className="space-y-2">
                  <span className="text-sm text-muted-foreground">Facilities:</span>
                  <div className="flex flex-wrap gap-1">
                    {hall.facilities.slice(0, 3).map((facility) => (
                      <Badge key={facility} variant="outline" className="text-xs">
                        {facility}
                      </Badge>
                    ))}
                    {hall.facilities.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{hall.facilities.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Description */}
              {hall.description && (
                <TypographyP className="text-sm text-muted-foreground line-clamp-2">
                  {hall.description}
                </TypographyP>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button size="sm" variant="outline" onClick={() => onView(hall)} className="flex-1">
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Button>
                <Button size="sm" onClick={() => onEdit(hall)} className="flex-1">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* No Results */}
      {filteredHalls.length === 0 && halls.length > 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Search className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
            <TypographyP className="text-muted-foreground">
              No halls match your search criteria.
            </TypographyP>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
