"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar, Plus, Loader2, AlertCircle, Clock, Building } from "lucide-react";
import { 
  ExamFormData, 
  AcademicSession, 
  Semester, 
  Course,
  calculateExamDuration,
  formatExamTime
} from "@/types/exam";
import { Faculty, Department, Level } from "@/types/department";
import { Hall } from "@/types/hall";
import {
  getAllAcademicSessions,
  getSemestersBySession,
  getCoursesByDepartment,
  addCourse,
  isCourseCodeUnique,
  addAcademicSession,
  ensureSemestersExist
} from "@/lib/firebase/exam-service";
import { 
  getAllFaculties, 
  getDepartmentsByFaculty, 
  getLevelsByDepartment
} from "@/lib/firebase/department-service";
import { getAllHalls } from "@/lib/firebase/hall-service";

interface AddExamFormProps {
  onSubmit: (data: ExamFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function AddExamForm({ onSubmit, onCancel, isLoading = false }: AddExamFormProps) {
  const [formData, setFormData] = useState<ExamFormData>({
    courseId: "",
    sessionId: "",
    semesterId: "",
    facultyId: "",
    departmentId: "",
    levelId: "",
    examDate: new Date(),
    startTime: "",
    endTime: "",
    selectedHalls: [],
    hallCapacityOverride: {}
  });

  const [sessions, setSessions] = useState<AcademicSession[]>([]);
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [faculties, setFaculties] = useState<Faculty[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [levels, setLevels] = useState<Level[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [halls, setHalls] = useState<Hall[]>([]);
  const [availableHalls, setAvailableHalls] = useState<Hall[]>([]);

  // New session creation states
  const [showNewSessionForm, setShowNewSessionForm] = useState(false);
  const [newSessionData, setNewSessionData] = useState({
    name: "",
    startDate: new Date(),
    endDate: new Date()
  });

  // New course creation states
  const [showNewCourseForm, setShowNewCourseForm] = useState(false);
  const [newCourseData, setNewCourseData] = useState({
    code: "",
    title: "",
    isElective: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoadingSessions, setIsLoadingSessions] = useState(true);
  const [isLoadingSemesters, setIsLoadingSemesters] = useState(false);
  const [isLoadingFaculties, setIsLoadingFaculties] = useState(true);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isLoadingLevels, setIsLoadingLevels] = useState(false);
  const [isLoadingCourses, setIsLoadingCourses] = useState(false);
  const [isLoadingHalls, setIsLoadingHalls] = useState(true);
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [isCreatingCourse, setIsCreatingCourse] = useState(false);

  // Load initial data
  useEffect(() => {
    loadSessions();
    loadFaculties();
    loadHalls();
  }, []);

  // Load semesters when session changes
  useEffect(() => {
    if (formData.sessionId) {
      loadSemesters(formData.sessionId);
      setFormData(prev => ({ ...prev, semesterId: "" }));
    } else {
      setSemesters([]);
    }
  }, [formData.sessionId]);

  // Load departments when faculty changes
  useEffect(() => {
    if (formData.facultyId) {
      loadDepartments(formData.facultyId);
      setFormData(prev => ({ ...prev, departmentId: "", levelId: "", courseId: "" }));
      setLevels([]);
      setCourses([]);
    } else {
      setDepartments([]);
      setLevels([]);
      setCourses([]);
    }
  }, [formData.facultyId]);

  // Load levels and courses when department changes
  useEffect(() => {
    if (formData.departmentId) {
      loadLevels(formData.departmentId);
      loadCourses(formData.departmentId);
      setFormData(prev => ({ ...prev, levelId: "", courseId: "" }));
    } else {
      setLevels([]);
      setCourses([]);
    }
  }, [formData.departmentId]);

  // Filter available halls based on date and time
  useEffect(() => {
    if (formData.examDate && formData.startTime && formData.endTime) {
      filterAvailableHalls();
    } else {
      setAvailableHalls(halls);
    }
  }, [formData.examDate, formData.startTime, formData.endTime, halls]);

  const loadSessions = async () => {
    try {
      const sessionsData = await getAllAcademicSessions();
      setSessions(sessionsData);
    } catch (error) {
      console.error("Error loading sessions:", error);
    } finally {
      setIsLoadingSessions(false);
    }
  };

  const loadSemesters = async (sessionId: string) => {
    try {
      setIsLoadingSemesters(true);

      // Ensure semesters exist for this session (backward compatibility)
      await ensureSemestersExist(sessionId, "admin-user-id"); // TODO: Get actual user ID

      // Load the semesters
      const semestersData = await getSemestersBySession(sessionId);
      setSemesters(semestersData);
    } catch (error) {
      console.error("Error loading semesters:", error);
      setSemesters([]);
    } finally {
      setIsLoadingSemesters(false);
    }
  };

  const loadFaculties = async () => {
    try {
      const facultiesData = await getAllFaculties();
      setFaculties(facultiesData);
    } catch (error) {
      console.error("Error loading faculties:", error);
    } finally {
      setIsLoadingFaculties(false);
    }
  };

  const loadDepartments = async (facultyId: string) => {
    try {
      setIsLoadingDepartments(true);
      const departmentsData = await getDepartmentsByFaculty(facultyId);
      setDepartments(departmentsData);
    } catch (error) {
      console.error("Error loading departments:", error);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  const loadLevels = async (departmentId: string) => {
    try {
      setIsLoadingLevels(true);
      const levelsData = await getLevelsByDepartment(departmentId);
      setLevels(levelsData);
    } catch (error) {
      console.error("Error loading levels:", error);
    } finally {
      setIsLoadingLevels(false);
    }
  };

  const loadCourses = async (departmentId: string) => {
    try {
      setIsLoadingCourses(true);
      const coursesData = await getCoursesByDepartment(departmentId);
      setCourses(coursesData);
    } catch (error) {
      console.error("Error loading courses:", error);
    } finally {
      setIsLoadingCourses(false);
    }
  };

  const loadHalls = async () => {
    try {
      const hallsData = await getAllHalls();
      setHalls(hallsData.filter(h => h.status === "active"));
    } catch (error) {
      console.error("Error loading halls:", error);
    } finally {
      setIsLoadingHalls(false);
    }
  };

  const filterAvailableHalls = async () => {
    // TODO: Implement conflict checking with existing exams
    // For now, show all active halls
    setAvailableHalls(halls);
  };

  const handleCreateNewSession = async () => {
    try {
      setIsCreatingSession(true);
      setErrors({});

      // Validate new session data
      const newErrors: Record<string, string> = {};
      if (!newSessionData.name.trim()) newErrors.newSessionName = "Session name is required";

      // Validate date range
      if (newSessionData.endDate <= newSessionData.startDate) {
        newErrors.newSessionEndDate = "End date must be after start date";
      }

      // Check if session name already exists
      const existingSession = sessions.find(s =>
        s.name.toLowerCase() === newSessionData.name.trim().toLowerCase()
      );
      if (existingSession) {
        newErrors.newSessionName = "Session name already exists";
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        return;
      }

      // Create the new session
      const sessionFormData = {
        name: newSessionData.name.trim(),
        startDate: newSessionData.startDate,
        endDate: newSessionData.endDate
      };

      const newSession = await addAcademicSession(sessionFormData, "admin-user-id"); // TODO: Get actual user ID

      // Add the new session to the list and select it
      setSessions(prev => [newSession, ...prev].sort((a, b) => b.name.localeCompare(a.name)));
      setFormData(prev => ({ ...prev, sessionId: newSession.id, semesterId: "" }));

      // Load semesters for the new session (they were created automatically)
      await loadSemesters(newSession.id);

      // Reset new session form
      setNewSessionData({
        name: "",
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
      });
      setShowNewSessionForm(false);

      console.log('✅ New session created and selected with semesters:', newSession.name);
    } catch (error) {
      console.error("Error creating session:", error);
      setErrors({ newSessionGeneral: error instanceof Error ? error.message : "Failed to create session" });
    } finally {
      setIsCreatingSession(false);
    }
  };

  const handleCancelNewSession = () => {
    setNewSessionData({
      name: "",
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    });
    setShowNewSessionForm(false);
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.newSessionName;
      delete newErrors.newSessionStartDate;
      delete newErrors.newSessionEndDate;
      delete newErrors.newSessionGeneral;
      return newErrors;
    });
  };

  const handleCreateNewCourse = async () => {
    try {
      setIsCreatingCourse(true);
      setErrors({});

      // Validate new course data
      const newErrors: Record<string, string> = {};
      if (!newCourseData.code.trim()) newErrors.newCourseCode = "Course code is required";
      if (!newCourseData.title.trim()) newErrors.newCourseTitle = "Course title is required";
      if (!formData.departmentId) newErrors.departmentId = "Please select a department first";

      // Check if course code is unique within the department
      if (newCourseData.code.trim() && formData.departmentId) {
        const isUnique = await isCourseCodeUnique(newCourseData.code.trim(), formData.departmentId);
        if (!isUnique) {
          newErrors.newCourseCode = "Course code already exists in this department";
        }
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        return;
      }

      // Create the new course
      const courseFormData = {
        code: newCourseData.code.trim(),
        title: newCourseData.title.trim(),
        departmentId: formData.departmentId,
        isElective: newCourseData.isElective
      };

      const newCourse = await addCourse(courseFormData, "admin-user-id"); // TODO: Get actual user ID

      // Add the new course to the list and select it
      setCourses(prev => [newCourse, ...prev].sort((a, b) => a.code.localeCompare(b.code)));
      setFormData(prev => ({ ...prev, courseId: newCourse.id }));

      // Reset new course form
      setNewCourseData({ code: "", title: "", isElective: false });
      setShowNewCourseForm(false);

      console.log('✅ New course created and selected:', newCourse.code);
    } catch (error) {
      console.error("Error creating course:", error);
      setErrors({ newCourseGeneral: error instanceof Error ? error.message : "Failed to create course" });
    } finally {
      setIsCreatingCourse(false);
    }
  };

  const handleCancelNewCourse = () => {
    setNewCourseData({ code: "", title: "", isElective: false });
    setShowNewCourseForm(false);
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.newCourseCode;
      delete newErrors.newCourseTitle;
      delete newErrors.newCourseGeneral;
      return newErrors;
    });
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.sessionId) newErrors.sessionId = "Please select an academic session";
    if (!formData.semesterId) newErrors.semesterId = "Please select a semester";
    if (!formData.facultyId) newErrors.facultyId = "Please select a faculty";
    if (!formData.departmentId) newErrors.departmentId = "Please select a department";
    if (!formData.levelId) newErrors.levelId = "Please select a level";
    if (!formData.courseId) newErrors.courseId = "Please select a course";
    if (!formData.startTime) newErrors.startTime = "Please enter start time";
    if (!formData.endTime) newErrors.endTime = "Please enter end time";
    if (formData.selectedHalls.length === 0) newErrors.selectedHalls = "Please select at least one hall";

    // Validate time range
    if (formData.startTime && formData.endTime) {
      const start = new Date(`2000-01-01 ${formData.startTime}`);
      const end = new Date(`2000-01-01 ${formData.endTime}`);
      if (end <= start) {
        newErrors.endTime = "End time must be after start time";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = validateForm();
    if (!isValid) {
      return;
    }

    await onSubmit(formData);
  };

  const handleHallToggle = (hallId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedHalls: checked
        ? [...prev.selectedHalls, hallId]
        : prev.selectedHalls.filter(id => id !== hallId)
    }));
  };

  const handleCapacityOverride = (hallId: string, capacity: number) => {
    setFormData(prev => ({
      ...prev,
      hallCapacityOverride: {
        ...prev.hallCapacityOverride,
        [hallId]: capacity
      }
    }));
  };

  const selectedSession = sessions.find(s => s.id === formData.sessionId);
  const selectedSemester = semesters.find(s => s.id === formData.semesterId);
  const selectedFaculty = faculties.find(f => f.id === formData.facultyId);
  const selectedDepartment = departments.find(d => d.id === formData.departmentId);
  const selectedLevel = levels.find(l => l.id === formData.levelId);
  const selectedCourse = courses.find(c => c.id === formData.courseId);

  const examDuration = formData.startTime && formData.endTime 
    ? calculateExamDuration(formData.startTime, formData.endTime)
    : 0;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Schedule New Exam
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Academic Session & Semester */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Academic Session *</Label>
                {!showNewSessionForm && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowNewSessionForm(true)}
                    disabled={isLoadingSessions}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add New Session
                  </Button>
                )}
              </div>

              {showNewSessionForm ? (
                <Card className="p-4 border-2 border-dashed">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-sm">Create New Academic Session</h5>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleCancelNewSession}
                      >
                        ✕
                      </Button>
                    </div>

                    {errors.newSessionGeneral && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.newSessionGeneral}
                        </p>
                      </div>
                    )}

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="newSessionName">Session Name *</Label>
                        <Input
                          id="newSessionName"
                          placeholder="e.g., 2024/2025"
                          value={newSessionData.name}
                          onChange={(e) => setNewSessionData(prev => ({ ...prev, name: e.target.value }))}
                          className={errors.newSessionName ? "border-red-500" : ""}
                        />
                        {errors.newSessionName && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.newSessionName}
                          </p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="newSessionStartDate">Start Date *</Label>
                          <Input
                            id="newSessionStartDate"
                            type="date"
                            value={newSessionData.startDate.toISOString().split('T')[0]}
                            onChange={(e) => setNewSessionData(prev => ({
                              ...prev,
                              startDate: new Date(e.target.value)
                            }))}
                            className={errors.newSessionStartDate ? "border-red-500" : ""}
                          />
                          {errors.newSessionStartDate && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.newSessionStartDate}
                            </p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="newSessionEndDate">End Date *</Label>
                          <Input
                            id="newSessionEndDate"
                            type="date"
                            value={newSessionData.endDate.toISOString().split('T')[0]}
                            onChange={(e) => setNewSessionData(prev => ({
                              ...prev,
                              endDate: new Date(e.target.value)
                            }))}
                            className={errors.newSessionEndDate ? "border-red-500" : ""}
                          />
                          {errors.newSessionEndDate && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.newSessionEndDate}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          type="button"
                          onClick={handleCreateNewSession}
                          disabled={isCreatingSession}
                          size="sm"
                          className="flex-1"
                        >
                          {isCreatingSession ? (
                            <>
                              <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            <>
                              <Plus className="mr-2 h-3 w-3" />
                              Create Session
                            </>
                          )}
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleCancelNewSession}
                          size="sm"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ) : (
                <>
                  <Select
                    value={formData.sessionId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, sessionId: value }))}
                    disabled={isLoadingSessions}
                  >
                    <SelectTrigger className={errors.sessionId ? "border-red-500" : ""}>
                      <SelectValue placeholder={
                        isLoadingSessions ? "Loading..." :
                        sessions.length === 0 ? "No sessions found - click 'Add New Session'" :
                        "Select session"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {sessions.length === 0 && !isLoadingSessions ? (
                        <div className="p-2 text-sm text-muted-foreground text-center">
                          No academic sessions found
                        </div>
                      ) : (
                        sessions.map((session) => (
                          <SelectItem key={session.id} value={session.id}>
                            {session.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.sessionId && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.sessionId}
                    </p>
                  )}
                </>
              )}
            </div>

            <div className="space-y-2">
              <Label>Semester *</Label>
              <Select 
                value={formData.semesterId} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, semesterId: value }))}
                disabled={!formData.sessionId || isLoadingSemesters}
              >
                <SelectTrigger className={errors.semesterId ? "border-red-500" : ""}>
                  <SelectValue placeholder={
                    !formData.sessionId ? "Select session first" :
                    isLoadingSemesters ? "Loading semesters..." :
                    semesters.length === 0 ? "No semesters available" :
                    "Select semester"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {semesters.length === 0 && formData.sessionId && !isLoadingSemesters ? (
                    <div className="p-2 text-sm text-muted-foreground text-center">
                      No semesters found for this session
                    </div>
                  ) : (
                    semesters.map((semester) => (
                      <SelectItem key={semester.id} value={semester.id}>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {semester.code}
                          </Badge>
                          <span>{semester.name}</span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.semesterId && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.semesterId}
                </p>
              )}
            </div>
          </div>

          {/* Academic Structure */}
          <div className="space-y-4">
            <h4 className="font-medium">Academic Structure</h4>
            
            {/* Faculty Selection */}
            <div className="space-y-2">
              <Label>Faculty *</Label>
              <Select 
                value={formData.facultyId} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, facultyId: value }))}
                disabled={isLoadingFaculties}
              >
                <SelectTrigger className={errors.facultyId ? "border-red-500" : ""}>
                  <SelectValue placeholder={isLoadingFaculties ? "Loading..." : "Select faculty"} />
                </SelectTrigger>
                <SelectContent>
                  {faculties.map((faculty) => (
                    <SelectItem key={faculty.id} value={faculty.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          {faculty.code}
                        </Badge>
                        <span>{faculty.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.facultyId && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.facultyId}
                </p>
              )}
            </div>

            {/* Department Selection */}
            <div className="space-y-2">
              <Label>Department *</Label>
              <Select 
                value={formData.departmentId} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, departmentId: value }))}
                disabled={!formData.facultyId || isLoadingDepartments}
              >
                <SelectTrigger className={errors.departmentId ? "border-red-500" : ""}>
                  <SelectValue placeholder={
                    !formData.facultyId ? "Select faculty first" :
                    isLoadingDepartments ? "Loading..." : 
                    "Select department"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((department) => (
                    <SelectItem key={department.id} value={department.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          {department.code}
                        </Badge>
                        <span>{department.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.departmentId && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.departmentId}
                </p>
              )}
            </div>

            {/* Level and Course Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Level *</Label>
                <Select 
                  value={formData.levelId} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, levelId: value }))}
                  disabled={!formData.departmentId || isLoadingLevels}
                >
                  <SelectTrigger className={errors.levelId ? "border-red-500" : ""}>
                    <SelectValue placeholder={
                      !formData.departmentId ? "Select department first" :
                      isLoadingLevels ? "Loading..." : 
                      "Select level"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {levels.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {level.code}
                          </Badge>
                          <span>{level.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.levelId && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.levelId}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Course *</Label>
                  {formData.departmentId && !showNewCourseForm && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowNewCourseForm(true)}
                      disabled={isLoadingCourses}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add New Course
                    </Button>
                  )}
                </div>

                {showNewCourseForm ? (
                  <Card className="p-4 border-2 border-dashed">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium text-sm">Create New Course</h5>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={handleCancelNewCourse}
                        >
                          ✕
                        </Button>
                      </div>

                      {errors.newCourseGeneral && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.newCourseGeneral}
                          </p>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="newCourseCode">Course Code *</Label>
                          <Input
                            id="newCourseCode"
                            placeholder="e.g., CSC101"
                            value={newCourseData.code}
                            onChange={(e) => setNewCourseData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                            className={errors.newCourseCode ? "border-red-500" : ""}
                          />
                          {errors.newCourseCode && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.newCourseCode}
                            </p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="newCourseTitle">Course Title *</Label>
                          <Input
                            id="newCourseTitle"
                            placeholder="e.g., Introduction to Computer Science"
                            value={newCourseData.title}
                            onChange={(e) => setNewCourseData(prev => ({ ...prev, title: e.target.value }))}
                            className={errors.newCourseTitle ? "border-red-500" : ""}
                          />
                          {errors.newCourseTitle && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.newCourseTitle}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="newCourseElective"
                          checked={newCourseData.isElective}
                          onChange={(e) => setNewCourseData(prev => ({ ...prev, isElective: e.target.checked }))}
                          className="rounded border-gray-300"
                          aria-label="Mark course as elective"
                        />
                        <Label htmlFor="newCourseElective" className="text-sm">
                          This is an elective course
                        </Label>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          type="button"
                          onClick={handleCreateNewCourse}
                          disabled={isCreatingCourse}
                          size="sm"
                          className="flex-1"
                        >
                          {isCreatingCourse ? (
                            <>
                              <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            <>
                              <Plus className="mr-2 h-3 w-3" />
                              Create Course
                            </>
                          )}
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleCancelNewCourse}
                          size="sm"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </Card>
                ) : (
                  <>
                    <Select
                      value={formData.courseId}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, courseId: value }))}
                      disabled={!formData.departmentId || isLoadingCourses}
                    >
                      <SelectTrigger className={errors.courseId ? "border-red-500" : ""}>
                        <SelectValue placeholder={
                          !formData.departmentId ? "Select department first" :
                          isLoadingCourses ? "Loading..." :
                          courses.length === 0 ? "No courses found - click 'Add New Course'" :
                          "Select course"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {courses.length === 0 && formData.departmentId && !isLoadingCourses ? (
                          <div className="p-2 text-sm text-muted-foreground text-center">
                            No courses found for this department
                          </div>
                        ) : (
                          courses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="font-mono text-xs">
                                  {course.code}
                                </Badge>
                                <span>{course.title}</span>
                                {course.isElective && (
                                  <Badge variant="secondary" className="text-xs">
                                    Elective
                                  </Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {errors.courseId && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.courseId}
                      </p>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <h4 className="font-medium">Exam Schedule</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="examDate">Exam Date *</Label>
                <Input
                  id="examDate"
                  type="date"
                  value={formData.examDate.toISOString().split('T')[0]}
                  onChange={(e) => setFormData(prev => ({ ...prev, examDate: new Date(e.target.value) }))}
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="startTime">Start Time *</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  className={errors.startTime ? "border-red-500" : ""}
                  required
                />
                {errors.startTime && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.startTime}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime">End Time *</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                  className={errors.endTime ? "border-red-500" : ""}
                  required
                />
                {errors.endTime && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.endTime}
                  </p>
                )}
              </div>
            </div>

            {examDuration > 0 && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Duration: {Math.floor(examDuration / 60)}h {examDuration % 60}m</span>
              </div>
            )}
          </div>

          {/* Hall Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Available Halls</h4>
              {isLoadingHalls && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading halls...</span>
                </div>
              )}
            </div>

            {errors.selectedHalls && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.selectedHalls}
              </p>
            )}

            {availableHalls.length === 0 ? (
              <div className="p-4 bg-muted rounded-lg text-center">
                <Building className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">
                  {isLoadingHalls ? "Loading halls..." : "No halls available for the selected time"}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-64 overflow-y-auto border rounded-lg p-4">
                {availableHalls.map((hall) => (
                  <div key={hall.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id={`hall-${hall.id}`}
                        checked={formData.selectedHalls.includes(hall.id)}
                        onCheckedChange={(checked) => handleHallToggle(hall.id, checked as boolean)}
                      />
                      <div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {hall.code}
                          </Badge>
                          <span className="font-medium">{hall.name}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          📍 {hall.location} • 👥 {hall.capacity} capacity
                        </div>
                      </div>
                    </div>

                    {formData.selectedHalls.includes(hall.id) && (
                      <div className="flex items-center gap-2">
                        <Label htmlFor={`capacity-${hall.id}`} className="text-xs">
                          Capacity:
                        </Label>
                        <Input
                          id={`capacity-${hall.id}`}
                          type="number"
                          min="1"
                          max={hall.capacity}
                          defaultValue={hall.capacity}
                          onChange={(e) => handleCapacityOverride(hall.id, parseInt(e.target.value) || hall.capacity)}
                          className="w-20 h-8 text-xs"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {formData.selectedHalls.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Selected {formData.selectedHalls.length} hall{formData.selectedHalls.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>

          {/* Preview */}
          {selectedSession && selectedSemester && selectedFaculty && selectedDepartment && selectedLevel && selectedCourse && (
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Exam Preview</h4>
              <div className="space-y-1 text-sm">
                <p><strong>Session:</strong> {selectedSession.name} - {selectedSemester.name}</p>
                <p><strong>Course:</strong> {selectedCourse.code} - {selectedCourse.title}</p>
                <p><strong>Department:</strong> {selectedDepartment.name} ({selectedFaculty.code})</p>
                <p><strong>Level:</strong> {selectedLevel.name}</p>
                {formData.examDate && formData.startTime && formData.endTime && (
                  <>
                    <p><strong>Date:</strong> {formData.examDate.toLocaleDateString()}</p>
                    <p><strong>Time:</strong> {formatExamTime(formData.startTime, formData.endTime)}</p>
                    <p><strong>Duration:</strong> {Math.floor(examDuration / 60)}h {examDuration % 60}m</p>
                  </>
                )}
                {formData.selectedHalls.length > 0 && (
                  <p><strong>Halls:</strong> {formData.selectedHalls.length} selected</p>
                )}
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Exam...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Exam
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
