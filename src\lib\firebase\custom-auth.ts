// Custom Authentication System for Hall Automata
import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  User,
  UserCredential
} from 'firebase/auth';
import { 
  doc, 
  getDoc, 
  setDoc, 
  collection, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { auth, db } from './firebase';

// User types in the system
export type UserRole = 'admin' | 'supervisor' | 'invigilator' | 'student' | 'department';

export interface HallAutomataUser {
  uid: string;
  id: string; // matric number, admin ID, supervisor ID, etc.
  email?: string;
  role: UserRole;
  displayName?: string;
  department?: string;
  isFirstLogin: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
}

// Default passwords for different user types
const DEFAULT_PASSWORDS = {
  student: 'student123',
  supervisor: 'supervisor123',
  invigilator: 'invigilator123',
  department: 'department123',
  admin: '706_113' // Your specified admin password
};

// Admin credentials
const ADMIN_CREDENTIALS = {
  id: 'auto-admin-id-9aa745-mapoly-exam-hall-automata',
  password: '706_113'
};

// Sign in with ID or Email
export const signInWithIdOrEmail = async (
  identifier: string,
  password: string
): Promise<{ user: User; userData: HallAutomataUser }> => {
  try {
    console.log('🔐 Sign in attempt with identifier:', identifier);
    console.log('🔐 Checking against admin ID:', ADMIN_CREDENTIALS.id);
    console.log('🔐 Password match:', password === ADMIN_CREDENTIALS.password);

    // Check if it's the admin login
    if (identifier === ADMIN_CREDENTIALS.id && password === ADMIN_CREDENTIALS.password) {
      console.log('✅ Admin credentials matched, proceeding with admin login');
      return await handleAdminLogin();
    }

    // Check if identifier is email format
    const isEmail = identifier.includes('@');

    if (isEmail) {
      console.log('📧 Email format detected, signing in with email');
      // Sign in with email
      const userCredential = await signInWithEmailAndPassword(auth, identifier, password);
      const userData = await getUserData(userCredential.user.uid, userCredential.user.email || identifier);
      return { user: userCredential.user, userData };
    } else {
      console.log('🆔 ID format detected, signing in with ID');
      // Sign in with ID (matric number, supervisor ID, etc.)
      return await signInWithId(identifier, password);
    }
  } catch (error: any) {
    console.error('❌ Sign in error:', error);
    console.error('❌ Error code:', error.code);
    console.error('❌ Error message:', error.message);
    console.error('❌ Error name:', error.name);

    // If it's one of our custom errors, re-throw it as-is
    if (error.name && ['FIRST_TIME_LOGIN', 'STUDENT_NOT_FOUND', 'STAFF_NOT_FOUND', 'INVALID_DEFAULT_PASSWORD', 'EMAIL_NOT_VERIFIED', 'INVALID_EMAIL_PASSWORD'].includes(error.name)) {
      throw error;
    }

    // For Firebase auth errors, use the generic handler
    throw new Error(getAuthErrorMessage(error.code || error.message));
  }
};

// Handle admin login (special case)
const handleAdminLogin = async (): Promise<{ user: User; userData: HallAutomataUser }> => {
  // For admin, we'll create a special Firebase user if it doesn't exist
  const adminEmail = '<EMAIL>';

  console.log('🔐 Starting admin login process...');
  console.log('🔐 Admin email:', adminEmail);
  console.log('🔐 Admin password length:', ADMIN_CREDENTIALS.password.length);

  try {
    console.log('🔐 Attempting admin login with Firebase Auth...');

    // Try to sign in with admin email
    const userCredential = await signInWithEmailAndPassword(auth, adminEmail, ADMIN_CREDENTIALS.password);
    console.log('✅ Admin Firebase auth successful, UID:', userCredential.user.uid);

    // Try to get existing user data
    try {
      const userData = await getUserData(userCredential.user.uid);
      console.log('✅ Admin user data retrieved from Firestore:', userData);
      return { user: userCredential.user, userData };
    } catch (userDataError) {
      console.log('⚠️ User data not found in Firestore, creating...');

      // Create user data in Firestore
      const userData: HallAutomataUser = {
        uid: userCredential.user.uid,
        id: ADMIN_CREDENTIALS.id,
        email: adminEmail,
        role: 'admin',
        displayName: 'System Administrator',
        isFirstLogin: false,
        createdAt: new Date()
      };

      await setDoc(doc(db, 'users', userCredential.user.uid), userData);
      console.log('✅ Admin user data created in Firestore');

      return { user: userCredential.user, userData };
    }

  } catch (authError: any) {
    console.log('❌ Admin Firebase auth failed:', authError.code, authError.message);

    // If user doesn't exist, create the account
    if (authError.code === 'auth/user-not-found' || authError.code === 'auth/invalid-credential' || authError.code === 'auth/invalid-email') {
      try {
        console.log('🔧 Creating new admin Firebase account...');

        // Create admin account
        const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, ADMIN_CREDENTIALS.password);
        console.log('✅ Admin Firebase account created, UID:', userCredential.user.uid);

        const userData: HallAutomataUser = {
          uid: userCredential.user.uid,
          id: ADMIN_CREDENTIALS.id,
          email: adminEmail,
          role: 'admin',
          displayName: 'System Administrator',
          isFirstLogin: false,
          createdAt: new Date()
        };

        await setDoc(doc(db, 'users', userCredential.user.uid), userData);
        console.log('✅ Admin user data saved to Firestore');

        return { user: userCredential.user, userData };
      } catch (createError: any) {
        console.error('❌ Failed to create admin account:', createError.code, createError.message);
        throw new Error(`Failed to create admin account: ${createError.message}`);
      }
    }

    throw new Error(`Admin authentication failed: ${authError.message}`);
  }
};

// Sign in with ID (matric number, supervisor ID, etc.)
const signInWithId = async (
  id: string,
  password: string
): Promise<{ user: User; userData: HallAutomataUser }> => {
  console.log('🔍 Searching for user with ID:', id);

  // First check if it's a matric number (contains forward slashes)
  if (id.includes('/')) {
    console.log('📚 Matric number detected, checking students collection');
    return await signInStudent(id, password);
  }

  // Check system_users collection for staff/supervisors
  console.log('👨‍💼 Staff ID detected, checking system_users collection');
  return await signInStaff(id, password);
};

// Sign in staff with user ID
const signInStaff = async (
  userId: string,
  password: string
): Promise<{ user: User; userData: HallAutomataUser }> => {
  console.log('👨‍💼 Searching for staff with user ID:', userId);

  const systemUsersRef = collection(db, 'system_users');
  const q = query(systemUsersRef, where('userId', '==', userId));
  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    const error = new Error('No staff account found with this ID');
    error.name = 'STAFF_NOT_FOUND';
    throw error;
  }

  const staffDoc = querySnapshot.docs[0];
  const staffData = staffDoc.data();

  console.log('✅ Staff found:', {
    userId: staffData.userId,
    role: staffData.role,
    hasChangedPassword: staffData.hasChangedPassword
  });

  // Check if staff has completed account setup
  if (staffData.hasChangedPassword && staffData.email) {
    console.log('📧 Staff has completed setup, signing in with email');
    // Staff has completed setup, sign in with email
    try {
      const userCredential = await signInWithEmailAndPassword(auth, staffData.email, password);
      const userData: HallAutomataUser = {
        uid: userCredential.user.uid,
        id: staffData.userId,
        role: staffData.role,
        displayName: staffData.name,
        email: staffData.email,
        department: staffData.department,
        isFirstLogin: false,
        createdAt: new Date()
      };
      return { user: userCredential.user, userData };
    } catch (error) {
      const authError = new Error('Invalid email or password');
      authError.name = 'INVALID_EMAIL_PASSWORD';
      throw authError;
    }
  } else {
    console.log('🔑 First-time staff login detected, checking default password');
    // First time login - check default password
    if (password !== staffData.defaultPassword) {
      const error = new Error('Invalid staff ID or password');
      error.name = 'INVALID_DEFAULT_PASSWORD';
      throw error;
    }

    // For first-time login, throw special error
    const error = new Error('First time login detected');
    error.name = 'FIRST_TIME_LOGIN';
    throw error;
  }
};

// Sign in student with matric number
const signInStudent = async (
  matricNumber: string,
  password: string
): Promise<{ user: User; userData: HallAutomataUser }> => {
  console.log('👨‍🎓 Searching for student with matric number:', matricNumber);

  const studentsRef = collection(db, 'students');
  const q = query(studentsRef, where('matricNumber', '==', matricNumber.toUpperCase()));
  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    const error = new Error('No student account found with this matric number');
    error.name = 'STUDENT_NOT_FOUND';
    throw error;
  }

  const studentDoc = querySnapshot.docs[0];
  const studentData = studentDoc.data();

  console.log('✅ Student found:', {
    matricNumber: studentData.matricNumber,
    name: studentData.name,
    departmentName: studentData.departmentName,
    levelName: studentData.levelName,
    hasSetupAccount: studentData.hasSetupAccount,
    hasChangedPassword: studentData.hasChangedPassword
  });

  // Check if student has completed account setup
  if (studentData.hasSetupAccount && studentData.email) {
    console.log('📧 Student has completed setup, signing in with email');
    // Student has completed setup, sign in with email
    try {
      const userCredential = await signInWithEmailAndPassword(auth, studentData.email, password);

      // Check if email is verified
      if (!userCredential.user.emailVerified) {
        const error = new Error('Please verify your email address before signing in');
        error.name = 'EMAIL_NOT_VERIFIED';
        throw error;
      }

      const userData: HallAutomataUser = {
        uid: userCredential.user.uid,
        id: studentData.matricNumber,
        role: 'student',
        displayName: studentData.name,
        email: studentData.email,
        department: studentData.departmentName,
        isFirstLogin: false,
        createdAt: new Date()
      };
      return { user: userCredential.user, userData };
    } catch (error: any) {
      if (error.name === 'EMAIL_NOT_VERIFIED') {
        throw error; // Re-throw the properly formatted error
      }
      const authError = new Error('Invalid email or password');
      authError.name = 'INVALID_EMAIL_PASSWORD';
      throw authError;
    }
  } else {
    console.log('🔑 First-time login detected, checking default password');
    // First time login - check default password
    if (password !== studentData.defaultPassword) {
      const error = new Error('Invalid matric number or password');
      error.name = 'INVALID_DEFAULT_PASSWORD';
      throw error;
    }

    // For first-time login, throw special error
    const error = new Error('First time login detected');
    error.name = 'FIRST_TIME_LOGIN';
    throw error;
  }
};

// Get user data from Firestore - checks multiple collections
const getUserData = async (uid: string, userEmail?: string): Promise<HallAutomataUser> => {
  console.log('🔍 Getting user data for UID:', uid, 'Email:', userEmail);

  // If we have an email, use it to find the user
  if (userEmail) {
    // Check students collection first
    const studentsRef = collection(db, 'students');
    const studentQuery = query(studentsRef, where('email', '==', userEmail));
    const studentSnapshot = await getDocs(studentQuery);

    if (!studentSnapshot.empty) {
      const studentData = studentSnapshot.docs[0].data();
      console.log('✅ Found student data:', studentData.matricNumber);
      return {
        uid: uid,
        id: studentData.matricNumber,
        role: 'student',
        displayName: studentData.name,
        email: studentData.email,
        department: studentData.departmentName,
        isFirstLogin: false,
        createdAt: new Date()
      };
    }

    // Check system_users collection for staff
    const systemUsersRef = collection(db, 'system_users');
    const staffQuery = query(systemUsersRef, where('email', '==', userEmail));
    const staffSnapshot = await getDocs(staffQuery);

    if (!staffSnapshot.empty) {
      const staffData = staffSnapshot.docs[0].data();
      console.log('✅ Found staff data:', staffData.userId);
      return {
        uid: uid,
        id: staffData.userId,
        role: staffData.role,
        displayName: staffData.name,
        email: staffData.email,
        department: staffData.department,
        isFirstLogin: false,
        createdAt: new Date()
      };
    }
  }

  // Fallback: check users collection (for admin)
  const userDoc = await getDoc(doc(db, 'users', uid));
  if (userDoc.exists()) {
    console.log('✅ Found admin data');
    return userDoc.data() as HallAutomataUser;
  }

  console.error('❌ User data not found for UID:', uid, 'Email:', userEmail);
  throw new Error('User data not found.');
};

// Create user account (for admin to create supervisors, students, etc.)
export const createUserAccount = async (
  id: string,
  role: UserRole,
  displayName?: string,
  department?: string
): Promise<void> => {
  const userData: HallAutomataUser = {
    uid: '', // Will be set after Firebase user creation
    id,
    role,
    displayName,
    department,
    isFirstLogin: true,
    createdAt: new Date()
  };
  
  // Store in Firestore with the ID as document ID for easy lookup
  await setDoc(doc(db, 'users', id), userData);
};

// Helper function for error messages
const getAuthErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/user-not-found':
    case 'auth/wrong-password':
    case 'auth/invalid-credential':
      return 'Invalid ID/email or password. Please check your credentials.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    case 'FIRST_TIME_LOGIN':
      return 'First time login detected. Please complete your account setup.';
    default:
      return 'An error occurred during sign in. Please try again.';
  }
};

// Helper function to get student data for setup
export const getStudentDataForSetup = async (matricNumber: string) => {
  const studentsRef = collection(db, 'students');
  const q = query(studentsRef, where('matricNumber', '==', matricNumber));
  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    throw new Error('Student not found');
  }

  const studentData = querySnapshot.docs[0].data();
  return {
    matricNumber: studentData.matricNumber,
    name: studentData.name,
    departmentName: studentData.departmentName,
    levelName: studentData.levelName,
    facultyName: studentData.facultyName || 'Unknown'
  };
};

// Helper function to get staff data for setup
export const getStaffDataForSetup = async (userId: string) => {
  const systemUsersRef = collection(db, 'system_users');
  const q = query(systemUsersRef, where('userId', '==', userId));
  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    throw new Error('Staff member not found');
  }

  const staffData = querySnapshot.docs[0].data();
  return {
    userId: staffData.userId,
    name: staffData.name,
    role: staffData.role,
    department: staffData.department,
    email: staffData.email || '',
    hasChangedPassword: staffData.hasChangedPassword || false
  };
};
