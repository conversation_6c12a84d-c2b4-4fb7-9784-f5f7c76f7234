"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { logCourseAdded } from "@/lib/firebase/activity-service";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BookOpen,
  Search,
  Filter,
  Download,
  Plus,
  Edit,
  Trash2,
  Eye,
  GraduationCap,
  Building,
  Loader2,
  Refresh<PERSON><PERSON>,
  FileSpreadsheet,
  Star,
  Clock,
  Users
} from "lucide-react";
import { getAllCourses, getAllFaculties, getAllDepartments } from "@/lib/firebase/department-service";

interface Course {
  id: string;
  code: string;
  title: string;
  creditUnits?: number;
  isElective: boolean;
  facultyId: string;
  facultyName: string;
  departmentId: string;
  departmentName: string;
  description?: string;
  enrolledStudents?: number; // Added for UI display
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFaculty, setSelectedFaculty] = useState<string>("all");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [selectedLevel, setSelectedLevel] = useState<string>("all");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [isAddingCourse, setIsAddingCourse] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [addCourseData, setAddCourseData] = useState({
    courseCode: "",
    courseTitle: "",
    departmentName: "",
    levelName: "",
    facultyName: "",
    creditUnits: 3,
    semester: "First",
    courseType: "core" as "core" | "elective"
  });

  const loadCourses = async () => {
    setIsLoading(true);
    try {
      console.log('📚 Loading courses from Firestore...');

      // Get real courses data from Firestore
      const coursesData = await getAllCourses();

      console.log(`✅ Loaded ${coursesData.length} courses from Firestore`);

      // Add enrolled students count (this would need to be calculated from student-course relationships)
      const coursesWithEnrollment = coursesData.map(course => ({
        ...course,
        enrolledStudents: Math.floor(Math.random() * 100) + 20 // Placeholder - implement real enrollment count
      }));

      setCourses(coursesWithEnrollment);
      setFilteredCourses(coursesWithEnrollment);
    } catch (error) {
      console.error('❌ Error loading courses:', error);
      setCourses([]);
      setFilteredCourses([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadCourses();
  }, []);

  // Filter courses based on search and filters
  useEffect(() => {
    let filtered = courses;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Faculty filter
    if (selectedFaculty !== "all") {
      filtered = filtered.filter(course => course.facultyName === selectedFaculty);
    }

    // Department filter
    if (selectedDepartment !== "all") {
      filtered = filtered.filter(course => course.departmentName === selectedDepartment);
    }

    // Level filter
    if (selectedLevel !== "all") {
      filtered = filtered.filter(course => course.levelName === selectedLevel);
    }

    // Type filter
    if (selectedType !== "all") {
      if (selectedType === "core") {
        filtered = filtered.filter(course => !course.isElective);
      } else if (selectedType === "elective") {
        filtered = filtered.filter(course => course.isElective);
      }
    }

    setFilteredCourses(filtered);
  }, [courses, searchTerm, selectedFaculty, selectedDepartment, selectedLevel, selectedType]);

  const getTypeBadge = (isElective: boolean) => {
    return isElective 
      ? <Badge variant="outline" className="text-purple-600 border-purple-600">Elective</Badge>
      : <Badge className="bg-blue-600">Core</Badge>;
  };

  const getStatusBadge = () => {
    // All courses are considered active since we don't have status field
    return <Badge className="bg-green-600">Active</Badge>;
  };

  const exportCourses = (format: 'excel' | 'csv') => {
    console.log(`Exporting ${filteredCourses.length} courses as ${format}`);
  };

  const handleAddCourse = async () => {
    if (!addCourseData.courseCode || !addCourseData.courseTitle || !addCourseData.departmentName || !addCourseData.levelName) {
      alert('Please fill in all required fields');
      return;
    }

    setIsAddingCourse(true);
    try {
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      const courseData = {
        courseCode: addCourseData.courseCode.toUpperCase(),
        courseTitle: addCourseData.courseTitle,
        departmentName: addCourseData.departmentName,
        levelName: addCourseData.levelName,
        facultyName: addCourseData.facultyName || 'Unknown',
        creditUnits: addCourseData.creditUnits,
        semester: addCourseData.semester,
        isElective: addCourseData.courseType === 'elective',
        status: 'active' as const,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: 'admin' // TODO: Get actual admin ID
      };

      const coursesRef = collection(db, 'courses');
      const docRef = await addDoc(coursesRef, courseData);

      console.log('✅ Course added successfully:', docRef.id);

      // Log activity
      await logCourseAdded(addCourseData.courseCode, addCourseData.courseTitle);

      // Reset form and close dialog
      setAddCourseData({
        courseCode: "",
        courseTitle: "",
        departmentName: "",
        levelName: "",
        facultyName: "",
        creditUnits: 3,
        semester: "First",
        courseType: "core"
      });
      setShowAddDialog(false);

      // Reload courses list
      loadCourses();

    } catch (error) {
      console.error('❌ Error adding course:', error);
      alert('Failed to add course. Please try again.');
    } finally {
      setIsAddingCourse(false);
    }
  };

  const handleDeleteCourse = async () => {
    if (!selectedCourse) return;

    setIsDeleting(true);
    try {
      const { doc, deleteDoc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      await deleteDoc(doc(db, 'courses', selectedCourse.id));

      console.log('✅ Course deleted successfully:', selectedCourse.id);

      // Log activity
      await logCourseAdded(`${selectedCourse.code} - DELETED`, selectedCourse.title);

      // Close dialog and reload courses
      setShowDeleteDialog(false);
      setSelectedCourse(null);
      loadCourses();

    } catch (error) {
      console.error('❌ Error deleting course:', error);
      alert('Failed to delete course. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const stats = {
    total: courses.length,
    core: courses.filter(c => !c.isElective).length,
    elective: courses.filter(c => c.isElective).length,
    active: courses.length, // All courses are considered active
    totalEnrollment: courses.reduce((sum, c) => sum + (c.enrolledStudents || 0), 0)
  };

  return (
    <AdminLayout
      title="Courses Management"
      description="Manage academic courses and curriculum"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.total}
            </div>
            <p className="text-xs text-muted-foreground">All courses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Core Courses</CardTitle>
            <Star className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.core}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? Math.round((stats.core / stats.total) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Elective Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.elective}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? Math.round((stats.elective / stats.total) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
            <Clock className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.active}
            </div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Enrollment</CardTitle>
            <Users className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.totalEnrollment.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Students enrolled</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadCourses}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportCourses('excel')}
              >
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Export Excel
              </Button>
              <Button size="sm" onClick={() => setShowAddDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Course
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search courses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedFaculty} onValueChange={setSelectedFaculty}>
              <SelectTrigger>
                <SelectValue placeholder="Faculty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Faculties</SelectItem>
                <SelectItem value="Faculty of Engineering">Engineering</SelectItem>
                <SelectItem value="Faculty of Science">Science</SelectItem>
                <SelectItem value="Faculty of Business">Business</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
              <SelectTrigger>
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                <SelectItem value="Computer Science">Computer Science</SelectItem>
                <SelectItem value="Electrical Engineering">Electrical Eng.</SelectItem>
                <SelectItem value="Mechanical Engineering">Mechanical Eng.</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedLevel} onValueChange={setSelectedLevel}>
              <SelectTrigger>
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="ND1">ND1</SelectItem>
                <SelectItem value="ND2">ND2</SelectItem>
                <SelectItem value="HND1">HND1</SelectItem>
                <SelectItem value="HND2">HND2</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="core">Core</SelectItem>
                <SelectItem value="elective">Elective</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
            <span>Showing {filteredCourses.length} of {courses.length} courses</span>
            {(searchTerm || selectedFaculty !== "all" || selectedDepartment !== "all" || selectedLevel !== "all" || selectedType !== "all") && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedFaculty("all");
                  setSelectedDepartment("all");
                  setSelectedLevel("all");
                  setSelectedType("all");
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Courses Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Courses List
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="h-10 w-20 bg-muted rounded animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-muted rounded w-1/3 animate-pulse" />
                    <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
                  </div>
                  <div className="h-6 bg-muted rounded w-16 animate-pulse" />
                </div>
              ))}
            </div>
          ) : filteredCourses.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <TypographyP className="text-muted-foreground">
                {searchTerm || selectedFaculty !== "all" || selectedDepartment !== "all" || selectedLevel !== "all" || selectedType !== "all"
                  ? "No courses match your filters"
                  : "No courses found"
                }
              </TypographyP>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Course</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Credit Units</TableHead>
                    <TableHead>Enrollment</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCourses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{course.title}</div>
                          <div className="text-sm text-muted-foreground">
                            <Badge variant="outline" className="font-mono text-xs mr-2">
                              {course.code}
                            </Badge>
                            {course.description && course.description.length > 50 
                              ? `${course.description.substring(0, 50)}...`
                              : course.description
                            }
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{course.departmentName}</div>
                          <div className="text-sm text-muted-foreground">{course.facultyName}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{course.levelName}</Badge>
                      </TableCell>
                      <TableCell>
                        {getTypeBadge(course.isElective)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-mono">
                          {course.creditUnits} units
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{course.enrolledStudents}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // TODO: Implement view course details
                              console.log('View course:', course.id);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedCourse(course);
                              setShowEditDialog(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedCourse(course);
                              setShowDeleteDialog(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Course Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Course</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="courseCode" className="text-right">
                Course Code *
              </Label>
              <Input
                id="courseCode"
                placeholder="CSC201"
                className="col-span-3"
                value={addCourseData.courseCode}
                onChange={(e) => setAddCourseData({...addCourseData, courseCode: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="courseTitle" className="text-right">
                Course Title *
              </Label>
              <Input
                id="courseTitle"
                placeholder="Data Structures"
                className="col-span-3"
                value={addCourseData.courseTitle}
                onChange={(e) => setAddCourseData({...addCourseData, courseTitle: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="department" className="text-right">
                Department *
              </Label>
              <Input
                id="department"
                placeholder="Computer Science"
                className="col-span-3"
                value={addCourseData.departmentName}
                onChange={(e) => setAddCourseData({...addCourseData, departmentName: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="level" className="text-right">
                Level *
              </Label>
              <Input
                id="level"
                placeholder="ND2"
                className="col-span-3"
                value={addCourseData.levelName}
                onChange={(e) => setAddCourseData({...addCourseData, levelName: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="creditUnits" className="text-right">
                Credit Units
              </Label>
              <Input
                id="creditUnits"
                type="number"
                placeholder="3"
                className="col-span-3"
                value={addCourseData.creditUnits}
                onChange={(e) => setAddCourseData({...addCourseData, creditUnits: parseInt(e.target.value) || 3})}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowAddDialog(false)} disabled={isAddingCourse}>
              Cancel
            </Button>
            <Button onClick={handleAddCourse} disabled={isAddingCourse}>
              {isAddingCourse ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                'Add Course'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
