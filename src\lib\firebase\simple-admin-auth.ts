// Simplified Admin Authentication (without Firestore dependency)
import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  User
} from 'firebase/auth';
import { auth } from './firebase';

// Admin credentials
const ADMIN_CREDENTIALS = {
  id: 'auto-admin-id-9aa745-mapoly-exam-hall-automata',
  password: '706_113',
  email: '<EMAIL>'
};

export interface SimpleAdminUser {
  uid: string;
  email: string;
  role: 'admin';
  displayName: string;
}

// Simple admin sign in
export const signInAdmin = async (
  adminId: string, 
  password: string
): Promise<{ user: User; userData: SimpleAdminUser }> => {
  console.log('🔐 Simple admin sign in attempt');
  console.log('🔐 Provided ID:', adminId);
  console.log('🔐 Expected ID:', ADMIN_CREDENTIALS.id);
  console.log('🔐 ID match:', adminId === ADMIN_CREDENTIALS.id);
  console.log('🔐 Password match:', password === ADMIN_CREDENTIALS.password);
  
  // Verify admin credentials
  if (adminId !== ADMIN_CREDENTIALS.id || password !== ADMIN_CREDENTIALS.password) {
    throw new Error('Invalid admin credentials');
  }
  
  try {
    console.log('✅ Admin credentials verified, attempting Firebase auth...');
    
    // Try to sign in with Firebase
    const userCredential = await signInWithEmailAndPassword(
      auth, 
      ADMIN_CREDENTIALS.email, 
      ADMIN_CREDENTIALS.password
    );
    
    console.log('✅ Firebase auth successful');
    
    const userData: SimpleAdminUser = {
      uid: userCredential.user.uid,
      email: ADMIN_CREDENTIALS.email,
      role: 'admin',
      displayName: 'System Administrator'
    };
    
    return { user: userCredential.user, userData };
    
  } catch (firebaseError: any) {
    console.log('❌ Firebase auth failed:', firebaseError.code);
    
    // If user doesn't exist, create it
    if (firebaseError.code === 'auth/user-not-found' || 
        firebaseError.code === 'auth/invalid-credential' ||
        firebaseError.code === 'auth/invalid-email') {
      
      try {
        console.log('🔧 Creating admin Firebase account...');
        
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          ADMIN_CREDENTIALS.email,
          ADMIN_CREDENTIALS.password
        );
        
        console.log('✅ Admin account created successfully');
        
        const userData: SimpleAdminUser = {
          uid: userCredential.user.uid,
          email: ADMIN_CREDENTIALS.email,
          role: 'admin',
          displayName: 'System Administrator'
        };
        
        return { user: userCredential.user, userData };
        
      } catch (createError: any) {
        console.error('❌ Failed to create admin account:', createError);
        throw new Error(`Failed to create admin account: ${createError.message}`);
      }
    }
    
    throw new Error(`Authentication failed: ${firebaseError.message}`);
  }
};
