"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Building2, Plus, Loader2, AlertCircle, Key } from "lucide-react";
import { DepartmentFormData, Faculty, DepartmentPermissions, LEVEL_PRESETS, LevelPresetType } from "@/types/department";
import { isDepartmentCodeUnique, getAllFaculties } from "@/lib/firebase/department-service";

interface AddDepartmentFormProps {
  onSubmit: (data: DepartmentFormData, levelPreset: LevelPresetType, customLevels?: Array<{name: string, code: string}>) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function AddDepartmentForm({ onSubmit, onCancel, isLoading = false }: AddDepartmentFormProps) {
  const [formData, setFormData] = useState<DepartmentFormData>({
    name: "",
    code: "",
    facultyId: "",
    description: "",
    hasPortalAccess: false,
    portalPermissions: {
      canAddStudents: false,
      canViewStudents: false,
      canGenerateReports: false,
      canManageExamRegistrations: false
    }
  });

  const [faculties, setFaculties] = useState<Faculty[]>([]);
  const [levelPreset, setLevelPreset] = useState<LevelPresetType>("ND_HND");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidatingCode, setIsValidatingCode] = useState(false);
  const [isLoadingFaculties, setIsLoadingFaculties] = useState(true);

  // Load faculties on component mount
  useEffect(() => {
    loadFaculties();
  }, []);

  const loadFaculties = async () => {
    try {
      const facultiesData = await getAllFaculties();
      setFaculties(facultiesData);
    } catch (error) {
      console.error("Error loading faculties:", error);
    } finally {
      setIsLoadingFaculties(false);
    }
  };

  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    // Validate required fields
    if (!formData.name.trim()) {
      newErrors.name = "Department name is required";
    }

    if (!formData.code.trim()) {
      newErrors.code = "Department code is required";
    } else if (formData.code.length < 2) {
      newErrors.code = "Department code must be at least 2 characters";
    } else if (formData.facultyId) {
      // Check if department code is unique within faculty
      setIsValidatingCode(true);
      const isUnique = await isDepartmentCodeUnique(formData.code, formData.facultyId);
      setIsValidatingCode(false);
      
      if (!isUnique) {
        newErrors.code = "Department code already exists in this faculty";
      }
    }

    if (!formData.facultyId) {
      newErrors.facultyId = "Please select a faculty";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    await onSubmit(formData, levelPreset);
  };

  const handlePermissionChange = (permission: keyof DepartmentPermissions, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      portalPermissions: {
        ...prev.portalPermissions,
        [permission]: checked
      }
    }));
  };

  const selectedFaculty = faculties.find(f => f.id === formData.facultyId);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Add New Department
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Department Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Computer Science, Mechanical Engineering"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={errors.name ? "border-red-500" : ""}
                required
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="code">Department Code *</Label>
              <Input
                id="code"
                placeholder="e.g., CS, ME, EE"
                value={formData.code}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                className={`font-mono ${errors.code ? "border-red-500" : ""}`}
                maxLength={10}
                required
              />
              {errors.code && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.code}
                </p>
              )}
              {isValidatingCode && (
                <p className="text-sm text-blue-600 flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Checking availability...
                </p>
              )}
            </div>
          </div>

          {/* Faculty Selection */}
          <div className="space-y-2">
            <Label>Faculty *</Label>
            <Select 
              value={formData.facultyId} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, facultyId: value }))}
              disabled={isLoadingFaculties}
            >
              <SelectTrigger className={errors.facultyId ? "border-red-500" : ""}>
                <SelectValue placeholder={isLoadingFaculties ? "Loading faculties..." : "Select a faculty"} />
              </SelectTrigger>
              <SelectContent>
                {faculties.map((faculty) => (
                  <SelectItem key={faculty.id} value={faculty.id}>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono text-xs">
                        {faculty.code}
                      </Badge>
                      <span>{faculty.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.facultyId && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.facultyId}
              </p>
            )}
          </div>

          {/* Level Preset Selection */}
          <div className="space-y-2">
            <Label>Level System *</Label>
            <Select 
              value={levelPreset} 
              onValueChange={(value: LevelPresetType) => setLevelPreset(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ND_HND">
                  <div>
                    <p className="font-medium">ND/HND System</p>
                    <p className="text-xs text-muted-foreground">ND1, ND2, HND1, HND2</p>
                  </div>
                </SelectItem>
                <SelectItem value="NUMERIC">
                  <div>
                    <p className="font-medium">Numeric System</p>
                    <p className="text-xs text-muted-foreground">100, 200, 300, 400, 500</p>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              Preview: {LEVEL_PRESETS[levelPreset].map(level => level.code).join(", ")}
            </div>
          </div>

          {/* Portal Access */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasPortalAccess"
                checked={formData.hasPortalAccess}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, hasPortalAccess: checked as boolean }))
                }
              />
              <Label htmlFor="hasPortalAccess" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Grant Department Portal Access
              </Label>
            </div>

            {formData.hasPortalAccess && (
              <Card className="p-4">
                <h4 className="font-medium mb-3">Portal Permissions</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canAddStudents"
                      checked={formData.portalPermissions.canAddStudents}
                      onCheckedChange={(checked) => handlePermissionChange('canAddStudents', checked as boolean)}
                    />
                    <Label htmlFor="canAddStudents">Can add students</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewStudents"
                      checked={formData.portalPermissions.canViewStudents}
                      onCheckedChange={(checked) => handlePermissionChange('canViewStudents', checked as boolean)}
                    />
                    <Label htmlFor="canViewStudents">Can view student lists</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canGenerateReports"
                      checked={formData.portalPermissions.canGenerateReports}
                      onCheckedChange={(checked) => handlePermissionChange('canGenerateReports', checked as boolean)}
                    />
                    <Label htmlFor="canGenerateReports">Can generate reports</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canManageExamRegistrations"
                      checked={formData.portalPermissions.canManageExamRegistrations}
                      onCheckedChange={(checked) => handlePermissionChange('canManageExamRegistrations', checked as boolean)}
                    />
                    <Label htmlFor="canManageExamRegistrations">Can manage exam registrations</Label>
                  </div>
                </div>
                
                {selectedFaculty && formData.code && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">Access ID will be:</p>
                    <Badge variant="outline" className="font-mono">
                      DEPT-{selectedFaculty.code}-{formData.code}-{new Date().getFullYear()}
                    </Badge>
                  </div>
                )}
              </Card>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Brief description of the department..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading || isLoadingFaculties} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding Department...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Department
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
