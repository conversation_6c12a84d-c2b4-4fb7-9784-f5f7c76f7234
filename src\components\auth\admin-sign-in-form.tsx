"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TypographyH2 } from "@/components/ui/typography";
import { Shield, User, Lock, Eye, EyeOff, Loader2, Crown } from "lucide-react";
import { signInAdmin } from "@/lib/firebase/simple-admin-auth";
import { testFirebaseConnection } from "@/lib/firebase/test-connection";

interface AdminSignInFormProps {
  onClose?: () => void;
  isModal?: boolean;
}

export function AdminSignInForm({ onClose, isModal = false }: AdminSignInFormProps) {
  const [adminId, setAdminId] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Test Firebase connection first
    console.log('🔥 Testing Firebase connection...');
    testFirebaseConnection();

    try {
      const { user, userData } = await signInAdmin(adminId, password);

      console.log('✅ Admin signed in successfully:', userData);

      // Redirect to admin dashboard
      router.push('/admin/dashboard');

      // Close modal if it's a modal
      if (isModal && onClose) {
        onClose();
      }
    } catch (err: any) {
      console.error('❌ Admin sign in error:', err.message);
      setError(err.message || 'Failed to sign in. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`w-full max-w-md mx-auto ${isModal ? 'p-0' : 'p-4'}`}>
      <Card className="border-0 shadow-lg">
        <CardHeader className="text-center space-y-4 pb-6">
          <div className="flex items-center justify-center">
            <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
              <Crown className="h-7 w-7 text-white" />
            </div>
          </div>
          <div>
            <TypographyH2 className="text-2xl font-bold">Admin Access</TypographyH2>
            <CardDescription className="font-sans text-muted-foreground mt-2">
              Hall Automata Administrative Portal
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="adminId" className="font-sans text-sm font-medium">
                Administrator ID
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="adminId"
                  type="text"
                  placeholder="Enter admin ID"
                  value={adminId}
                  onChange={(e) => setAdminId(e.target.value)}
                  className="pl-10 font-mono text-sm"
                  required
                />
              </div>
              <p className="text-xs text-muted-foreground font-mono">
                Format: auto-admin-id-xxxxx-mapoly-exam-hall-automata
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="font-sans text-sm font-medium">
                Administrator Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter admin password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 pr-10 font-mono"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <div className="pt-2">
              <Button
                type="submit"
                className="w-full font-sans"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Authenticating...
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-4 w-4" />
                    Access Admin Portal
                  </>
                )}
              </Button>
            </div>
          </form>

          <div className="text-center pt-4 border-t border-border">
            <p className="text-xs text-muted-foreground font-mono">
              🔒 Secure Administrative Access • MAPOLY Hall Automata
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
