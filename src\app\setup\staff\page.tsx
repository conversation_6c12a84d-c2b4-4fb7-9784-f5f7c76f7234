"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TypographyH1, TypographyP } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  User, 
  Lock, 
  Eye, 
  EyeOff, 
  CheckCircle, 
  Loader2,
  Shield,
  AlertCircle,
  ArrowRight,
  UserCheck
} from "lucide-react";

export default function StaffSetupPage() {
  const [setupData, setSetupData] = useState({
    newId: "",
    password: "",
    confirmPassword: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [step, setStep] = useState(1);
  const router = useRouter();

  // Get staff info from temporary session
  const [staffInfo, setStaffInfo] = useState({
    defaultId: "",
    name: "",
    role: "supervisor" as "supervisor" | "invigilator",
    department: "",
    email: ""
  });

  useEffect(() => {
    // Get temporary setup data
    const tempData = localStorage.getItem('temp_staff_setup');
    if (tempData) {
      const data = JSON.parse(tempData);
      console.log('📋 Staff setup data received:', data);
      setStaffInfo({
        defaultId: data.userId || "Unknown",
        name: data.name || "Staff Member",
        role: data.role || "supervisor",
        department: data.department || "Unknown",
        email: data.email || ""
      });
    } else {
      // Redirect back to sign-in if no temp data
      console.log('❌ No temp staff setup data found, redirecting to sign-in');
      router.push('/sign-in');
    }
  }, [router]);

  const handleSetup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validation
    if (setupData.password !== setupData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (setupData.password.length < 6) {
      setError("Password must be at least 6 characters long");
      setIsLoading(false);
      return;
    }

    if (setupData.newId.length < 3) {
      setError("New ID must be at least 3 characters long");
      setIsLoading(false);
      return;
    }

    try {
      // Create Firebase user account with email and password
      const { createUserWithEmailAndPassword } = await import('firebase/auth');
      const { auth } = await import('@/lib/firebase/firebase');
      const { collection, query, where, getDocs, updateDoc, doc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      console.log("Creating Firebase user account for staff...");
      const userCredential = await createUserWithEmailAndPassword(auth, `${setupData.newId}@mapoly.edu.ng`, setupData.password);

      console.log("Updating staff record in Firestore...");
      // Find and update the staff record
      const systemUsersRef = collection(db, 'system_users');
      const q = query(systemUsersRef, where('userId', '==', staffInfo.defaultId));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const staffDoc = querySnapshot.docs[0];
        await updateDoc(doc(db, 'system_users', staffDoc.id), {
          userId: setupData.newId, // Update to new ID
          email: `${setupData.newId}@mapoly.edu.ng`,
          hasChangedPassword: true,
          updatedAt: new Date()
        });

        console.log("✅ Staff account setup completed successfully");

        // Clear temporary setup data
        localStorage.removeItem('temp_staff_setup');

        // Set proper session
        localStorage.setItem('staff_session', JSON.stringify({
          userId: setupData.newId,
          email: `${setupData.newId}@mapoly.edu.ng`,
          role: staffInfo.role,
          loginTime: new Date().toISOString()
        }));

        // Move to success step
        setStep(2);

        // Auto-redirect after 3 seconds
        setTimeout(() => {
          router.push('/staff/dashboard');
        }, 3000);
      } else {
        throw new Error('Staff record not found');
      }

    } catch (error: any) {
      console.error('Staff setup error:', error);
      setError(error.message || "Setup failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (step === 2) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <div className="bg-green-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <TypographyH1 className="text-2xl font-bold text-gray-900 mb-4">
              Setup Complete!
            </TypographyH1>
            <TypographyP className="text-gray-600 mb-6">
              Your account has been successfully configured. You can now access your {staffInfo.role} portal.
            </TypographyP>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Loader2 className="h-4 w-4 animate-spin" />
              Redirecting to dashboard...
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-purple-600 p-2 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hall Automata</h1>
                <p className="text-sm text-gray-600">Staff Account Setup</p>
              </div>
            </div>
            <Badge variant="outline" className="font-mono">
              🎓 MAPOLY
            </Badge>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-md mx-auto px-4 py-8 sm:py-12">
        <div className="text-center mb-8">
          <div className="bg-purple-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <UserCheck className="h-10 w-10 text-purple-600" />
          </div>
          <TypographyH1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Complete Your Setup
          </TypographyH1>
          <TypographyP className="text-gray-600">
            Set up your new ID and password for secure access
          </TypographyP>
        </div>

        {/* Staff Info Card */}
        <Card className="mb-6 bg-white/80 backdrop-blur-sm border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg">Your Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Name:</span>
              <span className="text-sm font-medium">{staffInfo.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Role:</span>
              <span className="text-sm font-medium capitalize">{staffInfo.role}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Department:</span>
              <span className="text-sm font-medium">{staffInfo.department}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Current ID:</span>
              <span className="text-sm font-mono font-medium">{staffInfo.defaultId}</span>
            </div>
          </CardContent>
        </Card>

        {/* Setup Form */}
        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-center">Account Security Setup</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSetup} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <strong>Required:</strong> You must set up a new ID and password to continue using the system.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="newId">New User ID</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="newId"
                    type="text"
                    placeholder="Choose your new ID (e.g., jsmith2024)"
                    value={setupData.newId}
                    onChange={(e) => setSetupData(prev => ({ ...prev, newId: e.target.value }))}
                    className="pl-10 font-mono"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500">
                  This will replace your default ID for future logins
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">New Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Create a strong password"
                    value={setupData.password}
                    onChange={(e) => setSetupData(prev => ({ ...prev, password: e.target.value }))}
                    className="pl-10 pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={setupData.confirmPassword}
                    onChange={(e) => setSetupData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className="pl-10 pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full bg-purple-600 hover:bg-purple-700"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting up account...
                  </>
                ) : (
                  <>
                    Complete Setup
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <TypographyP className="text-xs text-gray-500">
                After setup, you can sign in with your new ID and password
              </TypographyP>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
