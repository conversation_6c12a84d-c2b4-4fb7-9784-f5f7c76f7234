"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { GraduationCap, Plus, Loader2, AlertCircle, User } from "lucide-react";
import {
  StudentFormData,
  Faculty,
  Department,
  Level,
  Gender,
  StudyMode,
  StudentStatus,
  validateMatricNumber
} from "@/types/department";
import { Course } from "@/types/exam";
import {
  getAllFaculties,
  getDepartmentsByFaculty,
  getLevelsByDepartment,
  isMatricNumberUnique
} from "@/lib/firebase/department-service";
import { getCoursesByDepartment } from "@/lib/firebase/exam-service";

interface AddStudentFormProps {
  onSubmit: (data: StudentFormData, selectedCourses: string[]) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function AddStudentForm({ onSubmit, onCancel, isLoading = false }: AddStudentFormProps) {
  const [formData, setFormData] = useState<StudentFormData>({
    matricNumber: "",
    name: "",
    gender: "male",
    phoneNumber: "",
    email: "",
    facultyId: "",
    departmentId: "",
    levelId: "",
    studyMode: "full_time",
    status: "active"
  });

  const [faculties, setFaculties] = useState<Faculty[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [levels, setLevels] = useState<Level[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidatingMatric, setIsValidatingMatric] = useState(false);
  const [isLoadingFaculties, setIsLoadingFaculties] = useState(true);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isLoadingLevels, setIsLoadingLevels] = useState(false);
  const [isLoadingCourses, setIsLoadingCourses] = useState(false);

  // Load faculties on component mount
  useEffect(() => {
    loadFaculties();
  }, []);

  // Load departments when faculty changes
  useEffect(() => {
    if (formData.facultyId) {
      loadDepartments(formData.facultyId);
      // Reset department and level when faculty changes
      setFormData(prev => ({ ...prev, departmentId: "", levelId: "" }));
      setLevels([]);
    } else {
      setDepartments([]);
      setLevels([]);
    }
  }, [formData.facultyId]);

  // Load levels when department changes
  useEffect(() => {
    if (formData.departmentId) {
      loadLevels(formData.departmentId);
      loadCourses(formData.departmentId);
      // Reset level when department changes
      setFormData(prev => ({ ...prev, levelId: "" }));
      setSelectedCourses([]);
    } else {
      setLevels([]);
      setCourses([]);
      setSelectedCourses([]);
    }
  }, [formData.departmentId]);

  const loadFaculties = async () => {
    try {
      const facultiesData = await getAllFaculties();
      setFaculties(facultiesData);
    } catch (error) {
      console.error("Error loading faculties:", error);
    } finally {
      setIsLoadingFaculties(false);
    }
  };

  const loadDepartments = async (facultyId: string) => {
    try {
      setIsLoadingDepartments(true);
      setDepartments([]);
      const departmentsData = await getDepartmentsByFaculty(facultyId);
      setDepartments(departmentsData);
    } catch (error) {
      console.error("Error loading departments:", error);
      setDepartments([]);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  const loadLevels = async (departmentId: string) => {
    try {
      setIsLoadingLevels(true);
      setLevels([]);
      const levelsData = await getLevelsByDepartment(departmentId);
      setLevels(levelsData);
    } catch (error) {
      console.error("Error loading levels:", error);
      setLevels([]);
    } finally {
      setIsLoadingLevels(false);
    }
  };

  const loadCourses = async (departmentId: string) => {
    try {
      setIsLoadingCourses(true);
      setCourses([]);
      const coursesData = await getCoursesByDepartment(departmentId);
      setCourses(coursesData);
    } catch (error) {
      console.error("Error loading courses:", error);
      setCourses([]);
    } finally {
      setIsLoadingCourses(false);
    }
  };

  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    // Validate matric number
    if (!formData.matricNumber.trim()) {
      newErrors.matricNumber = "Matric number is required";
    } else if (!validateMatricNumber(formData.matricNumber)) {
      newErrors.matricNumber = "Matric number must be at least 10 characters and contain 2+ forward slashes";
    } else {
      // Check if matric number is unique
      setIsValidatingMatric(true);
      const isUnique = await isMatricNumberUnique(formData.matricNumber);
      setIsValidatingMatric(false);
      
      if (!isUnique) {
        newErrors.matricNumber = "Matric number already exists";
      }
    }

    // Validate required selections
    if (!formData.facultyId) {
      newErrors.facultyId = "Please select a faculty";
    }

    if (!formData.departmentId) {
      newErrors.departmentId = "Please select a department";
    }

    if (!formData.levelId) {
      newErrors.levelId = "Please select a level";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    await onSubmit(formData, selectedCourses);
  };

  const selectedFaculty = faculties.find(f => f.id === formData.facultyId);
  const selectedDepartment = departments.find(d => d.id === formData.departmentId);
  const selectedLevel = levels.find(l => l.id === formData.levelId);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GraduationCap className="h-5 w-5" />
          Add New Student
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Student Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="matricNumber">Matric Number *</Label>
              <Input
                id="matricNumber"
                placeholder="e.g., CS/2024/001"
                value={formData.matricNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, matricNumber: e.target.value.toUpperCase() }))}
                className={`font-mono ${errors.matricNumber ? "border-red-500" : ""}`}
                required
              />
              {errors.matricNumber && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.matricNumber}
                </p>
              )}
              {isValidatingMatric && (
                <p className="text-sm text-blue-600 flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Checking availability...
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="name">Full Name (Optional)</Label>
              <Input
                id="name"
                placeholder="e.g., John Doe"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Gender *</Label>
              <Select 
                value={formData.gender} 
                onValueChange={(value: Gender) => setFormData(prev => ({ ...prev, gender: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Phone Number (Optional)</Label>
              <Input
                id="phoneNumber"
                placeholder="e.g., +234 ************"
                value={formData.phoneNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
              />
            </div>
          </div>

          {/* Academic Information */}
          <div className="space-y-4">
            <h4 className="font-medium">Academic Information</h4>

            {/* Faculty Selection */}
            <div className="space-y-2">
              <Label>Faculty *</Label>
              <Select
                value={formData.facultyId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, facultyId: value }))}
                disabled={isLoadingFaculties}
              >
                <SelectTrigger className={errors.facultyId ? "border-red-500" : ""}>
                  <SelectValue placeholder={isLoadingFaculties ? "Loading..." : "Select faculty"} />
                </SelectTrigger>
                <SelectContent>
                  {faculties.map((faculty) => (
                    <SelectItem key={faculty.id} value={faculty.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          {faculty.code}
                        </Badge>
                        <span>{faculty.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.facultyId && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.facultyId}
                </p>
              )}
            </div>

            {/* Department Selection */}
            <div className="space-y-2">
              <Label>Department *</Label>
              <Select
                value={formData.departmentId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, departmentId: value }))}
                disabled={!formData.facultyId || isLoadingDepartments}
              >
                <SelectTrigger className={errors.departmentId ? "border-red-500" : ""}>
                  <SelectValue placeholder={
                    !formData.facultyId ? "Select faculty first" :
                    isLoadingDepartments ? "Loading departments..." :
                    departments.length === 0 ? "No departments found" :
                    "Select department"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {departments.length === 0 && formData.facultyId && !isLoadingDepartments ? (
                    <div className="p-2 text-sm text-muted-foreground text-center">
                      No departments found for this faculty
                    </div>
                  ) : (
                    departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {department.code}
                          </Badge>
                          <span>{department.name}</span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.departmentId && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.departmentId}
                </p>
              )}
            </div>

            {/* Level Selection */}
            <div className="space-y-2">
              <Label>Level *</Label>
              <Select
                value={formData.levelId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, levelId: value }))}
                disabled={!formData.departmentId || isLoadingLevels}
              >
                <SelectTrigger className={errors.levelId ? "border-red-500" : ""}>
                  <SelectValue placeholder={
                    !formData.departmentId ? "Select department first" :
                    isLoadingLevels ? "Loading levels..." :
                    levels.length === 0 ? "No levels found" :
                    "Select level"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {levels.length === 0 && formData.departmentId && !isLoadingLevels ? (
                    <div className="p-2 text-sm text-muted-foreground text-center">
                      No levels found for this department
                    </div>
                  ) : (
                    levels.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {level.code}
                          </Badge>
                          <span>{level.name}</span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.levelId && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.levelId}
                </p>
              )}
            </div>
          </div>

          {/* Study Mode and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Study Mode *</Label>
              <Select 
                value={formData.studyMode} 
                onValueChange={(value: StudyMode) => setFormData(prev => ({ ...prev, studyMode: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full_time">Full Time</SelectItem>
                  <SelectItem value="part_time">Part Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Status *</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value: StudentStatus) => setFormData(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Email (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="email">Email Address (Optional)</Label>
            <Input
              id="email"
              type="email"
              placeholder="e.g., <EMAIL>"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            />
          </div>

          {/* Course Registration */}
          <div className="space-y-4">
            <h4 className="font-medium">Course Registration</h4>

            {isLoadingCourses ? (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading courses...</span>
              </div>
            ) : courses.length === 0 ? (
              <div className="p-4 bg-muted rounded-lg text-center">
                <p className="text-muted-foreground">
                  {formData.departmentId ? "No courses found for this department" : "Select a department to view courses"}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Select courses for this student to register:
                </p>

                <div className="grid grid-cols-1 gap-3 max-h-48 overflow-y-auto border rounded-lg p-4">
                  {courses.map((course) => (
                    <div key={course.id} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id={`course-${course.id}`}
                        checked={selectedCourses.includes(course.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedCourses(prev => [...prev, course.id]);
                          } else {
                            setSelectedCourses(prev => prev.filter(id => id !== course.id));
                          }
                        }}
                        className="rounded border-gray-300"
                        aria-label={`Select course ${course.code} - ${course.title}`}
                      />
                      <Label htmlFor={`course-${course.id}`} className="flex-1 cursor-pointer">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            {course.code}
                          </Badge>
                          <span className="text-sm">{course.title}</span>
                          {course.isElective && (
                            <Badge variant="secondary" className="text-xs">
                              Elective
                            </Badge>
                          )}
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>

                {selectedCourses.length > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Selected {selectedCourses.length} course{selectedCourses.length !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Preview */}
          {selectedFaculty && selectedDepartment && selectedLevel && (
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Student Preview</h4>
              <div className="space-y-1 text-sm">
                <p><strong>Faculty:</strong> {selectedFaculty.name} ({selectedFaculty.code})</p>
                <p><strong>Department:</strong> {selectedDepartment.name} ({selectedDepartment.code})</p>
                <p><strong>Level:</strong> {selectedLevel.name} ({selectedLevel.code})</p>
                <p><strong>Study Mode:</strong> {formData.studyMode === "full_time" ? "Full Time" : "Part Time"}</p>
                <p><strong>Default Password:</strong> <code className="bg-background px-1 rounded">student2024</code></p>
                {selectedCourses.length > 0 && (
                  <p><strong>Courses:</strong> {selectedCourses.length} selected</p>
                )}
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding Student...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Student
                </>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
