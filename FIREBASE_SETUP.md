# 🔥 Firebase Setup Guide for Hall Automata

## 📋 Step 1: Get Your Firebase Configuration

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Create a new project** or select existing project
3. **Click "Add app"** and choose **Web app** (</> icon)
4. **Register your app** with name "Hall Automata"
5. **Copy the configuration object** that looks like this:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456",
  measurementId: "G-XXXXXXXXXX"
};
```

## 🔧 Step 2: Configure Your Project

### Option A: Direct Configuration (Quick Setup)
Replace the values in `src/lib/firebase/config.ts`:

```typescript
export const firebaseConfig = {
  apiKey: "YOUR_ACTUAL_API_KEY",
  authDomain: "your-actual-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-actual-project.appspot.com",
  messagingSenderId: "your-actual-sender-id",
  appId: "your-actual-app-id",
  measurementId: "your-actual-measurement-id"
};
```

### Option B: Environment Variables (Recommended for Production)
1. **Copy** `.env.local.example` to `.env.local`
2. **Replace** the placeholder values with your actual Firebase config:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_actual_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX
```

3. **Update** `src/lib/firebase/config.ts` to use environment variables:
```typescript
export const firebaseConfig = firebaseConfigFromEnv;
```

## 🔐 Step 3: Enable Authentication

1. **Go to Firebase Console** → Your Project
2. **Click "Authentication"** in left sidebar
3. **Go to "Sign-in method"** tab
4. **Enable "Email/Password"** provider
5. **Save changes**

## 📊 Step 4: Create Firestore Database

1. **Go to Firebase Console** → Your Project
2. **Click "Firestore Database"** in left sidebar
3. **Click "Create database"**
4. **Choose "Start in test mode"** (for development)
5. **Select your preferred location**
6. **Click "Done"**

## 🗂️ Step 5: Setup Storage (Optional)

1. **Go to Firebase Console** → Your Project
2. **Click "Storage"** in left sidebar
3. **Click "Get started"**
4. **Choose "Start in test mode"**
5. **Select your preferred location**

## ✅ Step 6: Test Your Setup

Run your development server:
```bash
npm run dev
```

Try signing in - you should see Firebase connection logs in the browser console.

## 🔒 Security Rules (Production Ready)

### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Hall allocation data (admin only)
    match /halls/{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

### Storage Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 🎯 Ready to Use!

Your Firebase is now configured for Hall Automata! The authentication system will work with:
- ✅ Email/Password sign-in
- ✅ User registration
- ✅ Password reset
- ✅ Secure data storage
- ✅ File uploads (if needed)

## 📱 Next Steps

1. **Test the sign-in form** on your landing page
2. **Create admin users** in Firebase Console
3. **Set up user roles** in Firestore
4. **Configure security rules** for production
5. **Add biometric data storage** collections
