// Simple Node.js script to run the database seeding
// Run this with: node src/scripts/run-seed.js

const { execSync } = require('child_process');
const path = require('path');

console.log('🌱 Hall Automata Database Seeding Script');
console.log('=========================================');
console.log('');
console.log('This script will populate your Firestore database with:');
console.log('📚 5 Faculties with 5+ departments each');
console.log('🎓 4 levels per department (ND1, ND2, HND1, HND2)');
console.log('📖 7+ courses per level');
console.log('👥 50 students per level (25 full-time + 25 part-time)');
console.log('🏢 20+ examination halls');
console.log('📅 Academic sessions and semesters');
console.log('');
console.log('⚠️  WARNING: This will create thousands of documents in Firestore!');
console.log('⚠️  Make sure you are connected to your development database.');
console.log('');

// Ask for confirmation
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Do you want to proceed? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
    console.log('');
    console.log('🚀 Starting database seeding...');
    console.log('This may take several minutes...');
    console.log('');
    
    try {
      // Run the TypeScript seeding script
      execSync('npx tsx src/scripts/seed-database.ts', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('');
      console.log('🎉 Database seeding completed successfully!');
      console.log('');
      console.log('You can now:');
      console.log('1. 📋 Go to /admin/hall-allocation to test hall allocation');
      console.log('2. 👥 View students in the admin panel');
      console.log('3. 🏢 Check halls in hall management');
      console.log('4. 📚 Create exams and test the full workflow');
      console.log('');
      
    } catch (error) {
      console.error('');
      console.error('❌ Error during seeding:', error.message);
      console.error('');
      console.error('Troubleshooting:');
      console.error('1. Make sure you have tsx installed: npm install -g tsx');
      console.error('2. Check your Firebase configuration');
      console.error('3. Ensure you have proper Firestore permissions');
      console.error('');
      process.exit(1);
    }
  } else {
    console.log('');
    console.log('❌ Seeding cancelled.');
    console.log('');
  }
  
  rl.close();
});
