import { SignInForm } from "@/components/auth/sign-in-form";
import { Badge } from "@/components/ui/badge";
import { TypographyH1, TypographyP } from "@/components/ui/typography";

export default function SignInPage() {
  console.log("📄 Regular sign-in page loaded (not intercepted)");
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Badge variant="outline" className="font-mono text-foreground border-border mb-3 mt-6">
            🎓 Final Year Project • MAPOLY Innovationmgn
          </Badge>
          <TypographyP className="text-muted-foreground">
            Smart Exam Management System
          </TypographyP>
        </div>

        {/* Sign In Form */}
        <SignInForm />

        {/* Footer */}
        <div className="text-center mt-8">
          <TypographyP className="text-sm text-muted-foreground">
            © 2024 Hall Automata • MAPOLY Innovation
          </TypographyP>
        </div>
      </div>
    </div>
  );
}
