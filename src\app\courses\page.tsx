"use client";

import { useState, useEffect } from "react";
import { StudentLayout } from "@/components/student/student-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { TypographyH2, TypographyP } from "@/components/ui/typography";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  BookOpen, 
  Search,
  Filter,
  RefreshCw,
  Loader2,
  Info,
  CheckCircle,
  Clock,
  GraduationCap
} from "lucide-react";

interface Course {
  id: string;
  courseCode: string;
  courseTitle: string;
  creditUnits: number;
  semester: string;
  level: string;
  department: string;
  isRegistered: boolean;
  status: 'active' | 'completed' | 'pending';
}

export default function StudentCoursesPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSemester, setSelectedSemester] = useState<string>("all");

  useEffect(() => {
    loadCourses();
  }, []);

  useEffect(() => {
    filterCourses();
  }, [courses, searchTerm, selectedSemester]);

  const loadCourses = async () => {
    setIsLoading(true);
    try {
      // Get student session to determine level and department
      const session = localStorage.getItem('student_session');
      if (!session) return;

      const sessionData = JSON.parse(session);
      
      // Get real courses data from Firestore
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');
      
      // First get student data to know their level and department
      const studentsRef = collection(db, 'students');
      const studentQuery = query(studentsRef, where('matricNumber', '==', sessionData.matricNumber));
      const studentSnapshot = await getDocs(studentQuery);
      
      if (!studentSnapshot.empty) {
        const studentData = studentSnapshot.docs[0].data();
        
        // Get courses for student's level and department
        const coursesRef = collection(db, 'courses');
        const coursesQuery = query(
          coursesRef, 
          where('levelName', '==', studentData.levelName),
          where('departmentName', '==', studentData.departmentName)
        );
        const coursesSnapshot = await getDocs(coursesQuery);
        
        const coursesData: Course[] = [];
        coursesSnapshot.forEach((doc) => {
          const courseData = doc.data();
          coursesData.push({
            id: doc.id,
            courseCode: courseData.courseCode,
            courseTitle: courseData.courseTitle,
            creditUnits: courseData.creditUnits || 3,
            semester: courseData.semester || 'First',
            level: courseData.levelName,
            department: courseData.departmentName,
            isRegistered: true, // Assume all courses for student's level are registered
            status: 'active'
          });
        });
        
        setCourses(coursesData);
      }
    } catch (error) {
      console.error('Error loading courses:', error);
      // Fallback with empty array
      setCourses([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filterCourses = () => {
    let filtered = courses;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.courseCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.courseTitle.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Semester filter
    if (selectedSemester !== "all") {
      filtered = filtered.filter(course => course.semester.toLowerCase() === selectedSemester.toLowerCase());
    }

    setFilteredCourses(filtered);
  };

  const getStatusBadge = (status: Course['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-600">Active</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const totalCredits = courses.reduce((sum, course) => sum + course.creditUnits, 0);
  const registeredCount = courses.filter(c => c.isRegistered).length;
  const activeCount = courses.filter(c => c.status === 'active').length;

  return (
    <StudentLayout
      title="My Courses"
      description="View your registered courses and academic information"
    >
      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700">{courses.length}</div>
            <p className="text-xs text-muted-foreground">This level</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Registered</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">{registeredCount}</div>
            <p className="text-xs text-muted-foreground">Courses enrolled</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{activeCount}</div>
            <p className="text-xs text-muted-foreground">Ongoing</p>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Credit Units</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{totalCredits}</div>
            <p className="text-xs text-muted-foreground">Total units</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/80 backdrop-blur-sm mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <Button variant="outline" onClick={loadCourses} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by course code or title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedSemester}
              onChange={(e) => setSelectedSemester(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="all">All Semesters</option>
              <option value="first">First Semester</option>
              <option value="second">Second Semester</option>
            </select>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredCourses.length} of {courses.length} courses
          </div>
        </CardContent>
      </Card>

      {/* Courses List */}
      {isLoading ? (
        <div className="text-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-green-700 mx-auto mb-4" />
          <TypographyP className="text-gray-600">Loading your courses...</TypographyP>
        </div>
      ) : filteredCourses.length === 0 ? (
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <TypographyP className="text-gray-600 mb-2">
              {searchTerm || selectedSemester !== "all" 
                ? "No courses match your search criteria"
                : "No courses found"
              }
            </TypographyP>
            <TypographyP className="text-sm text-gray-500">
              Your registered courses will appear here
            </TypographyP>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          {filteredCourses.map((course) => (
            <Card key={course.id} className="bg-white/80 backdrop-blur-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg font-bold text-gray-900">
                      {course.courseCode}
                    </CardTitle>
                    <TypographyP className="text-gray-600 mt-1">
                      {course.courseTitle}
                    </TypographyP>
                  </div>
                  {getStatusBadge(course.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2 text-sm">
                    <GraduationCap className="h-4 w-4 text-green-700" />
                    <span>{course.creditUnits} Credit Units</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span>{course.semester} Semester</span>
                  </div>
                </div>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    <strong>Level:</strong> {course.level} • <strong>Department:</strong> {course.department}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </StudentLayout>
  );
}
