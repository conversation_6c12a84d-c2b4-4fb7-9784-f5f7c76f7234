"use client";

export type UserRole = 'admin' | 'student' | 'supervisor' | 'invigilator' | 'department';

export interface SessionData {
  userId: string;
  role: UserRole;
  name: string;
  email?: string;
  matricNumber?: string;
  department?: string;
  isFirstLogin?: boolean;
  loginTime: string;
  lastActivity: string;
}

export class SessionManager {
  private static readonly SESSION_KEY = 'hall_automata_session';
  private static readonly SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Set user session
   */
  static setSession(sessionData: SessionData): void {
    if (typeof window === 'undefined') return;
    
    const session = {
      ...sessionData,
      loginTime: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
    
    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
    
    // Also set role-specific session for backward compatibility
    const roleKey = this.getRoleSessionKey(sessionData.role);
    localStorage.setItem(roleK<PERSON>, JSON.stringify(session));
  }

  /**
   * Get current session
   */
  static getSession(): SessionData | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const sessionStr = localStorage.getItem(this.SESSION_KEY);
      if (!sessionStr) return null;
      
      const session: SessionData = JSON.parse(sessionStr);
      
      // Check if session is expired
      if (this.isSessionExpired(session)) {
        this.clearSession();
        return null;
      }
      
      // Update last activity
      this.updateLastActivity();
      
      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      this.clearSession();
      return null;
    }
  }

  /**
   * Update last activity timestamp
   */
  static updateLastActivity(): void {
    if (typeof window === 'undefined') return;
    
    const session = this.getSession();
    if (session) {
      session.lastActivity = new Date().toISOString();
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
      
      // Update role-specific session too
      const roleKey = this.getRoleSessionKey(session.role);
      localStorage.setItem(roleKey, JSON.stringify(session));
    }
  }

  /**
   * Clear all sessions
   */
  static clearSession(): void {
    if (typeof window === 'undefined') return;
    
    // Clear main session
    localStorage.removeItem(this.SESSION_KEY);
    
    // Clear all role-specific sessions
    const roles: UserRole[] = ['admin', 'student', 'supervisor', 'invigilator', 'department'];
    roles.forEach(role => {
      localStorage.removeItem(this.getRoleSessionKey(role));
    });
    
    // Clear legacy sessions
    localStorage.removeItem('student_session');
    localStorage.removeItem('staff_session');
    localStorage.removeItem('supervisor_session');
    localStorage.removeItem('admin_session');
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return this.getSession() !== null;
  }

  /**
   * Check if user has specific role
   */
  static hasRole(role: UserRole): boolean {
    const session = this.getSession();
    return session?.role === role;
  }

  /**
   * Get user role
   */
  static getUserRole(): UserRole | null {
    const session = this.getSession();
    return session?.role || null;
  }

  /**
   * Check if session is expired
   */
  private static isSessionExpired(session: SessionData): boolean {
    const lastActivity = new Date(session.lastActivity);
    const now = new Date();
    return (now.getTime() - lastActivity.getTime()) > this.SESSION_TIMEOUT;
  }

  /**
   * Get role-specific session key for backward compatibility
   */
  private static getRoleSessionKey(role: UserRole): string {
    switch (role) {
      case 'student':
        return 'student_session';
      case 'supervisor':
      case 'invigilator':
        return 'staff_session';
      case 'admin':
        return 'admin_session';
      case 'department':
        return 'department_session';
      default:
        return 'user_session';
    }
  }

  /**
   * Get redirect URL based on user role and setup status
   */
  static getRedirectUrl(role: UserRole, isFirstLogin: boolean = false): string {
    if (isFirstLogin) {
      if (role === 'student') {
        return '/setup/student';
      } else if (role === 'supervisor' || role === 'invigilator') {
        return '/setup/staff';
      }
    }

    switch (role) {
      case 'admin':
        return '/admin/dashboard';
      case 'student':
        return '/dashboard';
      case 'supervisor':
      case 'invigilator':
        return '/staff/dashboard';
      case 'department':
        return '/department/dashboard';
      default:
        return '/dashboard';
    }
  }

  /**
   * Require authentication - redirect to sign-in if not authenticated
   */
  static requireAuth(router: any): SessionData | null {
    const session = this.getSession();
    if (!session) {
      router.push('/sign-in');
      return null;
    }
    return session;
  }

  /**
   * Require specific role - redirect if user doesn't have required role
   */
  static requireRole(router: any, requiredRole: UserRole): SessionData | null {
    const session = this.requireAuth(router);
    if (!session) return null;
    
    if (session.role !== requiredRole) {
      // Redirect to appropriate dashboard based on user's actual role
      const redirectUrl = this.getRedirectUrl(session.role);
      router.push(redirectUrl);
      return null;
    }
    
    return session;
  }

  /**
   * Handle logout
   */
  static logout(router: any): void {
    this.clearSession();
    router.push('/sign-in');
  }

  /**
   * Check URL access permissions
   */
  static checkUrlAccess(pathname: string, router: any): boolean {
    const session = this.getSession();
    
    // Public routes
    const publicRoutes = ['/sign-in', '/setup/student', '/setup/staff', '/'];
    if (publicRoutes.some(route => pathname.startsWith(route))) {
      return true;
    }
    
    // Require authentication for all other routes
    if (!session) {
      router.push('/sign-in');
      return false;
    }
    
    // Check role-based access
    if (pathname.startsWith('/admin') && session.role !== 'admin') {
      router.push(this.getRedirectUrl(session.role));
      return false;
    }
    
    if (pathname.startsWith('/staff') && !['supervisor', 'invigilator'].includes(session.role)) {
      router.push(this.getRedirectUrl(session.role));
      return false;
    }
    
    if (pathname.startsWith('/department') && session.role !== 'department') {
      router.push(this.getRedirectUrl(session.role));
      return false;
    }
    
    // Student routes (root level) - only allow students
    const studentRoutes = ['/dashboard', '/exams', '/allocations', '/courses'];
    if (studentRoutes.some(route => pathname === route) && session.role !== 'student') {
      router.push(this.getRedirectUrl(session.role));
      return false;
    }
    
    return true;
  }
}
