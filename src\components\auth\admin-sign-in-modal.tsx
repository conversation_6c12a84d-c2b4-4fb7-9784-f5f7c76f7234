"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { AdminSignInForm } from "./admin-sign-in-form";
import { X } from "lucide-react";

export function AdminSignInModal() {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    console.log("🔐 Admin intercepted route working! Modal overlay active.");
    
    // Animate in
    setIsVisible(true);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    
    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleEscape);
    };
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    // Small delay for animation
    setTimeout(() => {
      router.back();
    }, 150);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-200 ${
        isVisible 
          ? 'bg-black/50 backdrop-blur-sm opacity-100' 
          : 'bg-black/0 backdrop-blur-none opacity-0'
      }`}
      onClick={handleBackdropClick}
    >
      <div
        className={`relative w-full max-w-md transform transition-all duration-200 ${
          isVisible 
            ? 'scale-100 opacity-100 translate-y-0' 
            : 'scale-95 opacity-0 translate-y-4'
        }`}
      >
        {/* Close Button */}
        <button
          type="button"
          onClick={handleClose}
          className="absolute -top-2 -right-2 z-10 w-8 h-8 bg-background border border-border rounded-full flex items-center justify-center shadow-lg hover:bg-muted transition-colors"
          aria-label="Close admin sign-in modal"
        >
          <X className="h-4 w-4" />
        </button>

        {/* Admin Sign In Form */}
        <AdminSignInForm onClose={handleClose} isModal={true} />
      </div>
    </div>
  );
}
