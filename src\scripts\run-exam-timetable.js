// Simple Node.js script to run the exam timetable generation
// Run this with: node src/scripts/run-exam-timetable.js

const { execSync } = require('child_process');

console.log('📋 Hall Automata Exam Timetable Generator');
console.log('=========================================');
console.log('');
console.log('This script will create a comprehensive 5-day exam timetable:');
console.log('📅 5 examination days (Monday to Friday)');
console.log('🕐 4 time slots per day (8:00-10:00, 10:30-12:30, 14:00-16:00, 16:30-18:30)');
console.log('📚 Exams for all departments and levels');
console.log('🎯 Smart scheduling with conflict avoidance');
console.log('📊 Comprehensive timetable summary');
console.log('');
console.log('⚠️  Make sure you have run the database seeding first!');
console.log('⚠️  This will create hundreds of exam documents in Firestore.');
console.log('');

// Ask for confirmation
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Do you want to generate the exam timetable? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
    console.log('');
    console.log('🚀 Generating exam timetable...');
    console.log('This may take a few minutes...');
    console.log('');
    
    try {
      // Run the TypeScript timetable generation script
      execSync('npx tsx src/scripts/generate-exam-timetable.ts', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('');
      console.log('🎉 Exam timetable generated successfully!');
      console.log('');
      console.log('🧪 Ready for Testing:');
      console.log('1. 📋 Go to /admin/hall-allocation');
      console.log('2. 📅 Select "2024/2025" session');
      console.log('3. 📚 Choose "First Semester"');
      console.log('4. 🎯 Select multiple exams');
      console.log('5. ⚙️ Configure allocation settings');
      console.log('6. 🚀 Run hall allocation');
      console.log('7. 📊 Export results to Excel');
      console.log('');
      console.log('🎯 Perfect for testing:');
      console.log('- Mixed department allocation');
      console.log('- Study mode separation');
      console.log('- Conflict detection');
      console.log('- Bulk allocation');
      console.log('- Excel export');
      console.log('');
      
    } catch (error) {
      console.error('');
      console.error('❌ Error during timetable generation:', error.message);
      console.error('');
      console.error('Troubleshooting:');
      console.error('1. Make sure you have run database seeding first: npm run seed');
      console.error('2. Check your Firebase configuration');
      console.error('3. Ensure you have proper Firestore permissions');
      console.error('4. Verify active academic session exists');
      console.error('');
      process.exit(1);
    }
  } else {
    console.log('');
    console.log('❌ Timetable generation cancelled.');
    console.log('');
    console.log('💡 When you\'re ready:');
    console.log('1. Run database seeding first: npm run seed');
    console.log('2. Then generate timetable: npm run generate-exams');
    console.log('');
  }
  
  rl.close();
});
