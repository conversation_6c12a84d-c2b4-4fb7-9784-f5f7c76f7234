// Examination and Hall Allocation Types

export interface AcademicSession {
  id: string;
  name: string; // e.g., "2023/2024"
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Semester {
  id: string;
  sessionId: string;
  sessionName: string;
  name: string; // e.g., "First Semester", "Second Semester"
  code: string; // e.g., "1ST", "2ND"
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Course {
  id: string;
  code: string; // e.g., "CSC101", "MTH201"
  title: string; // e.g., "Introduction to Computer Science"
  departmentId: string;
  departmentName: string;
  facultyId: string;
  facultyName: string;
  isElective: boolean;
  creditUnits?: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface StudentCourseRegistration {
  id: string;
  studentId: string;
  studentMatricNumber: string;
  courseId: string;
  courseCode: string;
  courseTitle: string;
  sessionId: string;
  semesterId: string;
  levelId: string;
  levelName: string;
  departmentId: string;
  registrationDate: Date;
  status: CourseRegistrationStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export type CourseRegistrationStatus = "registered" | "dropped" | "completed";

export interface Exam {
  id: string;
  courseId: string;
  courseCode: string;
  courseTitle: string;
  sessionId: string;
  sessionName: string;
  semesterId: string;
  semesterName: string;
  facultyId: string;
  facultyName: string;
  departmentId: string;
  departmentName: string;
  levelId: string;
  levelName: string;
  examDate: Date;
  startTime: string; // e.g., "09:00"
  endTime: string; // e.g., "12:00"
  duration: number; // in minutes
  selectedHalls: string[]; // Hall IDs admin selected for this exam
  hallCapacityOverride?: Record<string, number>; // hallId -> custom capacity
  status: ExamStatus;
  studentCount?: number; // Calculated from registrations
  isAllocated: boolean;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export type ExamStatus = "draft" | "scheduled" | "allocated" | "approved" | "ongoing" | "completed" | "cancelled";

export interface HallAllocation {
  id: string;
  examId: string;
  examCode: string;
  examTitle: string;
  hallId: string;
  hallName: string;
  hallCode: string;
  sessionId: string;
  semesterId: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  allocatedCapacity: number;
  studentsAssigned: StudentHallAssignment[];
  status: AllocationStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface StudentHallAssignment {
  studentId: string;
  matricNumber: string;
  studentName?: string;
  departmentId: string;
  departmentName: string;
  levelId: string;
  levelName: string;
  studyMode: "full_time" | "part_time";
  seatNumber?: string;
  isPresent?: boolean;
  attendanceTime?: Date;
}

export type AllocationStatus = "draft" | "allocated" | "approved" | "ongoing" | "completed";

export interface ExamConflict {
  type: ConflictType;
  message: string;
  examId: string;
  examTitle: string;
  conflictingExamId?: string;
  conflictingExamTitle?: string;
  hallId?: string;
  hallName?: string;
  studentIds?: string[];
  suggestions?: string[];
}

export type ConflictType = 
  | "hall_time_conflict" 
  | "student_time_conflict" 
  | "insufficient_capacity" 
  | "no_halls_selected"
  | "invalid_time_range";

// Form Data Types
export interface SessionFormData {
  name: string;
  startDate: Date;
  endDate: Date;
}

export interface SemesterFormData {
  sessionId: string;
  name: string;
  code: string;
  startDate: Date;
  endDate: Date;
}

export interface CourseFormData {
  code: string;
  title: string;
  departmentId: string;
  isElective: boolean;
  creditUnits?: number;
  description?: string;
}

export interface ExamFormData {
  courseId: string;
  sessionId: string;
  semesterId: string;
  facultyId: string;
  departmentId: string;
  levelId: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  selectedHalls: string[];
  hallCapacityOverride?: Record<string, number>;
}

export interface AllocationSettings {
  prioritizeMixedDepartments: boolean;
  groupByStudyMode: boolean;
  randomHallSelection: boolean;
  allowPartialAllocation: boolean;
}

// Utility functions
export const calculateExamDuration = (startTime: string, endTime: string): number => {
  const start = new Date(`2000-01-01 ${startTime}`);
  const end = new Date(`2000-01-01 ${endTime}`);
  return Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
};

export const formatExamTime = (startTime: string, endTime: string): string => {
  return `${startTime} - ${endTime}`;
};

export const getExamStatusLabel = (status: ExamStatus): string => {
  switch (status) {
    case "draft": return "Draft";
    case "scheduled": return "Scheduled";
    case "allocated": return "Allocated";
    case "approved": return "Approved";
    case "ongoing": return "Ongoing";
    case "completed": return "Completed";
    case "cancelled": return "Cancelled";
    default: return status;
  }
};

export const getAllocationStatusLabel = (status: AllocationStatus): string => {
  switch (status) {
    case "draft": return "Draft";
    case "allocated": return "Allocated";
    case "approved": return "Approved";
    case "ongoing": return "Ongoing";
    case "completed": return "Completed";
    default: return status;
  }
};
