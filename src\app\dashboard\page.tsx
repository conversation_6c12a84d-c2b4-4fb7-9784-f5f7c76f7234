"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { StudentLayout } from "@/components/student/student-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyH1, TypographyH2, TypographyP } from "@/components/ui/typography";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Calendar, 
  Building, 
  Clock, 
  MapPin, 
  User, 
  BookOpen,
  GraduationCap,
  LogOut,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  Info,
  Bell,
  Search
} from "lucide-react";

interface StudentInfo {
  matricNumber: string;
  name: string;
  department: string;
  faculty: string;
  level: string;
  email?: string;
}

interface ExamAllocation {
  id: string;
  courseCode: string;
  courseTitle: string;
  examDate: Date;
  startTime: string;
  endTime: string;
  duration: number;
  hallName: string;
  hallCapacity: number;
  seatNumber?: string;
  status: 'upcoming' | 'ongoing' | 'completed';
  instructions?: string;
}

export default function StudentDashboard() {
  const [studentInfo, setStudentInfo] = useState<StudentInfo | null>(null);
  const [examAllocations, setExamAllocations] = useState<ExamAllocation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const router = useRouter();

  useEffect(() => {
    loadStudentData();
  }, []);

  const loadStudentData = async () => {
    setIsLoading(true);
    try {
      // Check if student is logged in
      const session = localStorage.getItem('student_session');
      if (!session) {
        router.push('/sign-in');
        return;
      }

      const sessionData = JSON.parse(session);
      
      // Simulate loading student data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Get real student data from Firestore
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      const studentsRef = collection(db, 'students');
      const q = query(studentsRef, where('matricNumber', '==', sessionData.matricNumber));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const studentData = querySnapshot.docs[0].data();
        setStudentInfo({
          matricNumber: studentData.matricNumber,
          name: studentData.name,
          department: studentData.departmentName,
          faculty: studentData.facultyName || "Unknown Faculty",
          level: studentData.levelName,
          email: studentData.email || sessionData.email
        });
      } else {
        // Fallback data if student not found
        setStudentInfo({
          matricNumber: sessionData.matricNumber || "Unknown",
          name: "Student",
          department: "Unknown",
          faculty: "Unknown",
          level: "Unknown",
          email: sessionData.email || "Unknown"
        });
      }

      // Get real exam allocations from Firestore
      try {
        const { collection, query, where, getDocs } = await import('firebase/firestore');
        const { db } = await import('@/lib/firebase/firebase');

        // Get student's current session data
        const session = localStorage.getItem('student_session');
        if (!session) return;

        const sessionData = JSON.parse(session);

        // First get student data to know their level and department
        const studentsRef = collection(db, 'students');
        const studentQuery = query(studentsRef, where('matricNumber', '==', sessionData.matricNumber));
        const studentSnapshot = await getDocs(studentQuery);

        if (!studentSnapshot.empty) {
          const studentData = studentSnapshot.docs[0].data();

          // Get exam timetable for student's level and department
          const examTimetableRef = collection(db, 'exam_timetable');
          const examQuery = query(
            examTimetableRef,
            where('levelName', '==', studentData.levelName),
            where('departmentName', '==', studentData.departmentName)
          );
          const examSnapshot = await getDocs(examQuery);

          const examAllocationsData: ExamAllocation[] = [];

          for (const examDoc of examSnapshot.docs) {
            const examData = examDoc.data();

            // Check if there's a hall allocation for this exam
            const hallAllocationsRef = collection(db, 'hall_allocations');
            const allocationQuery = query(
              hallAllocationsRef,
              where('examId', '==', examDoc.id)
            );
            const allocationSnapshot = await getDocs(allocationQuery);

            let hallInfo = {
              hallName: 'TBA',
              hallCapacity: 0,
              seatNumber: undefined
            };

            if (!allocationSnapshot.empty) {
              const allocationData = allocationSnapshot.docs[0].data();
              hallInfo = {
                hallName: allocationData.hallName || 'TBA',
                hallCapacity: allocationData.hallCapacity || 0,
                seatNumber: allocationData.seatNumber
              };
            }

            // Determine status based on exam date
            const examDate = examData.examDate.toDate();
            const now = new Date();
            let status: 'upcoming' | 'ongoing' | 'completed' = 'upcoming';

            if (examDate < now) {
              status = 'completed';
            } else if (examDate.toDateString() === now.toDateString()) {
              status = 'ongoing';
            }

            examAllocationsData.push({
              id: examDoc.id,
              courseCode: examData.courseCode,
              courseTitle: examData.courseTitle,
              examDate: examDate,
              startTime: examData.startTime,
              endTime: examData.endTime,
              duration: examData.duration || 180,
              hallName: hallInfo.hallName,
              hallCapacity: hallInfo.hallCapacity,
              seatNumber: hallInfo.seatNumber,
              status: status,
              instructions: examData.instructions || 'Bring your student ID and writing materials.'
            });
          }

          setExamAllocations(examAllocationsData);
        }
      } catch (error) {
        console.error('Error loading exam allocations:', error);
        // Fallback to empty array
        setExamAllocations([]);
      }

    } catch (error) {
      setError("Failed to load student data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('student_session');
    router.push('/sign-in');
  };

  const getStatusBadge = (status: ExamAllocation['status']) => {
    switch (status) {
      case 'upcoming':
        return <Badge className="bg-blue-600">Upcoming</Badge>;
      case 'ongoing':
        return <Badge className="bg-green-600">Ongoing</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    return new Date(`2024-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const upcomingExams = examAllocations.filter(exam => exam.status === 'upcoming');
  const completedExams = examAllocations.filter(exam => exam.status === 'completed');

  if (isLoading) {
    return (
      <StudentLayout title="Dashboard" description="Welcome to your student portal">
        <div className="text-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <TypographyP className="text-gray-600">Loading your dashboard...</TypographyP>
        </div>
      </StudentLayout>
    );
  }

  return (
    <StudentLayout title="Dashboard" description="Overview of your academic activities">
      <div>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Welcome Section */}
        {studentInfo && (
          <div className="mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <TypographyH1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                    Welcome back, {studentInfo.name}!
                  </TypographyH1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {studentInfo.matricNumber}
                    </div>
                    <div className="flex items-center gap-1">
                      <BookOpen className="h-4 w-4" />
                      {studentInfo.department}
                    </div>
                    <div className="flex items-center gap-1">
                      <GraduationCap className="h-4 w-4" />
                      {studentInfo.level}
                    </div>
                  </div>
                </div>
                <Button variant="outline" onClick={loadStudentData} className="mt-4 sm:mt-0">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 lg:gap-6 mb-8">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-700">{examAllocations.length}</div>
              <p className="text-xs text-muted-foreground">This semester</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-emerald-600">{upcomingExams.length}</div>
              <p className="text-xs text-muted-foreground">Exams scheduled</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">{completedExams.length}</div>
              <p className="text-xs text-muted-foreground">Exams taken</p>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Exams */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <TypographyH2 className="text-xl font-bold text-gray-900">
              Upcoming Exams
            </TypographyH2>
            {upcomingExams.length > 0 && (
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                {upcomingExams.length} scheduled
              </Badge>
            )}
          </div>

          {upcomingExams.length === 0 ? (
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <TypographyP className="text-gray-600 mb-2">No upcoming exams</TypographyP>
                <TypographyP className="text-sm text-gray-500">
                  Your exam schedule will appear here when available
                </TypographyP>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {upcomingExams.map((exam) => (
                <Card key={exam.id} className="bg-white/80 backdrop-blur-sm hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg font-bold text-gray-900">
                          {exam.courseCode}
                        </CardTitle>
                        <TypographyP className="text-gray-600 mt-1">
                          {exam.courseTitle}
                        </TypographyP>
                      </div>
                      {getStatusBadge(exam.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{formatDate(exam.examDate)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-4 w-4 text-green-600" />
                        <span>{formatTime(exam.startTime)} - {formatTime(exam.endTime)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Building className="h-4 w-4 text-purple-600" />
                        <span className="font-medium">{exam.hallName}</span>
                      </div>
                      {exam.seatNumber && (
                        <div className="flex items-center gap-2 text-sm">
                          <MapPin className="h-4 w-4 text-orange-600" />
                          <span>Seat: <span className="font-mono font-bold">{exam.seatNumber}</span></span>
                        </div>
                      )}
                    </div>
                    
                    {exam.instructions && (
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          {exam.instructions}
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Recent Exams */}
        {completedExams.length > 0 && (
          <div>
            <TypographyH2 className="text-xl font-bold text-gray-900 mb-6">
              Recent Exams
            </TypographyH2>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-0">
                <div className="divide-y divide-gray-200">
                  {completedExams.slice(0, 3).map((exam) => (
                    <div key={exam.id} className="p-4 hover:bg-gray-50/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">
                            {exam.courseCode} - {exam.courseTitle}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {formatDate(exam.examDate)} • {exam.hallName}
                          </div>
                        </div>
                        {getStatusBadge(exam.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </StudentLayout>
  );
}
