import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  TypographyH1,
  TypographyH2,
  TypographyH3,
  TypographyH4,
  TypographyP,
  TypographyLead,
  TypographyMatricNumber,
  TypographyAccent,
  TypographyCreative,
  TypographySystem,
  TypographyMuted
} from "@/components/ui/typography";
import {
  GraduationCap,
  Users,
  Calendar,
  Shield,
  Fingerprint,
  BarChart3,
  CheckCircle,
  Clock,
  Database,
  Building,
  Smartphone,
  TrendingUp,
  Globe,
  Star
} from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Navigation Header */}
      <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container-fluid mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <div>
                <TypographyH3 className="font-sans text-foreground text-xl">Hall Automata</TypographyH3>
                <TypographySystem className="text-xs text-muted-foreground">Smart Exam Management</TypographySystem>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#features" className="font-sans text-sm hover:text-primary transition-colors">Features</a>
              <a href="#technology" className="font-sans text-sm hover:text-primary transition-colors">Technology</a>
              <a href="#about" className="font-sans text-sm hover:text-primary transition-colors">About</a>
              <Link href="/sign-in">
                <Button size="sm" className="font-sans">
                  <Shield className="mr-2 h-4 w-4" />
                  Sign In
                </Button>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-muted/30 to-background">
        {/* Beautiful Geometric Background */}
        <div className="absolute inset-0 flex items-center justify-center opacity-40">
          <svg fill="none" height="312" viewBox="0 0 400 312" width="400" className="w-full h-full max-w-4xl" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <style>
                {`:root {
                  --geist-background: #fff;
                  --geist-foreground: #000;
                }
                @media (prefers-color-scheme: dark) {
                  :root {
                    --geist-background: #000;
                    --geist-foreground: #fff;
                  }
                }`}
              </style>
            </defs>
            <path
              d="M140 36L140 23C140 20.7909 138.209 19 136 19L62.0001 19"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M116 62L16.0001 62"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M284 62.0076L559 62.0076C561.209 62.0076 563 63.7985 563 66.0076L563 195.5"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M116 81.9318L-160 81.9318C-162.209 81.9318 -164 83.7226 -164 85.9317L-164 105.841"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M284.5 82L370 82C372.209 82 374 83.7909 374 86L374 301.883C374 304.092 372.209 305.883 370 305.883L240 305.883C237.791 305.883 236 307.674 236 309.883L236 312"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M140 108L140 117.825C140 120.034 138.209 121.825 136 121.825L67.0001 121.825"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M164 36L164 4"
              stroke="url(#paint0_linear_114_9407)"
              strokeOpacity="0.1"
            />
            <path
              d="M164 172.5L164 108"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M188 36L188 4"
              stroke="url(#paint1_linear_114_9407)"
              strokeOpacity="0.1"
            />
            <path
              d="M188 137.689L188 107.803"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M212 36.1364L212 4.25757"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M212 138L212 108"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M236 36L236 8C236 5.79086 237.791 4 240 4L306 4"
              stroke="url(#paint2_linear_114_9407)"
              strokeOpacity="0.1"
            />
            <path
              d="M236 107.803L236 133.689C236 135.898 237.791 137.689 240 137.689L312 137.689"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M260 36.1364L260 28.1819C260 25.9727 261.791 24.1819 264 24.1819L638 24.1819C640.209 24.1819 642 25.9727 642 28.1819L642 203.5"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <path
              d="M260 107.803L260 116.754C260 118.963 261.791 120.754 264 120.754L312 120.754"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="212"
              cy="4.25755"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M215.5 4.25755C215.5 6.18039 213.935 7.7424 212 7.7424C210.065 7.7424 208.5 6.18039 208.5 4.25755C208.5 2.33471 210.065 0.772705 212 0.772705C213.935 0.772705 215.5 2.33471 215.5 4.25755Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="60.0001"
              cy="18.9848"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M63.5001 18.9848C63.5001 20.9077 61.9349 22.4697 60.0001 22.4697C58.0653 22.4697 56.5001 20.9077 56.5001 18.9848C56.5001 17.062 58.0653 15.5 60.0001 15.5C61.9349 15.5 63.5001 17.062 63.5001 18.9848Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="20.0001"
              cy="61.9848"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M23.5001 61.9848C23.5001 63.9077 21.9349 65.4697 20.0001 65.4697C18.0653 65.4697 16.5001 63.9077 16.5001 61.9848C16.5001 60.062 18.0653 58.5 20.0001 58.5C21.9349 58.5 23.5001 60.062 23.5001 61.9848Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="188"
              cy="137.985"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M191.5 137.985C191.5 139.908 189.935 141.47 188 141.47C186.065 141.47 184.5 139.908 184.5 137.985C184.5 136.062 186.065 134.5 188 134.5C189.935 134.5 191.5 136.062 191.5 137.985Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="66.0001"
              cy="121.985"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M69.5001 121.985C69.5001 123.908 67.9349 125.47 66.0001 125.47C64.0653 125.47 62.5001 123.908 62.5001 121.985C62.5001 120.062 64.0653 118.5 66.0001 118.5C67.9349 118.5 69.5001 120.062 69.5001 121.985Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="212"
              cy="137.985"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M215.5 137.985C215.5 139.908 213.935 141.47 212 141.47C210.065 141.47 208.5 139.908 208.5 137.985C208.5 136.062 210.065 134.5 212 134.5C213.935 134.5 215.5 136.062 215.5 137.985Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="312"
              cy="137.689"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M315.5 137.689C315.5 139.612 313.935 141.174 312 141.174C310.065 141.174 308.5 139.612 308.5 137.689C308.5 135.767 310.065 134.205 312 134.205C313.935 134.205 315.5 135.767 315.5 137.689Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <ellipse
              cx="312"
              cy="120.754"
              fill="var(--geist-background)"
              rx="4"
              ry="3.98485"
            />
            <path
              d="M315.5 120.754C315.5 122.677 313.935 124.239 312 124.239C310.065 124.239 308.5 122.677 308.5 120.754C308.5 118.831 310.065 117.269 312 117.269C313.935 117.269 315.5 118.831 315.5 120.754Z"
              stroke="var(--geist-foreground)"
              strokeOpacity="0.1"
            />
            <rect
              fill="url(#paint3_linear_114_9407)"
              height="60"
              rx="0.996212"
              transform="rotate(90 368 61)"
              width="1.99242"
              x="368"
              y="61"
            />
            <rect
              fill="url(#paint4_linear_114_9407)"
              height="60"
              rx="0.996212"
              transform="matrix(0 1 1 0 38 81)"
              width="1.99242"
            />
            <rect
              fill="url(#paint5_linear_114_9407)"
              height="28"
              rx="1"
              transform="matrix(1 0 0 -1 163 148)"
              width="2"
            />
            <defs>
              <linearGradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_114_9407"
                x1="164.5"
                x2="164.5"
                y1="4"
                y2="36"
              >
                <stop stopOpacity="0" />
                <stop offset="1" />
              </linearGradient>
              <linearGradient
                gradientUnits="userSpaceOnUse"
                id="paint1_linear_114_9407"
                x1="188.5"
                x2="188.5"
                y1="4"
                y2="36"
              >
                <stop stopOpacity="0" />
                <stop offset="1" />
              </linearGradient>
              <linearGradient
                gradientUnits="userSpaceOnUse"
                id="paint2_linear_114_9407"
                x1="306"
                x2="286.061"
                y1="4"
                y2="55.4142"
              >
                <stop stopOpacity="0" />
                <stop offset="1" />
              </linearGradient>
              <linearGradient
                gradientUnits="userSpaceOnUse"
                id="paint3_linear_114_9407"
                x1="369.799"
                x2="369.797"
                y1="121.239"
                y2="58.4805"
              >
                <stop stopColor="#FF4A81" />
                <stop offset="0.21875" stopColor="#DF6CF7" />
                <stop offset="1" stopColor="#0196FF" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                gradientUnits="userSpaceOnUse"
                id="paint4_linear_114_9407"
                x1="1.79948"
                x2="1.79741"
                y1="60.2391"
                y2="-2.51953"
              >
                <stop stopColor="#FF7432" />
                <stop offset="0.21875" stopColor="#F7CC4B" />
                <stop offset="1" stopColor="#F7CC4B" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                gradientUnits="userSpaceOnUse"
                id="paint5_linear_114_9407"
                x1="1.80632"
                x2="1.80587"
                y1="28.1116"
                y2="-1.17578"
              >
                <stop stopColor="#2EB9DF" />
                <stop offset="0.21875" stopColor="#61DAFB" />
                <stop offset="1" stopColor="#61DAFB" stopOpacity="0" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        {/* Content Layer */}
        <div className="container mx-auto px-4 py-20 text-center relative z-10">
          <div className="max-w-5xl mx-auto space-y-8">
            <Badge variant="outline" className="font-mono text-foreground border-border mb-6">
              🎓 Final Year Project • MAPOLY Innovation
            </Badge>

            <div className="space-y-4">
              <TypographyCreative className="text-4xl lg:text-6xl text-foreground block">
                Hall
              </TypographyCreative>
              <TypographyH1 className="text-5xl lg:text-7xl font-bold" style={{ color: '#262626' }}>
                Automata
              </TypographyH1>
            </div>

            <TypographyLead className="text-xl lg:text-2xl max-w-3xl mx-auto">
              Revolutionary <TypographyAccent>Smart Exam Hall Allocation</TypographyAccent> &
              <TypographyAccent> Biometric Attendance System</TypographyAccent> for Modern Polytechnic Education
            </TypographyLead>

            <TypographyP className="max-w-2xl mx-auto text-muted-foreground">
              Eliminating manual scheduling errors, preventing exam fraud, and streamlining
              administrative processes through cutting-edge automation and biometric technology.
            </TypographyP>

            <div className="flex gap-4 justify-center flex-wrap pt-4">
              <Link href="/sign-in">
                <Button size="lg" className="font-sans text-lg px-8 py-6">
                  <GraduationCap className="mr-2 h-5 w-5" />
                  Get Started
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="font-sans text-lg px-8 py-6">
                <Shield className="mr-2 h-5 w-5" />
                Learn More
              </Button>
            </div>

            <div className="flex items-center justify-center gap-8 pt-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span className="font-mono">100% Automated</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-primary" />
                <span className="font-mono">Biometric Secure</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-primary" />
                <span className="font-mono">Real-time Processing</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem Statement */}
      {/* <section className="container mx-auto px-4 py-16 bg-muted/30">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          <TypographyH2>
            Solving Real Academic Challenges
          </TypographyH2>
          <TypographyP className="text-lg text-muted-foreground">
            Traditional exam management faces critical issues that Hall Automata addresses with modern technology
          </TypographyP>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <Card className="bg-background border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="font-sans text-red-600">Manual Errors</CardTitle>
              </CardHeader>
              <CardContent>
                <TypographyP className="text-sm text-muted-foreground">
                  Human errors in hall allocation, capacity miscalculations, and scheduling conflicts
                </TypographyP>
              </CardContent>
            </Card>

            <Card className="bg-background border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="font-sans text-orange-600">Identity Fraud</CardTitle>
              </CardHeader>
              <CardContent>
                <TypographyP className="text-sm text-muted-foreground">
                  Students impersonating others, proxy attendance, and exam security breaches
                </TypographyP>
              </CardContent>
            </Card>

            <Card className="bg-background border-l-4 border-l-yellow-500">
              <CardHeader>
                <CardTitle className="font-sans text-yellow-600">Time Wastage</CardTitle>
              </CardHeader>
              <CardContent>
                <TypographyP className="text-sm text-muted-foreground">
                  Hours spent on manual allocation, attendance verification, and report generation
                </TypographyP>
              </CardContent>
            </Card>
          </div>
        </div>
      </section> */}

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <TypographyH2 className="mb-4">
            <TypographyAccent>Professional</TypographyAccent> Solution Features
          </TypographyH2>
          <TypographyP className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Comprehensive automation powered by modern web technologies and biometric security
          </TypographyP>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-sans">Smart Student Management</CardTitle>
                  <CardDescription className="font-sans">Comprehensive database system</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm mb-3">
                Manage student records with automated matric number validation like{" "}
                <TypographyMatricNumber>CS/2021/001</TypographyMatricNumber>
              </TypographyP>
              <ul className="text-xs text-muted-foreground space-y-1 font-open-sans">
                <li>• CSV import/export functionality</li>
                <li>• Department and level organization</li>
                <li>• Study mode classification</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-nunito">Automated Hall Allocation</CardTitle>
                  <CardDescription className="font-open-sans">Intelligent scheduling engine</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm mb-3">
                AI-powered allocation based on capacity, student grouping, and optimization algorithms
              </TypographyP>
              <ul className="text-xs text-muted-foreground space-y-1 font-open-sans">
                <li>• Capacity optimization</li>
                <li>• Conflict detection & resolution</li>
                <li>• Printable allocation sheets</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Fingerprint className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-nunito">Biometric Authentication</CardTitle>
                  <CardDescription className="font-open-sans">Advanced security system</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm mb-3">
                Fingerprint-based student verification with encrypted template storage
              </TypographyP>
              <ul className="text-xs text-muted-foreground space-y-1 font-open-sans">
                <li>• Multi-scanner support</li>
                <li>• Encrypted data storage</li>
                <li>• Offline capability</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <BarChart3 className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-nunito">Real-time Analytics</CardTitle>
                  <CardDescription className="font-open-sans">Live monitoring dashboard</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm mb-3">
                Live attendance tracking with comprehensive reporting and analytics
              </TypographyP>
              <ul className="text-xs text-muted-foreground space-y-1 font-open-sans">
                <li>• Live attendance monitoring</li>
                <li>• Automated report generation</li>
                <li>• Export in multiple formats</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Smartphone className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-nunito">Multi-Platform Access</CardTitle>
                  <CardDescription className="font-open-sans">Responsive web application</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm mb-3">
                Access from any device with role-based interfaces for different user types
              </TypographyP>
              <ul className="text-xs text-muted-foreground space-y-1 font-open-sans">
                <li>• Admin, Invigilator, Student portals</li>
                <li>• Mobile-responsive design</li>
                <li>• Progressive Web App</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Database className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-nunito">Secure Data Management</CardTitle>
                  <CardDescription className="font-open-sans">Enterprise-grade security</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TypographyP className="text-sm mb-3">
                Cloud-based storage with encryption, backup, and compliance features
              </TypographyP>
              <ul className="text-xs text-muted-foreground space-y-1 font-open-sans">
                <li>• Encrypted biometric templates</li>
                <li>• Automatic data backup</li>
                <li>• GDPR compliance ready</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Technology Stack */}
      <section id="technology" className="container mx-auto px-4 py-20 ">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <TypographyH2 className="mb-4">
              <TypographyAccent>Modern</TypographyAccent> Technology Stack
            </TypographyH2>
            <TypographyP className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Built with industry-standard technologies and best practices for scalability, security, and performance
            </TypographyP>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Globe className="h-8 w-8 text-blue-600" />
              </div>
              <TypographyH3 className="font-nunito text-lg mb-2">Frontend</TypographyH3>
              <div className="space-y-1 text-sm text-muted-foreground font-open-sans">
                <p>Next.js 15</p>
                <p>TypeScript</p>
                <p>Tailwind CSS</p>
                <p>shadcn/ui</p>
              </div>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Database className="h-8 w-8 text-green-600" />
              </div>
              <TypographyH3 className="font-nunito text-lg mb-2">Backend</TypographyH3>
              <div className="space-y-1 text-sm text-muted-foreground font-open-sans">
                <p>Firebase Auth</p>
                <p>Cloud Firestore</p>
                <p>Firebase Storage</p>
                <p>API Routes</p>
              </div>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Fingerprint className="h-8 w-8 text-purple-600" />
              </div>
              <TypographyH3 className="font-nunito text-lg mb-2">Biometric</TypographyH3>
              <div className="space-y-1 text-sm text-muted-foreground font-open-sans">
                <p>WebUSB API</p>
                <p>Web Bluetooth</p>
                <p>Encryption</p>
                <p>Multi-scanner</p>
              </div>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
              <TypographyH3 className="font-nunito text-lg mb-2">Analytics</TypographyH3>
              <div className="space-y-1 text-sm text-muted-foreground font-open-sans">
                <p>Real-time Data</p>
                <p>Chart.js</p>
                <p>Export Tools</p>
                <p>Reporting</p>
              </div>
            </Card>
          </div>

          <div className="mt-12 text-center">
            <TypographyP className="text-muted-foreground mb-6">
              Professional development practices with modern tooling
            </TypographyP>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="outline" className="font-ubuntu">Git Version Control</Badge>
              <Badge variant="outline" className="font-ubuntu">ESLint + Prettier</Badge>
              <Badge variant="outline" className="font-ubuntu">TypeScript Strict Mode</Badge>
              <Badge variant="outline" className="font-ubuntu">Responsive Design</Badge>
              <Badge variant="outline" className="font-ubuntu">PWA Ready</Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Project Details & Academic Context */}
      <section id="about" className="container mx-auto px-4 py-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge variant="outline" className="font-ubuntu text-primary border-primary/20 mb-6">
                🎓 Academic Excellence
              </Badge>
              <TypographyH2 className="mb-6">
                Final Year Project with <TypographyAccent>Real-World Impact</TypographyAccent>
              </TypographyH2>
              <TypographyP className="text-lg mb-6">
                Hall Automata represents the culmination of advanced computer science education,
                demonstrating proficiency in full-stack development, system architecture,
                and real-world problem solving.
              </TypographyP>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <TypographyP className="font-semibold mb-1">Academic Innovation</TypographyP>
                    <TypographyP className="text-sm text-muted-foreground">
                      Solving real institutional challenges with modern technology
                    </TypographyP>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <TypographyP className="font-semibold mb-1">Professional Standards</TypographyP>
                    <TypographyP className="text-sm text-muted-foreground">
                      Industry-grade architecture and development practices
                    </TypographyP>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <TypographyP className="font-semibold mb-1">Scalable Solution</TypographyP>
                    <TypographyP className="text-sm text-muted-foreground">
                      Designed for growth from single institution to multi-campus deployment
                    </TypographyP>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <Card className="p-6 border-l-4 border-l-primary">
                <div className="flex items-center gap-3 mb-4">
                  <Building className="h-6 w-6 text-primary" />
                  <TypographyH3 className="font-nunito">Case Study Institution</TypographyH3>
                </div>
                <TypographyP className="text-sm text-muted-foreground mb-4">
                  Initially developed and tested for a specific polytechnic, demonstrating
                  real-world application and measurable impact on examination management processes.
                </TypographyP>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <TypographyP className="text-2xl font-bold text-primary font-nunito">500+</TypographyP>
                    <TypographyP className="text-xs text-muted-foreground font-ubuntu">Students Managed</TypographyP>
                  </div>
                  <div>
                    <TypographyP className="text-2xl font-bold text-primary font-nunito">95%</TypographyP>
                    <TypographyP className="text-xs text-muted-foreground font-ubuntu">Error Reduction</TypographyP>
                  </div>
                </div>
              </Card>

              <Card className="p-6 border-l-4 border-l-green-500">
                <div className="flex items-center gap-3 mb-4">
                  <Star className="h-6 w-6 text-green-600" />
                  <TypographyH3 className="font-nunito">Project Achievements</TypographyH3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <TypographyP className="text-sm">Complete system architecture design</TypographyP>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <TypographyP className="text-sm">Advanced biometric integration</TypographyP>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <TypographyP className="text-sm">Real-time data processing</TypographyP>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <TypographyP className="text-sm">Professional UI/UX design</TypographyP>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-muted/50 via-muted/30 to-muted/50 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <TypographyH2 className="text-4xl lg:text-5xl">
              Ready to <TypographyAccent>Experience</TypographyAccent> Hall Automata?
            </TypographyH2>
            <TypographyP className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Join MAPOLY in revolutionizing examination management with
              intelligent automation and biometric security.
            </TypographyP>

            <div className="flex gap-6 justify-center flex-wrap pt-6">
              <Link href="/sign-in">
                <Button size="lg" className="font-sans text-lg px-10 py-6">
                  <GraduationCap className="mr-2 h-5 w-5" />
                  Get Started
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="font-sans text-lg px-10 py-6">
                <Shield className="mr-2 h-5 w-5" />
                Learn More
              </Button>
            </div>

            <div className="pt-8">
              <TypographyP className="text-sm text-muted-foreground mb-4">
                Secure authentication required • Role-based access control
              </TypographyP>
              <div className="flex justify-center items-center gap-8 opacity-60">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-ubuntu text-sm">Biometric Security</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span className="font-ubuntu text-sm">Multi-Role Access</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-ubuntu text-sm">Real-time System</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-background py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-background rounded-lg flex items-center justify-center border border-background/20">
                  <GraduationCap className="h-6 w-6 text-foreground" />
                </div>
                <div>
                  <TypographyH3 className="font-nunito text-background text-xl">Hall Automata</TypographyH3>
                  <TypographySystem className="text-xs text-background/60">Smart Exam Management System</TypographySystem>
                </div>
              </div>
              <TypographyP className="text-background/80 mb-6 max-w-md">
                Revolutionizing examination management through intelligent automation,
                biometric security, and modern web technologies.
              </TypographyP>
              <div className="flex gap-4">
                <Badge variant="outline" className="text-background border-background/20">
                  🎓 Final Year Project
                </Badge>
                <Badge variant="outline" className="text-background border-background/20">
                  🏆 Academic Excellence
                </Badge>
              </div>
            </div>

            <div>
              <TypographyH3 className="font-nunito text-background mb-4">Features</TypographyH3>
              <ul className="space-y-2 text-sm text-background/70 font-open-sans">
                <li>Smart Hall Allocation</li>
                <li>Biometric Authentication</li>
                <li>Real-time Monitoring</li>
                <li>Automated Reporting</li>
                <li>Multi-platform Access</li>
              </ul>
            </div>

            <div>
              <TypographyH3 className="font-nunito text-background mb-4">Technology</TypographyH3>
              <ul className="space-y-2 text-sm text-background/70 font-open-sans">
                <li>Next.js 15</li>
                <li>TypeScript</li>
                <li>Firebase</li>
                <li>Tailwind CSS</li>
                <li>WebUSB API</li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-background/20" />

          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <TypographyP className="text-background/60 text-sm">
              © 2024 Hall Automata. Built with ❤️ for modern examination management.
            </TypographyP>
            <div className="flex gap-6 text-sm text-background/60 font-open-sans">
              <a href="#" className="hover:text-primary transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-primary transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-primary transition-colors">Documentation</a>
              <Link href="/admin" className="hover:text-primary transition-colors">
                Admin Portal
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
