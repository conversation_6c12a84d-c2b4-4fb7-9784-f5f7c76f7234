"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Building, 
  Users, 
  Download, 
  FileSpreadsheet, 
  Eye,
  Search,
  Filter,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { HallAllocation } from "@/types/exam";

interface AllocationResult {
  examId: string;
  examCode: string;
  examTitle: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  allocations?: HallAllocation[];
  error?: string;
  studentCount?: number;
  allocatedCount?: number;
}

interface HallAllocationResultsProps {
  results: AllocationResult[];
  sessionName: string;
  semesterName?: string;
}

export function HallAllocationResults({ results, sessionName, semesterName }: HallAllocationResultsProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedExamId, setSelectedExamId] = useState<string | null>(null);

  // Flatten all allocations for table view
  const allAllocations = results
    .filter(result => result.allocations)
    .flatMap(result => 
      result.allocations!.map(allocation => ({
        ...allocation,
        examCode: result.examCode,
        examTitle: result.examTitle,
        resultStatus: result.status
      }))
    );

  // Filter allocations
  const filteredAllocations = allAllocations.filter(allocation => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      if (!allocation.examCode.toLowerCase().includes(searchLower) &&
          !allocation.examTitle.toLowerCase().includes(searchLower) &&
          !allocation.hallName.toLowerCase().includes(searchLower) &&
          !allocation.hallCode.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Status filter
    if (statusFilter !== "all" && allocation.status !== statusFilter) {
      return false;
    }

    return true;
  });

  // Export to Excel
  const exportToExcel = () => {
    const csvContent = generateCSVContent();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `hall_allocation_${sessionName.replace(/[^a-zA-Z0-9]/g, '_')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generateCSVContent = () => {
    const headers = [
      'Session',
      'Semester',
      'Exam Code',
      'Exam Title',
      'Exam Date',
      'Exam Time',
      'Hall Code',
      'Hall Name',
      'Allocated Students',
      'Student Details'
    ];

    const rows = filteredAllocations.map(allocation => [
      sessionName,
      semesterName || 'All Semesters',
      allocation.examCode,
      allocation.examTitle,
      allocation.examDate.toLocaleDateString(),
      `${allocation.startTime} - ${allocation.endTime}`,
      allocation.hallCode,
      allocation.hallName,
      allocation.allocatedCapacity.toString(),
      allocation.studentsAssigned.map(s => `${s.matricNumber} (${s.studentName})`).join('; ')
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    return csvContent;
  };

  // Generate detailed Excel with student breakdown
  const exportDetailedExcel = () => {
    const csvContent = generateDetailedCSVContent();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `detailed_hall_allocation_${sessionName.replace(/[^a-zA-Z0-9]/g, '_')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generateDetailedCSVContent = () => {
    const headers = [
      'Session',
      'Semester',
      'Exam Code',
      'Exam Title',
      'Exam Date',
      'Exam Time',
      'Hall Code',
      'Hall Name',
      'Student Matric Number',
      'Student Name',
      'Department',
      'Level',
      'Study Mode'
    ];

    const rows: string[][] = [];
    
    filteredAllocations.forEach(allocation => {
      allocation.studentsAssigned.forEach(student => {
        rows.push([
          sessionName,
          semesterName || 'All Semesters',
          allocation.examCode,
          allocation.examTitle,
          allocation.examDate.toLocaleDateString(),
          `${allocation.startTime} - ${allocation.endTime}`,
          allocation.hallCode,
          allocation.hallName,
          student.matricNumber,
          student.studentName,
          student.departmentName,
          student.levelName,
          student.studyMode
        ]);
      });
    });

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    return csvContent;
  };

  const completedResults = results.filter(r => r.status === 'completed');
  const errorResults = results.filter(r => r.status === 'error');
  const totalStudentsAllocated = completedResults.reduce((sum, r) => sum + (r.allocatedCount || 0), 0);
  const totalHallsUsed = allAllocations.length;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{results.length}</div>
            <p className="text-xs text-muted-foreground">
              {completedResults.length} completed, {errorResults.length} errors
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Students Allocated</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStudentsAllocated}</div>
            <p className="text-xs text-muted-foreground">Across all exams</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Halls Used</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalHallsUsed}</div>
            <p className="text-xs text-muted-foreground">Total allocations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {results.length > 0 ? Math.round((completedResults.length / results.length) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">Allocation success</p>
          </CardContent>
        </Card>
      </div>

      {/* Export Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Options
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button onClick={exportToExcel} variant="outline">
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Export Summary (CSV)
            </Button>
            <Button onClick={exportDetailedExcel} variant="outline">
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Export Detailed (CSV)
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Summary export includes hall allocations. Detailed export includes individual student assignments.
          </p>
        </CardContent>
      </Card>

      {/* Error Results */}
      {errorResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Allocation Errors ({errorResults.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {errorResults.map((result) => (
                <div key={result.examId} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline" className="font-mono text-xs">
                      {result.examCode}
                    </Badge>
                    <span className="font-medium text-red-800">{result.examTitle}</span>
                  </div>
                  <p className="text-sm text-red-700">{result.error}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Allocation Results Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Hall Allocation Results ({filteredAllocations.length})
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search-results">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search-results"
                  placeholder="Search exams, halls..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results Table */}
          {filteredAllocations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No allocation results found</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-left p-3 font-medium">Exam</th>
                      <th className="text-left p-3 font-medium">Hall</th>
                      <th className="text-left p-3 font-medium">Date & Time</th>
                      <th className="text-left p-3 font-medium">Students</th>
                      <th className="text-left p-3 font-medium">Departments</th>
                      <th className="text-left p-3 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAllocations.map((allocation, index) => {
                      const departmentStats = allocation.studentsAssigned.reduce((acc, student) => {
                        if (!acc[student.departmentId]) {
                          acc[student.departmentId] = {
                            name: student.departmentName,
                            count: 0,
                            fullTime: 0,
                            partTime: 0
                          };
                        }
                        acc[student.departmentId].count++;
                        if (student.studyMode === 'full_time') {
                          acc[student.departmentId].fullTime++;
                        } else {
                          acc[student.departmentId].partTime++;
                        }
                        return acc;
                      }, {} as Record<string, { name: string; count: number; fullTime: number; partTime: number }>);

                      return (
                        <tr key={`${allocation.examId}-${allocation.hallId}-${index}`} className="border-b hover:bg-gray-50">
                          <td className="p-3">
                            <div>
                              <Badge variant="outline" className="font-mono text-xs mb-1">
                                {allocation.examCode}
                              </Badge>
                              <div className="font-medium">{allocation.examTitle}</div>
                            </div>
                          </td>
                          <td className="p-3">
                            <div>
                              <Badge variant="outline" className="font-mono text-xs mb-1">
                                {allocation.hallCode}
                              </Badge>
                              <div className="font-medium">{allocation.hallName}</div>
                            </div>
                          </td>
                          <td className="p-3">
                            <div className="text-sm">
                              <div>{allocation.examDate.toLocaleDateString()}</div>
                              <div className="text-muted-foreground">
                                {allocation.startTime} - {allocation.endTime}
                              </div>
                            </div>
                          </td>
                          <td className="p-3">
                            <div className="text-sm">
                              <div className="font-medium">{allocation.allocatedCapacity} students</div>
                              <div className="text-muted-foreground">
                                {Object.values(departmentStats).reduce((sum, dept) => sum + dept.fullTime, 0)} FT, {' '}
                                {Object.values(departmentStats).reduce((sum, dept) => sum + dept.partTime, 0)} PT
                              </div>
                            </div>
                          </td>
                          <td className="p-3">
                            <div className="space-y-1">
                              {Object.entries(departmentStats).slice(0, 2).map(([deptId, stats]) => (
                                <Badge key={deptId} variant="secondary" className="text-xs">
                                  {stats.name}: {stats.count}
                                </Badge>
                              ))}
                              {Object.keys(departmentStats).length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{Object.keys(departmentStats).length - 2} more
                                </Badge>
                              )}
                            </div>
                          </td>
                          <td className="p-3">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedExamId(
                                selectedExamId === allocation.examId ? null : allocation.examId
                              )}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              {selectedExamId === allocation.examId ? 'Hide' : 'View'}
                            </Button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Details Modal/Expandable Section */}
      {selectedExamId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Student Details - {filteredAllocations.find(a => a.examId === selectedExamId)?.examCode}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredAllocations
                .filter(a => a.examId === selectedExamId)
                .map((allocation, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline" className="font-mono text-xs">
                        {allocation.hallCode}
                      </Badge>
                      <span className="font-medium">{allocation.hallName}</span>
                      <Badge variant="secondary">
                        {allocation.allocatedCapacity} students
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
                      {allocation.studentsAssigned.map((student, studentIndex) => (
                        <div key={studentIndex} className="p-2 bg-gray-50 rounded border">
                          <div className="font-mono text-xs">{student.matricNumber}</div>
                          <div className="font-medium">{student.studentName}</div>
                          <div className="text-xs text-muted-foreground">
                            {student.departmentName} • {student.studyMode === 'full_time' ? 'FT' : 'PT'}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
