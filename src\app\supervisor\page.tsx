"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { TypographyH1, TypographyP } from "@/components/ui/typography";
import { Shield, Loader2 } from "lucide-react";

export default function SupervisorRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to main sign-in page
    router.push('/sign-in');
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center">
      <div className="text-center">
        <div className="bg-purple-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
          <Shield className="h-10 w-10 text-purple-600" />
        </div>
        <TypographyH1 className="text-2xl font-bold text-gray-900 mb-2">
          Redirecting to Sign In
        </TypographyH1>
        <TypographyP className="text-gray-600 mb-4">
          Please use the main sign-in page to access your supervisor portal
        </TypographyP>
        <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
          <Loader2 className="h-4 w-4 animate-spin" />
          Redirecting...
        </div>
      </div>
    </div>
  );
}
