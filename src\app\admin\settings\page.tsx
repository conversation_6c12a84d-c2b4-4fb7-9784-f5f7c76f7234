import { AdminLayout } from "@/components/admin/admin-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import { Settings, Key, Shield, Database, Bell, Palette } from "lucide-react";

export default function SystemSettingsPage() {
  return (
    <AdminLayout 
      title="System Settings" 
      description="Configure system preferences and security settings"
      badge="Configuration"
    >
      {/* Settings Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Default Passwords
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <TypographyP className="text-sm text-muted-foreground">
              Configure default passwords for different user types
            </TypographyP>
            <div className="space-y-2">
              <Badge variant="outline" className="font-mono w-full justify-start">
                Student: student123
              </Badge>
              <Badge variant="outline" className="font-mono w-full justify-start">
                Supervisor: supervisor123
              </Badge>
              <Badge variant="outline" className="font-mono w-full justify-start">
                Invigilator: invigilator123
              </Badge>
              <Badge variant="outline" className="font-mono w-full justify-start">
                Department: department123
              </Badge>
            </div>
            <Button size="sm" className="w-full">
              Update Passwords
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <TypographyP className="text-sm text-muted-foreground">
              Configure authentication and security policies
            </TypographyP>
            <div className="space-y-2">
              <Badge variant="outline" className="text-green-600">Biometric: Enabled</Badge>
              <Badge variant="outline" className="text-blue-600">2FA: Optional</Badge>
              <Badge variant="outline" className="text-orange-600">Session: 24h</Badge>
            </div>
            <Button size="sm" className="w-full" variant="outline">
              Security Settings
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              System Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <TypographyP className="text-sm text-muted-foreground">
              General system settings and preferences
            </TypographyP>
            <div className="space-y-2">
              <Badge variant="outline" className="text-purple-600">MAPOLY Mode</Badge>
              <Badge variant="outline" className="text-teal-600">Auto-backup</Badge>
              <Badge variant="outline" className="text-pink-600">Maintenance</Badge>
            </div>
            <Button size="sm" className="w-full" variant="outline">
              System Config
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Admin Credentials */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Administrator Credentials
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <TypographyP className="text-sm text-muted-foreground">
            Current administrator account settings and credentials
          </TypographyP>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Admin ID</label>
              <Badge variant="outline" className="font-mono w-full justify-start p-2">
                auto-admin-id-9aa745-mapoly-exam-hall-automata
              </Badge>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Password</label>
              <Badge variant="outline" className="font-mono w-full justify-start p-2">
                706_113
              </Badge>
            </div>
          </div>
          <div className="flex gap-2">
            <Button size="sm">
              Change Password
            </Button>
            <Button size="sm" variant="outline">
              Generate New ID
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              System Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Version</span>
              <Badge variant="outline">v1.0.0</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Database</span>
              <Badge variant="outline" className="text-green-600">Connected</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Firebase</span>
              <Badge variant="outline" className="text-blue-600">Active</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Institution</span>
              <Badge variant="outline" className="text-purple-600">MAPOLY</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Email Alerts</span>
              <Badge variant="outline" className="text-green-600">Enabled</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">SMS Notifications</span>
              <Badge variant="outline" className="text-orange-600">Disabled</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">System Alerts</span>
              <Badge variant="outline" className="text-blue-600">Enabled</Badge>
            </div>
            <Button size="sm" className="w-full mt-4">
              Configure Notifications
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Coming Soon */}
      <div className="mt-8 text-center">
        <Badge variant="outline" className="font-mono">
          🚧 Advanced settings interface coming soon...
        </Badge>
      </div>
    </AdminLayout>
  );
}
