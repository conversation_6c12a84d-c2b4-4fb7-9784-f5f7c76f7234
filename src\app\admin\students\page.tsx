"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { TypographyP } from "@/components/ui/typography";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { logStudentAdded } from "@/lib/firebase/activity-service";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Users,
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  GraduationCap,
  Building,
  Loader2,
  <PERSON>f<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  UserX,
  FileSpreadsheet
} from "lucide-react";
import { getAllStudents, getAllFaculties } from "@/lib/firebase/department-service";

interface Student {
  id: string;
  matricNumber: string;
  name: string;
  email: string;
  phoneNumber: string;
  gender: 'male' | 'female';
  studyMode: 'full_time' | 'part_time';
  status: 'active' | 'inactive' | 'suspended';
  facultyId: string;
  facultyName: string;
  departmentId: string;
  departmentName: string;
  levelId: string;
  levelName: string;
  hasSetupAccount: boolean;
  hasChangedPassword: boolean;
  defaultPassword: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFaculty, setSelectedFaculty] = useState<string>("all");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [selectedLevel, setSelectedLevel] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedStudyMode, setSelectedStudyMode] = useState<string>("all");
  const [selectedAccountSetup, setSelectedAccountSetup] = useState<string>("all");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showStudentDetails, setShowStudentDetails] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [faculties, setFaculties] = useState<Array<{name: string, code: string}>>([]);
  const [departments, setDepartments] = useState<Array<{name: string, code: string, facultyName: string}>>([]);
  const [isAddingStudent, setIsAddingStudent] = useState(false);
  const [addStudentData, setAddStudentData] = useState({
    matricNumber: "",
    name: "",
    departmentName: "",
    levelName: "",
    facultyName: "",
    email: "",
    phoneNumber: "",
    gender: "male" as "male" | "female",
    studyMode: "full_time" as "full_time" | "part_time"
  });

  const loadStudents = async () => {
    setIsLoading(true);
    try {
      console.log('📚 Loading students from Firestore...');

      // Get real students data from Firestore
      const studentsData = await getAllStudents();

      console.log(`✅ Loaded ${studentsData.length} students from Firestore`);

      setStudents(studentsData);
      setFilteredStudents(studentsData);
    } catch (error) {
      console.error('❌ Error loading students:', error);
      // Set empty arrays on error
      setStudents([]);
      setFilteredStudents([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStudents();
    loadFacultiesAndDepartments();
  }, []);

  const loadFacultiesAndDepartments = async () => {
    try {
      const { collection, getDocs } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      // Load faculties
      const facultiesRef = collection(db, 'faculties');
      const facultiesSnapshot = await getDocs(facultiesRef);
      const facultiesData = facultiesSnapshot.docs.map(doc => ({
        name: doc.data().name,
        code: doc.data().code
      }));
      setFaculties(facultiesData);

      // Load departments
      const departmentsRef = collection(db, 'departments');
      const departmentsSnapshot = await getDocs(departmentsRef);
      const departmentsData = departmentsSnapshot.docs.map(doc => ({
        name: doc.data().name,
        code: doc.data().code,
        facultyName: doc.data().facultyName
      }));
      setDepartments(departmentsData);

      console.log(`✅ Loaded ${facultiesData.length} faculties and ${departmentsData.length} departments`);
    } catch (error) {
      console.error('❌ Error loading faculties and departments:', error);
    }
  };

  // Filter students based on search and filters
  useEffect(() => {
    let filtered = students;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.matricNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Faculty filter
    if (selectedFaculty !== "all") {
      filtered = filtered.filter(student => student.facultyName === selectedFaculty);
    }

    // Department filter
    if (selectedDepartment !== "all") {
      filtered = filtered.filter(student => student.departmentName === selectedDepartment);
    }

    // Level filter
    if (selectedLevel !== "all") {
      filtered = filtered.filter(student => student.levelName === selectedLevel);
    }

    // Status filter
    if (selectedStatus !== "all") {
      filtered = filtered.filter(student => student.status === selectedStatus);
    }

    // Study mode filter
    if (selectedStudyMode !== "all") {
      filtered = filtered.filter(student => student.studyMode === selectedStudyMode);
    }

    // Account setup filter
    if (selectedAccountSetup !== "all") {
      if (selectedAccountSetup === "setup") {
        filtered = filtered.filter(student => student.hasSetupAccount);
      } else if (selectedAccountSetup === "not_setup") {
        filtered = filtered.filter(student => !student.hasSetupAccount);
      }
    }

    setFilteredStudents(filtered);
  }, [students, searchTerm, selectedFaculty, selectedDepartment, selectedLevel, selectedStatus, selectedStudyMode, selectedAccountSetup]);

  const getStatusBadge = (status: Student['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-600">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStudyModeBadge = (studyMode: Student['studyMode']) => {
    return studyMode === 'full_time' 
      ? <Badge variant="outline" className="text-blue-600 border-blue-600">Full Time</Badge>
      : <Badge variant="outline" className="text-purple-600 border-purple-600">Part Time</Badge>;
  };

  const exportStudents = (format: 'excel' | 'csv') => {
    console.log(`Exporting ${filteredStudents.length} students as ${format}`);
  };





  const handleAddStudent = async () => {
    if (!addStudentData.matricNumber || !addStudentData.name || !addStudentData.departmentName || !addStudentData.levelName) {
      alert('Please fill in all required fields');
      return;
    }

    setIsAddingStudent(true);
    try {
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/firebase');

      const studentData = {
        matricNumber: addStudentData.matricNumber.toUpperCase(),
        name: addStudentData.name,
        departmentName: addStudentData.departmentName,
        levelName: addStudentData.levelName,
        facultyName: addStudentData.facultyName || 'Unknown',
        email: addStudentData.email || '',
        phoneNumber: addStudentData.phoneNumber || '',
        gender: addStudentData.gender,
        studyMode: addStudentData.studyMode,
        status: 'active' as const,
        hasSetupAccount: false,
        hasChangedPassword: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: 'admin' // TODO: Get actual admin ID
      };

      const studentsRef = collection(db, 'students');
      const docRef = await addDoc(studentsRef, studentData);

      console.log('✅ Student added successfully:', docRef.id);

      // Log activity
      await logStudentAdded(addStudentData.name, addStudentData.matricNumber);

      // Reset form and close dialog
      setAddStudentData({
        matricNumber: "",
        name: "",
        departmentName: "",
        levelName: "",
        facultyName: "",
        email: "",
        phoneNumber: "",
        gender: "male",
        studyMode: "full_time"
      });
      setShowAddDialog(false);

      // Reload students list
      loadStudents();

    } catch (error) {
      console.error('❌ Error adding student:', error);
      alert('Failed to add student. Please try again.');
    } finally {
      setIsAddingStudent(false);
    }
  };

  const stats = {
    total: students.length,
    active: students.filter(s => s.status === 'active').length,
    setupAccount: students.filter(s => s.hasSetupAccount).length,
    changedPassword: students.filter(s => s.hasChangedPassword).length
  };

  return (
    <AdminLayout
      title="Students Management"
      description="Manage student accounts and information"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.total.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Registered students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Students</CardTitle>
            <UserCheck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.active.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? Math.round((stats.active / stats.total) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Account Setup</CardTitle>
            <Mail className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.setupAccount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? Math.round((stats.setupAccount / stats.total) * 100) : 0}% completed setup
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Password Changed</CardTitle>
            <UserCheck className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : stats.changedPassword.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.total > 0 ? Math.round((stats.changedPassword / stats.total) * 100) : 0}% changed default
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadStudents}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportStudents('excel')}
              >
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Export Excel
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportStudents('csv')}
              >
                <Download className="mr-2 h-4 w-4" />
                Export CSV
              </Button>
              <Button size="sm" onClick={() => setShowAddDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Student
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-8 gap-4">
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedFaculty} onValueChange={setSelectedFaculty}>
              <SelectTrigger>
                <SelectValue placeholder="Faculty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Faculties</SelectItem>
                {faculties.map((faculty) => (
                  <SelectItem key={faculty.code} value={faculty.name}>
                    {faculty.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
              <SelectTrigger>
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {departments
                  .filter(dept => selectedFaculty === "all" || dept.facultyName === selectedFaculty)
                  .map((department) => (
                    <SelectItem key={department.code} value={department.name}>
                      {department.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>

            <Select value={selectedLevel} onValueChange={setSelectedLevel}>
              <SelectTrigger>
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="ND1">ND1</SelectItem>
                <SelectItem value="ND2">ND2</SelectItem>
                <SelectItem value="HND1">HND1</SelectItem>
                <SelectItem value="HND2">HND2</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStudyMode} onValueChange={setSelectedStudyMode}>
              <SelectTrigger>
                <SelectValue placeholder="Study Mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Modes</SelectItem>
                <SelectItem value="full_time">Full Time</SelectItem>
                <SelectItem value="part_time">Part Time</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedAccountSetup} onValueChange={setSelectedAccountSetup}>
              <SelectTrigger>
                <SelectValue placeholder="Account Setup" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Students</SelectItem>
                <SelectItem value="setup">Setup Complete</SelectItem>
                <SelectItem value="not_setup">Setup Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
            <span>Showing {filteredStudents.length} of {students.length} students</span>
            {(searchTerm || selectedFaculty !== "all" || selectedDepartment !== "all" || selectedLevel !== "all" || selectedStatus !== "all" || selectedStudyMode !== "all" || selectedAccountSetup !== "all") && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedFaculty("all");
                  setSelectedDepartment("all");
                  setSelectedLevel("all");
                  setSelectedStatus("all");
                  setSelectedStudyMode("all");
                  setSelectedAccountSetup("all");
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Students List
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="h-10 w-10 bg-muted rounded-full animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-muted rounded w-1/4 animate-pulse" />
                    <div className="h-3 bg-muted rounded w-1/3 animate-pulse" />
                  </div>
                  <div className="h-6 bg-muted rounded w-16 animate-pulse" />
                </div>
              ))}
            </div>
          ) : filteredStudents.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <TypographyP className="text-muted-foreground">
                {searchTerm || selectedFaculty !== "all" || selectedDepartment !== "all" || selectedLevel !== "all" || selectedStatus !== "all"
                  ? "No students match your filters"
                  : "No students found"
                }
              </TypographyP>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Matric Number</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Study Mode</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Account</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStudents.slice(0, 20).map((student) => (
                    <TableRow
                      key={student.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        setSelectedStudent(student);
                        setShowStudentDetails(true);
                      }}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium">{student.name}</div>
                          <div className="text-sm text-muted-foreground">{student.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-mono">
                          {student.matricNumber}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{student.departmentName}</div>
                          <div className="text-sm text-muted-foreground">{student.facultyName}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{student.levelName}</Badge>
                      </TableCell>
                      <TableCell>
                        {getStudyModeBadge(student.studyMode)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(student.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {student.hasSetupAccount ? (
                            <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                              Setup ✓
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-orange-600 border-orange-600 text-xs">
                              Pending
                            </Badge>
                          )}
                          {student.hasChangedPassword ? (
                            <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                              Password ✓
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-gray-600 border-gray-600 text-xs">
                              Default
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Mail className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {filteredStudents.length > 20 && (
                <div className="mt-4 text-center">
                  <Button variant="outline">
                    Load More Students ({filteredStudents.length - 20} remaining)
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Student Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Student</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="matricNumber" className="text-right">
                Matric Number *
              </Label>
              <Input
                id="matricNumber"
                placeholder="CSC/2024/001"
                className="col-span-3"
                value={addStudentData.matricNumber}
                onChange={(e) => setAddStudentData({...addStudentData, matricNumber: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name *
              </Label>
              <Input
                id="name"
                placeholder="John Doe"
                className="col-span-3"
                value={addStudentData.name}
                onChange={(e) => setAddStudentData({...addStudentData, name: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="department" className="text-right">
                Department *
              </Label>
              <Select
                value={addStudentData.departmentName}
                onValueChange={(value) => setAddStudentData({...addStudentData, departmentName: value})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select Department" />
                </SelectTrigger>
                <SelectContent>
                  {departments
                    .filter(dept => !addStudentData.facultyName || dept.facultyName === addStudentData.facultyName)
                    .map((department) => (
                      <SelectItem key={department.code} value={department.name}>
                        {department.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="level" className="text-right">
                Level *
              </Label>
              <Input
                id="level"
                placeholder="ND1"
                className="col-span-3"
                value={addStudentData.levelName}
                onChange={(e) => setAddStudentData({...addStudentData, levelName: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="faculty" className="text-right">
                Faculty
              </Label>
              <Select
                value={addStudentData.facultyName}
                onValueChange={(value) => setAddStudentData({...addStudentData, facultyName: value, departmentName: ""})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select Faculty" />
                </SelectTrigger>
                <SelectContent>
                  {faculties.map((faculty) => (
                    <SelectItem key={faculty.code} value={faculty.name}>
                      {faculty.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="col-span-3"
                value={addStudentData.email}
                onChange={(e) => setAddStudentData({...addStudentData, email: e.target.value})}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowAddDialog(false)} disabled={isAddingStudent}>
              Cancel
            </Button>
            <Button onClick={handleAddStudent} disabled={isAddingStudent}>
              {isAddingStudent ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                'Add Student'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Student Details Dialog */}
      <Dialog open={showStudentDetails} onOpenChange={setShowStudentDetails}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Student Details</DialogTitle>
          </DialogHeader>
          {selectedStudent && (
            <div className="grid gap-6 py-4">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Name</Label>
                    <p className="text-sm font-medium">{selectedStudent.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Matric Number</Label>
                    <p className="text-sm font-medium">{selectedStudent.matricNumber}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Email</Label>
                    <p className="text-sm">{selectedStudent.email || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Phone</Label>
                    <p className="text-sm">{selectedStudent.phoneNumber || 'Not provided'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Gender</Label>
                    <p className="text-sm capitalize">{selectedStudent.gender}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Study Mode</Label>
                    <div>{getStudyModeBadge(selectedStudent.studyMode)}</div>
                  </div>
                </div>
              </div>

              {/* Academic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Academic Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Faculty</Label>
                    <p className="text-sm">{selectedStudent.facultyName}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Department</Label>
                    <p className="text-sm">{selectedStudent.departmentName}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Level</Label>
                    <p className="text-sm">{selectedStudent.levelName}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Status</Label>
                    <div>{getStatusBadge(selectedStudent.status)}</div>
                  </div>
                </div>
              </div>

              {/* Account Status */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Account Status</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Account Setup</Label>
                    <div className="flex items-center gap-2">
                      {selectedStudent.hasSetupAccount ? (
                        <Badge className="bg-green-600">Setup Complete</Badge>
                      ) : (
                        <Badge variant="outline" className="text-orange-600 border-orange-600">Setup Pending</Badge>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Password Changed</Label>
                    <div className="flex items-center gap-2">
                      {selectedStudent.hasChangedPassword ? (
                        <Badge className="bg-green-600">Changed</Badge>
                      ) : (
                        <Badge variant="outline" className="text-red-600 border-red-600">Default</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={() => setShowStudentDetails(false)}>
                  Close
                </Button>
                <Button onClick={() => {
                  // TODO: Implement edit student functionality
                  console.log('Edit student:', selectedStudent.id);
                }}>
                  Edit Student
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
